# -*- coding: utf-8 -*-

from odoo import models, fields


class AircallDetail(models.Model):
    _inherit = "aircall.details"

    helpdesk_ticket = fields.Many2one(
        string="Helpdesk Ticket",
        comodel_name="helpdesk.ticket"
    )

    def action_view_ticket(self):
        """View connected Ticket"""
        self.ensure_one()
        action = self.env["ir.actions.actions"]._for_xml_id(
            "helpdesk.helpdesk_ticket_action_main_tree"
        )
        form_view = [
            (self.env.ref("helpdesk.helpdesk_ticket_view_form").id, "form")
        ]
        if "views" in action:
            action["views"] = form_view + [
                (state, view)
                for state, view in action["views"]
                if view != "form"
            ]
        else:
            action["views"] = form_view
        action["res_id"] = self.helpdesk_ticket.id
        return action

    def create_helpdesk_ticket(self):
        """Create Helpdesk Ticket"""
        ticket = self.env['helpdesk.ticket'].create({
            'name': f"Created From Aircall Detail ID #{self.id}",
            'partner_id': self.customer_id.id
        })
        self.helpdesk_ticket = ticket
        # Redirect to new created ticket
        return self.action_view_ticket()
