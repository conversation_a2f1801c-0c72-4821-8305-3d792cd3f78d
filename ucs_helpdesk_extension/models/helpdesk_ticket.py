# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.osv import expression


class HelpdeskTicket(models.Model):
    _inherit = "helpdesk.ticket"

    product_count = fields.Integer(
        string="Product Count", compute="_compute_product_count"
    )
    aircall_detail_ids = fields.One2many(
        string="Call Details",
        comodel_name="aircall.details",
        inverse_name="helpdesk_ticket"
    )
    aircall_detail_count = fields.Integer(
        string="Air Call Count", compute="_compute_aircall_details"
    )

    @api.depends("aircall_detail_ids")
    def _compute_aircall_details(self):
        for record in self:
            record.aircall_detail_count = len(
                record.aircall_detail_ids
            )

    @api.depends("product_ids")
    def _compute_product_count(self):
        for record in self:
            record.product_count = len(record.product_ids)

    def action_view_product(self):
        self.ensure_one()
        action = self.env["ir.actions.actions"]._for_xml_id(
            "product.product_normal_action_sell"
        )
        if len(self.product_ids) > 1:
            action["domain"] = [("id", "in", self.product_ids.ids)]
        elif len(self.product_ids) == 1:
            form_view = [
                (self.env.ref("product.product_normal_form_view").id, "form")
            ]
            if "views" in action:
                action["views"] = form_view + [
                    (state, view)
                    for state, view in action["views"]
                    if view != "form"
                ]
            else:
                action["views"] = form_view
            action["res_id"] = self.product_ids.id
        else:
            action = {"type": "ir.actions.act_window_close"}
        return action

    def action_view_aircalls(self):
        self.ensure_one()
        action = self.env["ir.actions.actions"]._for_xml_id(
            "aircall_api_integration.action_aircall_detail"
        )
        if len(self.aircall_detail_ids) > 1:
            action["domain"] = [("id", "in", self.aircall_detail_ids.ids)]
        elif len(self.aircall_detail_ids) == 1:
            form_view = [
                (
                    self.env.ref(
                        "aircall_api_integration.aircall_detail_form_view"
                    ).id,
                    "form",
                )
            ]
            if "views" in action:
                action["views"] = form_view + [
                    (state, view)
                    for state, view in action["views"]
                    if view != "form"
                ]
            else:
                action["views"] = form_view
            action["res_id"] = self.aircall_detail_ids.id
        else:
            action = {"type": "ir.actions.act_window_close"}
        return action

    @api.model
    def _name_search(
            self, name, args=None, operator='ilike', limit=100, name_get_uid=None
    ):
        """
        Allow Search by ticket ref
        """
        if self.env.context.get('search_ticket_ref', False):
            args = expression.OR(
                [['|', ('name', operator, name), ('ticket_ref', 'ilike', name)], args]
            )
            return self._search(args, limit=limit, access_rights_uid=name_get_uid)
        return super()._name_search(name, args, operator, limit, name_get_uid)
