<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ucs_helpdesk_ticket_view_form" model="ir.ui.view">
        <field name='name'>ucs.helpdesk.ticket.view.form</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_product" type="object" class="oe_stat_button" icon="fa-cubes" attrs="{'invisible': [('product_count', '=', 0)]}">
                    <div class="o_stat_info">
                        <field name="product_count" class="o_stat_value" />
                        <span class="o_stat_text">Products</span>
                    </div>
                </button>
                <button name="action_view_aircalls" type="object" class="oe_stat_button" icon="fa-phone" attrs="{'invisible': [('aircall_detail_count', '=', 0)]}">
                    <div class="o_stat_info">
                        <field name="aircall_detail_count" class="o_stat_value" />
                        <span class="o_stat_text">Air Calls</span>
                    </div>
                </button>
            </xpath>

            <field name="category_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="tag_ids" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="product_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="task_ids" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="replied_date" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <xpath expr="//group//field[@name='ticket_type_id']" position="after">
                <field name="partner_id" position="move"/>
                <field name="person_name" position="move"/>
                <field name="partner_name" position="move"/>
                <xpath expr="//label[@for='partner_email']" position="move"/>
                <xpath expr="//field[@name='partner_email']/parent::div" position="move"/>
                <xpath expr="//label[@for='partner_phone']" position="move"/>
                <xpath expr="//field[@name='partner_phone']/parent::div" position="move"/>
                <field name="email_cc" position="move"/>
            </xpath>
            <xpath expr="//group//field[@name='company_id']" position="after">
                <field name="sh_purchase_order_ids" position="move"/>
            </xpath>
            <xpath expr="//group//field[@name='sh_due_date']" position="before">
                <field name="sh_ticket_alarm_ids" position="move"/>
            </xpath>
            <xpath expr="//field[@name='properties']/parent::div" position="after">
                <group string="Aircall Details">
                    <field name="aircall_detail_ids" no_label="1"/>
                </group>
            </xpath>
        </field>
    </record>
</odoo>