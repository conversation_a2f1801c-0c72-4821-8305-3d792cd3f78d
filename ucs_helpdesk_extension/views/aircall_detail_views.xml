<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ucs_aircall_detail_form_view" model="ir.ui.view">
        <field name='name'>aircall.detail.form.view</field>
        <field name="model">aircall.details</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="aircall_api_integration.aircall_detail_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet" position="before">
                <header>
                    <button name="create_helpdesk_ticket" string="Create Ticket" type="object" class="oe_highlight" attrs="{'invisible': [('helpdesk_ticket', '!=', False)]}"/>
                </header>
            </xpath>
            <xpath expr="//sheet/group" position="before">
                <div class="oe_button_box" name="button_box">
                    <button name="action_view_ticket" type="object" class="oe_stat_button" icon="fa-ticket" attrs="{'invisible': [('helpdesk_ticket', '=', False)]}">
                        <div class="o_stat_info">
                            <span class="o_stat_text">Tickets</span>
                        </div>
                    </button>
                </div>
            </xpath>
            <field name="notes" position="after">
                <field name="helpdesk_ticket" options="{'no_create': True}" context="{'search_ticket_ref': True}"/>
            </field>
        </field>
    </record>
</odoo>