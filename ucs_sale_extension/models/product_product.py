# -*- coding: utf-8 -*-

from odoo import models

class ProductProduct(models.Model):
    _inherit = "product.product"

    def name_get(self):
        """Update to display product name as [part no] product name in SOL"""
        model_in_use = self.env.context.get('params', {}).get('model')
        if model_in_use in ['sale.order.line', 'sale.order'] or self.env.context.get('from_order_line', False):
            res = []
            for product in self:
                display_name = (
                    f"[{product.default_code}] {product.name}"
                    if product.default_code else product.name
                )
                res.append((product.id, display_name))
            return res
        return super().name_get()