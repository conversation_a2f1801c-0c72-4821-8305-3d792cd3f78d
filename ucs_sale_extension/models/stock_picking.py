# -*- coding: utf-8 -*-

from odoo import models

class StockPicking(models.Model):
    _inherit = "stock.picking"

    def open_transfer_date_wizard(self):
        """Open wizard to update transfer date"""
        return {
            'name': 'Update Transfer Date',
            'type': 'ir.actions.act_window',
            'res_model': 'update.transfer.date.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_picking_id': self.id}
        }