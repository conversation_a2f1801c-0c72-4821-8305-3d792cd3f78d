# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import timedelta
import pytz
from pytz import timezone, UTC


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    promise_date = fields.Date(store=True, compute="_compute_sol_promise_date")

    @api.depends(
        "product_id",
        "product_uom_qty",
        "product_uom",
    )
    def _compute_sol_promise_date(self):
        """
        Compute Promise Date
        Logic:
        ------
        If company = Nascom
            If ADI Global Customer (open_so_sheet) then check available
            stock at ADI Global Location, For other customers check stock
            at default stock location.
            - If all lines have available stock:
                - PD = order date + 1 day
            - If any line does not have available stock:
                - PD = order date + most future date (sale_delay)
        Else
            For all customers check stock
            at default stock location.
            - If all lines have available stock:
                - PD = order date + 1 day
            - If any line does not have available stock:
                - PD = order date + most future date (sale_delay)
        """
        orders = self.order_id
        user_tz = self.env.context.get('tz') or self.env.user.tz
        user_pytz = timezone(user_tz) if user_tz else pytz.utc

        for order in orders:
            all_qty_available = True
            most_future_date = 0
            if order.partner_id.open_so_sheet and order.company_id.is_nascom:
                location_id = self.env["stock.location"].search(
                    [("adi_global_location", "=", True)], limit=1
                )
                if not location_id:
                    raise UserError(
                        _(
                            "Please select ADI Global Location "
                            "in Inventory > Configuration > Location"
                        )
                    )
            else:
                # take default stock location of company's warehouse
                company = (
                    order.company_id
                    and order.company_id.id
                    or self.env.company.id
                )
                location_id = (
                    self.env["stock.warehouse"]
                    .search([("company_id", "=", company)], limit=1)
                    .lot_stock_id
                )

            for line in order.order_line.filtered(
                lambda ol: ol.product_id
                and ol.product_uom
                and ol.order_id.date_order
                and (not ol.is_delivery or (ol.is_delivery and ol.order_id.website_id))
            ):
                # converty qty to standard UOM
                standard_qty = line.product_id.uom_id._compute_quantity(
                    line.product_uom_qty, line.product_uom
                )
                available_qty = self.env[
                    "stock.quant"
                ]._get_available_quantity(line.product_id, location_id)
                if available_qty < standard_qty:
                    all_qty_available = False

                most_future_date = (
                    line.product_id.sale_delay
                    if line.product_id.sale_delay > most_future_date
                    else most_future_date
                )

            # order_date = order.date_order.astimezone(user_pytz)
            order_date = fields.Date.today()

            if all_qty_available:
                promise_date = order_date + timedelta(days=1)
            else:
                promise_date = order_date + timedelta(
                    days=most_future_date
                )
            order.order_line.filtered(
                lambda ol: not ol.is_delivery or (ol.is_delivery and ol.order_id.website_id)
            ).promise_date = promise_date

    def _get_sale_order_line_multiline_description_sale(self):
        """Override to display only [default code] name"""
        name = ""
        if self.product_id:
            name += self.product_id.nascom_description_sale
        return name

    def _get_sale_order_line_multiline_description_variants(self):
        """Override to display only [default code] name"""
        name = ""
        if self.product_id:
            name += self.product_id.nascom_description_sale
        return name


class DeliveryCarrier(models.Model):
    _inherit = 'delivery.carrier'

    is_free_shipping = fields.Boolean(string="Is Free Shipping", default=False, copy=False, tracking=True)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _prepare_delivery_line_vals(self, carrier, price_unit):
        values = super(SaleOrder, self)._prepare_delivery_line_vals(carrier, price_unit)
        if carrier.is_free_shipping:
            values['discount'] = 100
        return values