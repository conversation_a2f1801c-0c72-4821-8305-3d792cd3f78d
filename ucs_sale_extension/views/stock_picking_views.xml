<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="view_picking_form_inherit_transfer_date" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit.transfer.date</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <header position="inside">
                <button name="open_transfer_date_wizard"
                        string="Update Transfer Date"
                        type="object"
                        class="btn-primary"
                        attrs="{'invisible': [('state', '!=', 'done')]}"/>
            </header>
        </field>
    </record>
</odoo>