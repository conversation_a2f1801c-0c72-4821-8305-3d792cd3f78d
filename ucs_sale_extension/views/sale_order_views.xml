<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ucs_bv_nascom_custom_sales_order_form_view" model="ir.ui.view">
        <field name='name'>ucs.bv.nascom.custom.sales.order.form.view</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="bv_nascom_custom.bv_nascom_custom_sales_order_form_view"/>
        <field name="priority" eval="50"/>
        <field name="arch" type="xml">
            <field name="so_email_to_partner_id" position="attributes">
                <attribute name="domain">[('parent_id', '=', partner_id), ('type', '=', 'contact')]</attribute>
            </field>
            <field name="invoice_email_to_partner_id" position="attributes">
                <attribute name="domain">[('parent_id', '=', partner_id), ('type', '=', 'invoice')]</attribute>
            </field>
        </field>
    </record>
</odoo>