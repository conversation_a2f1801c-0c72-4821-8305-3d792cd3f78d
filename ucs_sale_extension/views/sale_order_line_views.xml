<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ucs_view_order_form" model="ir.ui.view">
        <field name='name'>ucs.view.order.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="priority" eval="50"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/tree//field[@name='product_id']" position="attributes">
                <attribute name="context">{'from_order_line': True,'partner_id': parent.partner_id,'quantity':
                    product_uom_qty,'pricelist': parent.pricelist_id,'uom':product_uom,'company_id':
                    parent.company_id,'default_lst_price': price_unit,'default_description_sale': name}
                </attribute>
            </xpath>
        </field>
    </record>

    <record id="ucs_view_delivery_carrier_form" model="ir.ui.view">
        <field name='name'>ucs.view.delivery.carrier.form</field>
        <field name="model">delivery.carrier</field>
        <field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='is_my_shipping']" position="after">
                <field name="is_free_shipping"/>
            </xpath>
        </field>
    </record>
</odoo>