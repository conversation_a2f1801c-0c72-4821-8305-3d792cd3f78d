# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import UserError

class UpdateTransferDateWizard(models.TransientModel):
    _name = 'update.transfer.date.wizard'
    _description = 'Update Transfer Date'

    picking_id = fields.Many2one(comodel_name='stock.picking', string="Transfer", required=True)
    transfer_date = fields.Datetime(string="Transfer Date", required=True)

    def update_transfer_date(self):
        if not self.transfer_date:
            raise UserError("Please enter a valid transfer date.")
        self.picking_id.date_done = self.transfer_date
        self.picking_id.move_ids.date = self.transfer_date
        return {'type': 'ir.actions.act_window_close'}