# -*- coding: utf-8 -*-

from odoo import models, fields, api, Command


class ResPartner(models.Model):
    _inherit = "res.partner"

    stripe_customer = fields.Many2one(
        string="Stripe Customer", comodel_name="stripe.customer", tracking=True
    )

    _sql_constraints = [
        # ('stripe_customer_uniq', 'unique (stripe_customer)',
        #  """Connected Stripe Customer should be unique
        #  for each Odoo Partner!"""),
    ]

    def action_import_stripe_payment_methods(self):
        """
        Import Stripe Payment Methods
        """
        return (
            self.env["import.stripe.payment.method"]
            .create(
                {
                    "action": "selected",
                    "stripe_customers": [
                        Command.link(self.stripe_customer.id)
                    ],
                }
            )
            .import_payment_method()
        )

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        if not self.env.context.get("from_stripe_customer", False):
            for rec in res:
                if rec.stripe_customer:
                    rec.stripe_customer.partner_id = rec.id
        return res

    def write(self, vals):
        stripe_customer = self.stripe_customer
        res = super().write(vals)
        if vals.get("stripe_customer") and not self.env.context.get(
            "from_stripe_customer", False
        ):
            if not self.stripe_customer:
                stripe_customer.with_context(from_odoo_partner=True).write(
                    {"partner_id": False}
                )
            else:
                # Update old to False as it is not connected anymore
                stripe_customer.with_context(from_odoo_partner=True).write(
                    {"partner_id": False}
                )
                self.stripe_customer.with_context(
                    from_odoo_partner=True
                ).write({"partner_id": self.id})
        return res
