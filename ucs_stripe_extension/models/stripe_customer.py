# -*- coding: utf-8 -*-

from odoo import models, fields, api, Command


class StripeCustomer(models.Model):
    _name = "stripe.customer"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Stripe Customer"

    stripe_id = fields.Char(string="Stripe ID", copy=False, tracking=True)
    name = fields.Char(string="Name", copy=False, tracking=True)
    phone = fields.Char(string="Phone", copy=False, tracking=True)
    email = fields.Char(string="Email", copy=False, tracking=True)
    stripe_data = fields.Text(string="Stripe Data", copy=False, tracking=True)
    source_default = fields.Char(
        string="Default Source", copy=False, tracking=True
    )
    partner_id = fields.Many2one(
        string="Odoo Partner",
        comodel_name="res.partner",
        copy=False,
        tracking=True,
    )

    _sql_constraints = [
        (
            "stripe_id_uniq",
            "unique (stripe_id)",
            """Stripe ID should be unique!""",
        ),
        # ('partner_id_uniq', 'unique (partner_id)',
        #  """Connected Odoo Partner should be unique for
        #  each Stripe Customer!"""),
    ]

    def action_import_stripe_payment_methods(self):
        """
        Import Stripe Payment Methods
        """
        return (
            self.env["import.stripe.payment.method"]
            .create(
                {
                    "action": "selected",
                    "stripe_customers": [Command.link(self.id)],
                }
            )
            .import_payment_method()
        )

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        if not self.env.context.get("from_odoo_partner", False):
            for rec in res:
                if rec.partner_id:
                    rec.partner_id.stripe_customer = rec.id
        return res

    def write(self, vals):
        partner_id = self.partner_id
        res = super().write(vals)
        if vals.get("partner_id") and not self.env.context.get(
            "from_odoo_partner", False
        ):
            if not self.partner_id:
                partner_id.with_context(from_stripe_customer=True).write(
                    {"stripe_customer": False}
                )
            else:
                # Update old to False as it is not connected anymore
                partner_id.with_context(from_stripe_customer=True).write(
                    {"stripe_customer": False}
                )
                self.partner_id.with_context(from_stripe_customer=True).write(
                    {"stripe_customer": self.id}
                )
        return res
