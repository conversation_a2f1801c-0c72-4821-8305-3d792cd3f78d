<odoo>
    <!--Wizard for Import Payment Method.-->
    <record id="view_import_stripe_payment_method_form" model="ir.ui.view">
        <field name="name">view.import.stripe.payment.method.form</field>
        <field name="model">import.stripe.payment.method</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <form string="Import Stripe Payment Method">
                <sheet>
                    <group>
                        <group>
                            <field name='action' required="1"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('action', '!=', 'selected')]}">
                        <field name='stripe_customers' widget="many2many_tags" options="{'no_create_edit': True}" attrs="{'required': [('action', '=', 'selected')]}"/>
                    </group>
                    <footer>
                        <button string="Import" class="oe_highlight"
                                type="object" name="import_payment_method"/>
                        <button string="Cancel" class="oe_highlight"
                                special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_import_stripe_payment_method" model="ir.actions.act_window">
        <field name="name">Import Stripe Payment Method</field>
        <field name="res_model">import.stripe.payment.method</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
    <menuitem name="Import Stripe Payment Method" action="action_import_stripe_payment_method" id="menu_action_import_stripe_payment_method" parent="account.menu_finance_receivables" sequence="125"/>
</odoo>