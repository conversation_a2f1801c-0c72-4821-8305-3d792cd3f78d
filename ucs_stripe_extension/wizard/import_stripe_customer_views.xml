<odoo>
    <!--Wizard for Import Customer.-->
    <record id="view_import_stripe_customer_form" model="ir.ui.view">
        <field name="name">view.import.stripe.customer.form</field>
        <field name="model">import.stripe.customer</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <form string="Import Stripe Customer">
                <sheet>
                    <group>
                        <group>
                            <field name='action' required="1"/>
                        </group>
                        <group attrs="{'invisible': [('action', '!=', 'after_date')]}">
                            <field name='after_date' attrs="{'required': [('action', '=', 'after_date')]}"/>
                        </group>
                    </group>
                    <footer>
                        <button string="Import" class="oe_highlight"
                                type="object" name="import_customers"/>
                        <button string="Cancel" class="oe_highlight"
                                special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_import_stripe_customer" model="ir.actions.act_window">
        <field name="name">Import Stripe Customer</field>
        <field name="res_model">import.stripe.customer</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
    <menuitem name="Import Stripe Customer" action="action_import_stripe_customer" id="menu_action_import_stripe_customer" parent="account.menu_finance_receivables" sequence="120"/>
</odoo>