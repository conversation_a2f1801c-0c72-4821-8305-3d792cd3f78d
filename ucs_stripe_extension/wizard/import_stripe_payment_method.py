# -*- coding: utf-8 -*-

from odoo import fields, models
import logging
import requests

_logger = logging.getLogger(__name__)


class ImportStripePaymentMethod(models.TransientModel):
    _name = "import.stripe.payment.method"
    _description = "Import Stripe Payment Method"

    action = fields.Selection(
        selection=[("all", "All"), ("selected", "Selected Customers")],
        string="Import",
        required=True,
        default="all",
    )
    stripe_customers = fields.Many2many(
        comodel_name="stripe.customer",
        string="Stripe Customers",
        domain="[('partner_id', '!=', False)]",
    )

    def get_stripe_url(self):
        return "https://api.stripe.com/v1"

    def import_payment_method(self):
        """
        This method used to import stripe payment token.
        """
        self = self.sudo()
        stripe_api_key = self.env.ref(
            "payment.payment_provider_stripe"
        ).stripe_secret_key
        stripe_url = self.get_stripe_url()
        if self.action == "selected":
            customers = self.stripe_customers
        else:
            customers = self.env["stripe.customer"].search(
                [("partner_id", "!=", False)]
            )
        curser_id = False
        for customer in customers:
            try:
                customers_url = (
                    f"{stripe_url}/payment_methods?customer="
                    f"{customer.stripe_id}&type=card&limit=100"
                )
                headers = {"Authorization": f"Bearer {stripe_api_key}"}
                response = requests.get(customers_url, headers=headers)
                response_data = response.json()
                if "error" in response_data:
                    raise Exception(response_data["error"])
                payment_methods = response_data.get("data")
                for method in payment_methods:
                    curser_id = method["id"]
                    month = fields.Datetime.today().month
                    year = fields.Datetime.today().year
                    if year < method.get("card").get("exp_year"):
                        if year == method.get("card").get("exp_year") and (
                            method.get("card").get("exp_month") < month
                        ):
                            continue
                        self._create_payment_token(method, customer)
                    self.env.cr.commit()
                while response_data.get("has_more"):
                    customers_url = (
                        f"{stripe_url}/payment_methods?customer="
                        f"{customer.stripe_id}&type=card&limit=100"
                        f"&starting_after={curser_id}"
                    )
                    response = requests.get(customers_url, headers=headers)
                    response_data = response.json()
                    if "error" in response_data:
                        raise Exception(response_data["error"])
                    payment_methods = response_data.get("data")
                    for method in payment_methods:
                        curser_id = method["id"]
                        month = fields.Datetime.today().month
                        year = fields.Datetime.today().year
                        if year < method.get("card").get("exp_year"):
                            if year == method.get("card").get("exp_year") and (
                                method.get("card").get("exp_month") < month
                            ):
                                continue
                            self._create_payment_token(method, customer)
                        self.env.cr.commit()

            except Exception as e:
                raise Exception(
                    "Error importing payment method (Message: %s)" % e
                )

        action = self.env["ir.actions.actions"]._for_xml_id(
            "payment.action_payment_token"
        )
        action["domain"] = [("partner_id", "in", customers.partner_id.ids)]
        return action

    def _create_payment_token(self, method, customer):
        exp_date = (
            f"{method.get('card').get('exp_month')}/"
            f"{method.get('card').get('exp_year')}"
        )
        token = self.env["payment.token"].search(
            [
                ("provider_ref", "=", customer.stripe_id),
                (
                    "provider_id",
                    "=",
                    self.env.ref("payment.payment_provider_stripe").id,
                ),
                ("payment_details", "=", method.get("card", {}).get("last4")),
            ],
            limit=1,
        )
        if not token:
            token = self.env["payment.token"].create(
                {
                    "provider_id": self.env.ref(
                        "payment.payment_provider_stripe"
                    ).id,
                    "payment_details": method.get("card", {}).get("last4"),
                    "partner_id": customer.partner_id.id,
                    "provider_ref": customer.stripe_id,
                    "exp_date": exp_date,
                }
            )

        token_data = {}
        if customer.source_default and (
            method.get("id") == customer.source_default
        ):
            token_data.update({"card_default_payment": True})

        if not token.exp_date:
            token_data.update({"exp_date": exp_date})

        if token_data:
            token.write(token_data)
