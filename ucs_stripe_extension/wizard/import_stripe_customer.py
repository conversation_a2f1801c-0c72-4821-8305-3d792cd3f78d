# -*- coding: utf-8 -*-

from odoo import fields, models
import logging
import requests
import re

_logger = logging.getLogger(__name__)


class ImportStripeCustomer(models.TransientModel):
    _name = "import.stripe.customer"
    _description = "Import Stripe Customer"

    action = fields.Selection(
        selection=[("all", "All"), ("after_date", "Created After")],
        string="Import",
        required=True,
        default="all",
    )
    after_date = fields.Datetime(string="Created After")

    def get_stripe_url(self):
        return "https://api.stripe.com/v1"

    def import_customers(self):
        """
        This method used to import stripe customers.
        """
        self = self.sudo()
        stripe_api_key = self.env.ref(
            "payment.payment_provider_stripe"
        ).stripe_secret_key
        curser_id = False
        stripe_url = self.get_stripe_url()
        try:
            customers_url = f"{stripe_url}/customers?limit=100"
            if self.action == "after_date":
                after_date = int(self.after_date.timestamp())
                customers_url = (
                    f"{stripe_url}"
                    f"/customers?limit=100&created[gte]={after_date}"
                )
            headers = {"Authorization": f"Bearer {stripe_api_key}"}
            response = requests.get(customers_url, headers=headers)
            response_data = response.json()
            if "error" in response_data:
                raise Exception(response_data["error"])

            for customer in response_data["data"]:
                self._create_customer(customer)
                curser_id = customer["id"]
            self.env.cr.commit()
            while response_data.get("has_more"):
                next_page_url = (
                    f"{stripe_url}"
                    f"/customers?limit=100&starting_after={curser_id}"
                )
                response = requests.get(next_page_url, headers=headers)
                response_data = response.json()
                if "error" in response_data:
                    raise Exception(response_data["error"])
                for customer in response_data["data"]:
                    self._create_customer(customer)
                    curser_id = customer["id"]
                self.env.cr.commit()
        except Exception as e:
            raise Exception("Error importing Customer (Message: %s)" % e)

        return self.env["ir.actions.actions"]._for_xml_id(
            "ucs_stripe_extension.action_stripe_customer"
        )

    def extract_id(self, text):
        if not isinstance(text, str) or not text.strip():
            return False  # Return None if the input is not a valid string

        # Define a regex pattern to find the ID
        pattern = r"id:\s*(\d+)"

        # Search for the pattern in the input text
        match = re.search(pattern, text)

        # Check if a match was found and return the ID
        if match:
            # check for available contact
            partner = self.env["res.partner"].search(
                [("id", "=", int(match.group(1)))]
            )
            return partner or False
        else:
            return False

    def _create_customer(self, customer):
        stripe_customer = self.env["stripe.customer"].search(
            [("stripe_id", "=", customer["id"])]
        )
        if not stripe_customer:
            odoo_contact = self.extract_id(customer.get("description"))
            customer_data = {
                "name": customer["name"],
                "email": customer["email"],
                "phone": customer["phone"],
                "stripe_data": customer,
                "stripe_id": customer["id"],
                "source_default": customer.get("default_source"),
                "partner_id": odoo_contact and odoo_contact.id or False,
            }
            self.env["stripe.customer"].create(customer_data)
            # if odoo_contact:
            #     odoo_contact.stripe_customer = stripe_customer.id
