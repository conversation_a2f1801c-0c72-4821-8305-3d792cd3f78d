<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ucs_stripe_customer_view_filter" model="ir.ui.view">
        <field name='name'>ucs.stripe.customer.view.filter</field>
        <field name="model">stripe.customer</field>
        <field name="arch" type="xml">
            <search>
                <filter name="odoo_partner_set" string="Odoo Partner Set" domain="[('partner_id', '!=', False)]"/>
                <filter name="odoo_partner_not_set" string="Odoo Partner Not Set" domain="[('partner_id', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="ucs_stripe_customer_view_tree" model="ir.ui.view">
        <field name='name'>ucs.stripe.customer.view.tree</field>
        <field name="model">stripe.customer</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="stripe_id"/>
                <field name="partner_id"/>
                <field name="email"/>
                <field name="phone"/>
            </tree>
        </field>
    </record>

    <record id="ucs_stripe_customer_view_form" model="ir.ui.view">
        <field name='name'>ucs.stripe.customer.view.form</field>
        <field name="model">stripe.customer</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div name="button_box" class="oe_button_box">
                        <button string="Import Stripe Payment Method" class="oe_stat_button" type="object" name="action_import_stripe_payment_methods" attrs="{'invisible': [('partner_id', '=', False)]}" icon="fa-cloud-download">
                        </button>
                    </div>
                    <h1><field placeholder="e.g. John Doe" name="name" required="1"/></h1>
                    <group>
                        <field name="stripe_id" required="1"/>
                        <field name="partner_id" options='{"no_create": True}'/>
                        <field name="email"/>
                        <field name="phone"/>
                        <field name="source_default"/>
                    </group>
                    <group>
                        <field name="stripe_data"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids" widget="mail_activity"/>
                </div>
            </form>
        </field>
    </record>

    <record id="action_stripe_customer" model="ir.actions.act_window">
        <field name="name">Stripe Customer</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">stripe.customer</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem name="Stripe Customer" action="action_stripe_customer" id="menu_action_stripe_customer" parent="account.menu_finance_receivables" sequence="115"/>
</odoo>