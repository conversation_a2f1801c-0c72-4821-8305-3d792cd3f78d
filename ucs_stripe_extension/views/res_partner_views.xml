<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ucs_stripe_view_res_partner_filter" model="ir.ui.view">
        <field name='name'>ucs.stripe.view.res.partner.filter</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <filter name="type_company" position="after">
                <separator/>
                <filter name="stripe_customer" string="Stripe Customer" domain="[('stripe_customer', '!=', False)]"/>
            </filter>
        </field>
    </record>

    <record id="ucs_stripe_view_partner_form" model="ir.ui.view">
        <field name='name'>ucs.stripe.view.partner.form</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button string="Import Stripe Payment Method" class="oe_stat_button" type="object" name="action_import_stripe_payment_methods" attrs="{'invisible': [('stripe_customer', '=', False)]}" icon="fa-cloud-download">
                </button>
            </div>
            <field name="category_id" position="after">
                <field name="stripe_customer" options='{"no_create": True}'/>
            </field>
        </field>
    </record>
</odoo>