<odoo>
    <record id="stock_barcode_picking_client_action" model="ir.actions.client">
        <field name="name">Barcode Picking Client Action</field>
        <field name="tag">stock_barcode_client_action</field>
        <field name="res_model">stock.picking</field>
    </record>

    <record id="stock_barcode_inventory_client_action" model="ir.actions.client">
        <field name="name">Barcode Inventory Client Action</field>
        <field name="tag">stock_barcode_client_action</field>
        <field name="res_model">stock.quant</field>
    </record>

    <data noupdate="1">
        <!-- Set `restrict_scan_source_location` on True for outgoing picking types. -->
        <function model="stock.picking.type" name="write">
            <value model="stock.picking.type" search="[('code', '=', 'outgoing')]"/>
            <value eval="{
                'restrict_scan_source_location': 'mandatory',
                'restrict_scan_dest_location': 'no',
            }"/>
        </function>
    </data>
</odoo>
