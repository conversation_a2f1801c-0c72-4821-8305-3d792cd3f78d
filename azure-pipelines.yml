# Pipeline to automatically mirror
# an Azure DevOps repository in GITHUB  
# Trigger on all branches
trigger:
- '*'
# Use latest Ubuntu image
pool:
  vmImage: 'ubuntu-latest'
# Pipeline
steps:
- checkout: none
- script: |
      # Install urlencode function to encode reserved characters in passwords
      sudo apt-get install gridsite-clients

      # Create local mirror of Azure DevOps repository
      git clone --mirror https://${AZURE_GIT_USERNAME}:$(urlencode ${AZURE_GIT_PASSWORD})@${AZURE_REPO_URL} repo-mirror
      
      # Sync GITHUB repository
      cd repo-mirror
      git push --mirror https://${GITHUB_GIT_USERNAME}:$(urlencode ${GITHUB_GIT_PASSWORD})@${GITHUB_REPO_URL}
  displayName: 'Sync repository with GITHUB'
  env:
    AZURE_REPO_URL: $(AZURE_REPO_URL)
    AZURE_GIT_USERNAME: $(AZURE_GIT_USERNAME)
    AZURE_GIT_PASSWORD: $(AZURE_GIT_PASSWORD)
    GITHUB_REPO_URL: $(GITHUB_REPO_URL)
    GITHUB_GIT_USERNAME: $(GITHUB_GIT_USERNAME)
    GITHUB_GIT_PASSWORD: $(GITHUB_GIT_PASSWORD)
