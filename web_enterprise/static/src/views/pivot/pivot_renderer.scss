@include media-breakpoint-down(md) {
    .o_pivot {
        height: 100%;

        .dropdown.show {
            > .dropdown-toggle::after {
                @include o-caret-down;
            }
        }

        th > .o_group_by_menu  > .dropdown-menu {
            .dropdown-item {
                // caret centered vertically
                .dropdown-toggle::after{
                    top: 12px;
                }

                // nested dropdown should be *under* the parent, not on its side
                .dropdown-menu {
                    top: initial !important;
                    left: 5% !important;
                    width: 95%;
                }
            }
        }
    }
}
