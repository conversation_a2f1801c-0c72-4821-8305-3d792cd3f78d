# psus-edi

EDI Repo

# Current Documents

## SPS Commerce

### Inbound

- 850 (Sale Order)
- 856 (Shipping Notice)

### Outbound

- 810 (Invoice)
- 846 (Inventory Inquiry/Advice)
- 855 (Sale Order Acknowledgment)
- 856 (Advance Ship Notice)

# Setup

To start configuring, start by installing the modules for the documents you wish to import/export. Next make sure you are in debug mode
and go to `Settings > Technical > EDI > Synchronizations`, and create a new record. Fill in the url, port, username and password provided by
SPS Commerce. If testing locally, you can use [FileZilla Server](https://filezilla-project.org/download.php?type=server)
to host the files used for testing (***NOTE***: SFTP is not supported on the free version, so you will need to use FTP). Then create the
synchronization actions for the documents you wish to sync. This will create a synchronization mapping in `Settings > Technical > EDI > Mappings`.
go to the Default Mapping for the document, and click the button `CONFIGURE`. This will create the base configuration for the document.

# Development

## Mapping

Mappings follow the XSD provided by SPS Commerce. Configuration can be done manually
through the web app, or can be configured through JSON. When configuring through JSON, the
structure for the JSON is an object with an optional <code>partner</code> key and a mandatory <code>config</code>
key. If <code>partner</code> is provided, the configuration in that object is specifically for a partner with the
trading partner id provided. <code>config</code> is a list of objects, each object includes a <code>path</code>, specifying
the line that is being configured. The rest of the object is determined by whether the mapping is
an import or export.

### Import

- <code>model</code> - sets the line as the basis of a model, it is primarily used to set the top level
  model of a document.
- <code>create</code> - if set, will try to create new records in the database
- <code>field</code> - associate the field with the line. Normally, the value is simply stored in the field
  after converting to the appropriate python type.
  - <code>search_field</code> - Used on relational fields when the value should be used to search for existing records.
- <code>mutlifield</code> - when a tag in the document may map to multiple fields, this key provides
  an object describing which fields should be set based on sentinel values
  - <code>determining_tag</code> - the descendant tag that determines which field should be set
  - <code>model</code> - the Odoo model that each field relates to.
  - <code>search</code> - a boolean defining whether all fields should be searched
  - <code>create</code> - a boolean defining whether all fields can create new records, if search
    is set, will only create a new record if one is not found
  - <code>values</code> - a list of objects configuring the fields to be set
    - <code>value</code> - the value associated with the field to be set
    - <code>field</code> - the field to be set
    - <code>search</code> - if search is not set for all the fields, this can configure a single field to search
    - <code>create</code> - if create is not set for all the fields, this can configure a single field to create new records,
      if search is set, will only create a new record if one is not found
- <code>search</code> - if the closest ancestor tag with either a field or multifield tag is to a relational
  field, this indicates the value should be included in the search
- <code>log_subfields</code> - provides the name of a text field that stores a string containing
  subfields, multiple instances of the tag occur, then each will be stored on it's own line○
  - <code>log</code> - used on descendant tags to log the tag
    - <code>label</code> - change the default label from the tag to a provided day
    - <code>log_map</code> - a list of lists providing a map from EDI short codes to human
      readable values to include in the log. The first value is the short code, and
      the second value is the human readable value. If a list with an empty
      string as the first value is passed, it will be treated as the default value if
      none of the other pairs match
- <code>skip</code> - if set, skips the record if the value matches an existing record
  - <code>skip_overrides</code> - a list of objects defining the conditions in which the skip condition should be ignored
    - <code>path</code> - the tag providing the value for comparison
    - <code>comparison</code> - whether to check for equality or inequality
    - <code>value</code> - value that signals the should be checked
- <code>custom_method</code> - the name of a custom method to call
  - <code>custom_method_returns_dict</code> - if true, the custom method will return a dictionary
    with odoo fields as keys and the corresponding value
  - <code>custom_method_args</code> - a list of paths from the element to additional
    arguments that should be passed into the custom method. The additional
    arguments are passed in as a dictionary with keys being the tag and values being
    the lxml node

### Export

- <code>model</code> - sets the line as the basis of a model, it is primarily used to set the top level model of a document.
- <code>field</code> - associate the field with the line. Normally, the value is simply stored in the field
  after converting to the appropriate python type.
- <code>multimap</code> - Used when a tag should pull data from multiple fields, this provides an
  object describing which fields should be set based on sentinel values
  - <code>determining_tag</code> - The tag that determines the field being exported
  - <code>model</code> - The model of the fields to be exported
  - <code>values</code> - a list of objects mapping of values and odoo fields
    - <code>value</code> - The value that should be exported when the field is exported
    - <code>field</code> - The field to export
- <code>action</code> - the external name of a server action to execute, the action should set a variable <code>edi_value</code>
- <code>static</code> - A static value to export

## Hooking Up The Mapping

To hook up the `CONFIGURE` button to automatically include your configuration file, extend the `edi.mapping` model, and override the
method `configure`, calling `_configure_import_from_json_file`. 
```python
from os.path import join as join_path

from odoo import models
from odoo.modules import get_module_path


class EdiMapping(models.Model):
    _inherit = 'edi.mapping'
    
    def configure(self):
        super(EdiMapping, self).configure()
        mappings = self.filtered_domain([('sync_action_doc_type', '=', 'export_invoice_xml')])
        module_path = get_module_path('edi_export_account_customization_module')
        for mapping in mappings:
            mapping._configure_import_from_json_file(join_path(module_path, 'data/Custom810.json'))
```
