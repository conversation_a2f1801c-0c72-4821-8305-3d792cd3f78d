<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="product_template_public" model="ir.rule">
        <field name="name">Public product template</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="domain_force">[('website_published', '=', True), ("sale_ok", "=", True)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="group_show_uom_price" model="res.groups">
        <field name="name">UOM Price Display for eCommerce</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_product_price_comparison" model="res.groups">
        <field name="name">Comparison Price</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="sales_team.group_sale_manager" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('website.group_website_restricted_editor'))]"/>
    </record>

    <record id="base.group_user" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('account.group_delivery_invoice_address'))]"/>
    </record>

    <record id="base.group_public" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('account.group_delivery_invoice_address'))]"/>
    </record>

    <record id="base.group_portal" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('account.group_delivery_invoice_address'))]"/>
    </record>

    <!--
        Multi-company/Multi-website compliant:
        We can't add a condition on domain_force without losing `product`
        ir.rule domain_force. It is better to disabled them to be able to
        reenable them on `website_sale` uninstall.
        Don't override domain_force or we will need to hardcode the original
        domain in `uninstall_hook` rather than just reenabling records.
    -->
    <record id="product.product_pricelist_comp_rule" model="ir.rule">
        <field name="active" eval="False"/>
    </record>
    <record id="product.product_pricelist_item_comp_rule" model="ir.rule">
        <field name="active" eval="False"/>
    </record>
    <record id="product_pricelist_comp_rule" model="ir.rule">
        <field name="name">product pricelist company rule</field>
        <field name="model_id" ref="product.model_product_pricelist"/>
        <field name="domain_force">['|', ('company_id', 'in', [False,website.company_id.id]), ('company_id', 'in', company_ids)]</field>
    </record>
    <record id="product_pricelist_item_comp_rule" model="ir.rule">
        <field name="name">product pricelist item company rule</field>
        <field name="model_id" ref="product.model_product_pricelist_item"/>
        <field name="domain_force">['|', ('company_id', 'in', [False,website.company_id.id]), ('company_id', 'in', company_ids)]</field>
    </record>

</odoo>
