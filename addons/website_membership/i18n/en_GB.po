# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_membership
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-08 10:38+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "All"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:88
#, python-format
msgid "All Countries"
msgstr "All Countries"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Associations"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Find a business partner"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:130
#, python-format
msgid "Free Members"
msgstr "Free Members"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Location"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.footer_custom
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Members"
msgstr "Members"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "No result found."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Our Members Directory"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr "World Map"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "membership.membership_line"
msgstr "membership.membership_line"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "pull-left"
msgstr "pull-left"
