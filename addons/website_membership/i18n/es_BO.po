# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_membership
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-24 20:04+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Bolivia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_BO/)\n"
"Language: es_BO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Todas"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:88
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr ""

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:130
#, python-format
msgid "Free Members"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Ubicación"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.footer_custom
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Members"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr ""

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "membership.membership_line"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "pull-left"
msgstr ""
