# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_membership
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-06-13 17:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Todos"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:88
#, python-format
msgid "All Countries"
msgstr "Todos lo Países"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Asociaciones"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Encontrar un partner de negocios"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:130
#, python-format
msgid "Free Members"
msgstr "Miembros Gratuitos"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Lugar"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.footer_custom
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Members"
msgstr "Miembros"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "No se encontraron resultados."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Nuestro Directorio de Miembros"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr "Mapa Mundial"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "membership.membership_line"
msgstr "membership.membership_line"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "pull-left"
msgstr "Alinear a la izquierda"
