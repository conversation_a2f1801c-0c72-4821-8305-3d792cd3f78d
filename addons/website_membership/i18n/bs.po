# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_membership
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Sve"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:83
#, python-format
msgid "All Countries"
msgstr "Sve zemlje"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "Zatvori"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr ""

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:126
#, python-format
msgid "Free Members"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Lokacija"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
#: model:website.menu,name:website_membership.menu_members
msgid "Members"
msgstr "Članovi"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr ""

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr "Svjetska mapa"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "float-left"
msgstr ""
