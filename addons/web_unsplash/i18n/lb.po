# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr ""

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:57
#, python-format
msgid "Apply"
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:86
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr ""

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:34
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:52
#, python-format
msgid "Generate an access key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:56
#, python-format
msgid "How to find my Unsplash Application ID?"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:50
#, python-format
msgid "Paste your access key here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:54
#, python-format
msgid "Paste your application ID here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:40
#, python-format
msgid "Photos not found"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:84
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:93
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:17
#, python-format
msgid "Search among my images for:"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:21
#, python-format
msgid "Search from Unsplash for:"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:72
#, python-format
msgid "Search is temporarily unavailable"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:90
#, python-format
msgid "Something went wrong"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:75
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:81
#, python-format
msgid "Unauthorized Key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:66
#, python-format
msgid "Unsplash requires an access key and an application ID"
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr ""
