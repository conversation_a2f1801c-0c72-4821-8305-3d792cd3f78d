<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="event_lead_rule_view_tree" model="ir.ui.view">
        <field name="name">event.lead.rule.view.tree.inherit.website.event.crm</field>
        <field name="model">event.lead.rule</field>
        <field name="inherit_id" ref="event_crm.event_lead_rule_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lead_creation_basis']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
        </field>
    </record>
    <record id="event_lead_rule_view_form" model="ir.ui.view">
        <field name="name">event.lead.rule.view.form.inherit.website.event.crm</field>
        <field name="model">event.lead.rule</field>
        <field name="inherit_id" ref="event_crm.event_lead_rule_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='lead_creation_basis']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
        </field>
    </record>
</odoo>
