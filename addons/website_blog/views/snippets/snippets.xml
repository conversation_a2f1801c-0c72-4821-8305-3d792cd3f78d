<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="website_blog" inherit_id="website.snippets" name="Snippet Blog">
    <xpath expr="//t[@id='blog_posts_hook']" position="replace">
        <t t-snippet="website_blog.s_blog_posts" string="Blog Posts" t-thumbnail="/website_blog/static/src/img/s_blog_posts.svg"/>
    </xpath>
</template>

<template id="snippet_options" inherit_id="website.snippet_options" name="Blog snippet options">
    <xpath expr="." position="inside">
        <div data-js="BlogPostTagSelection" data-selector=".o_wblog_post_page_cover" data-target="#o_wblog_post_name" data-no-check="true">
            <we-many2many string="Tags"
                data-no-preview="true"
                data-model="blog.post"
                data-m2o-field="tag_ids"
                data-set-tags=""
                data-create-method="createTag"/>
        </div>
        <!-- Blog Posts page  -->
        <div data-selector="main:has(#o_wblog_index_content)" data-page-options="true" groups="website.group_website_designer" data-no-check="true" string="Blogs Page">
            <we-select string="Top Banner" data-no-preview="true" data-reload="/">
                <we-button data-customize-website-views="website_blog.opt_blog_cover_post"
                    data-name="blog_cover_opt">Name / Latest Post</we-button>
                <we-button data-customize-website-views="">Drop Zone for Building Blocks</we-button>
            </we-select>
            <we-checkbox string="Full-Width"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_blog_cover_post_fullwidth_design"
                         data-dependencies="blog_cover_opt"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-select string="Layout" data-no-preview="true" data-reload="/">
                <we-button data-customize-website-views="">Grid</we-button>
                <we-button data-customize-website-views="website_blog.opt_blog_list_view">List</we-button>
            </we-select>
            <we-checkbox string="Cards"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_blog_cards_design"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Increase Readability"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_blog_readable"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Sidebar"
                         data-name="blog_posts_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_sidebar_show"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Archives"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_posts_sidebar_opt"
                         data-customize-website-views="website_blog.opt_sidebar_blog_index_archives"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Follow us"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_posts_sidebar_opt"
                         data-customize-website-views="website_blog.opt_sidebar_blog_index_follow_us"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Tags List"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_posts_sidebar_opt"
                         data-customize-website-views="website_blog.opt_sidebar_blog_index_tags"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-select string="Posts List" data-no-preview="true" data-reload="/">
                <we-button data-customize-website-views="">No Cover</we-button>
                <we-button data-customize-website-views="website_blog.opt_posts_loop_show_cover">Cover</we-button>
            </we-select>
            <we-checkbox string="Author"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_posts_loop_show_author"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Comments/Views Stats"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_posts_loop_show_stats"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Teaser &amp; Tags"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_posts_loop_show_teaser"
                         data-no-preview="true"
                         data-reload="/"/>
        </div>
        <!-- Blog Post page  -->
        <div data-selector="main:has(#o_wblog_post_main)" data-page-options="true" groups="website.group_website_designer" data-no-check="true" string="Blog Page">
            <we-select string="Layout" data-no-preview="true" data-reload="/">
                <we-button data-customize-website-views="website_blog.opt_blog_post_regular_cover">Title Above Cover</we-button>
                <we-button data-customize-website-views="">Title Inside Cover</we-button>
            </we-select>
            <we-checkbox string="Increase Readability"
                         class="o_we_sublevel_1"
                         data-customize-website-views="website_blog.opt_blog_post_readable"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Sidebar"
                         data-name="blog_post_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_post_sidebar"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Archive"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_post_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_post_archive_display"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Author"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_post_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_post_author_avatar_display"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Blogs List"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_post_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_post_blogs_display"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Share Links"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_post_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_post_share_links_display"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Tags"
                         class="o_we_sublevel_1"
                         data-dependencies="blog_post_sidebar_opt"
                         data-customize-website-views="website_blog.opt_blog_post_tags_display"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Breadcrumb"
                         data-customize-website-views="website_blog.opt_blog_post_breadcrumb"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-row string="Bottom" class="o_we_full_row">
                <we-button string="Next Article"
                           data-customize-website-views="website_blog.opt_blog_post_read_next"
                           data-no-preview="true"
                           data-reload="/"/>
                <we-button string="Comments"
                           data-name="allow_comments_opt"
                           data-customize-website-views="website_blog.opt_blog_post_comment"
                           data-no-preview="true"
                           data-reload="/"/>
            </we-row>
            <we-checkbox string="Select to Comment"
                         class="o_we_sublevel_1"
                         data-dependencies="allow_comments_opt"
                         data-customize-website-views="website_blog.opt_blog_post_select_to_comment"
                         data-no-preview="true"
                         data-reload="/"/>
            <we-checkbox string="Select to Tweet"
                         data-customize-website-views="website_blog.opt_blog_post_select_to_tweet"
                         data-no-preview="true"
                         data-reload="/"/>
        </div>
    </xpath>
    <xpath expr="//*[@data-js='anchor']" position="attributes">
        <attribute name="data-exclude" add=".o_wblog_post_content_field > :not(div, section)" separator=","/>
    </xpath>

    <!-- Hides ContainerWidth option for content in blog posts -->
    <xpath expr="//div[@data-js='ContainerWidth']" position="attributes">
        <attribute name="data-exclude" add="#o_wblog_post_content *" separator=","/>
    </xpath>
</template>

<template id="blog_searchbar_input_snippet_options" inherit_id="website.searchbar_input_snippet_options" name="blog search bar snippet options">
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='scope_opt']" position="inside">
        <we-button data-set-search-type="blogs" data-select-data-attribute="blogs" data-name="search_blogs_opt" data-form-action="/blog">Blogs</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='order_opt']" position="inside">
        <we-button data-set-order-by="published_date asc" data-select-data-attribute="published_date asc" data-dependencies="search_blogs_opt" data-name="order_published_date_asc_opt">Date (old to new)</we-button>
        <we-button data-set-order-by="published_date desc" data-select-data-attribute="published_date desc" data-dependencies="search_blogs_opt" data-name="order_published_date_desc_opt">Date (new to old)</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/div[@data-dependencies='limit_opt']" position="inside">
        <we-checkbox string="Description" data-dependencies="search_blogs_opt" data-select-data-attribute="true" data-attribute-name="displayDescription"
            data-apply-to=".search-query"/>
        <we-checkbox string="Publication Date" data-dependencies="search_blogs_opt" data-select-data-attribute="true" data-attribute-name="displayDetail"
            data-apply-to=".search-query"/>
    </xpath>
</template>
</odoo>
