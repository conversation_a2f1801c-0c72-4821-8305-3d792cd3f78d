# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_blog
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>le <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Finally, the leading edge is being brought to the masses.\n"
"                        It will now be the turn of the big players to catch up to\n"
"                        the superior technologies of the SME.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo now competes on many fronts, with no real\n"
"                    competition out there to knock them off the top spot.\n"
"                    With the launch of their integrated CMS and Ecommerce\n"
"                    systems,it only elevates their position as one of the leading\n"
"                    lights in the open source revolution. It will be at least 5\n"
"                    years before another ERP or CMS provider will be able to\n"
"                    compete at this level due to the technology currently\n"
"                    employed by most industry providers.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo's latest launch will allow a business to go from\n"
"                    zero to trading online quicker than ever before,” Stuart\n"
"                    Mackintosh, MD of Open Source specialist and Odoo\n"
"                    integration partner, OpusVL, explains. “The investment\n"
"                    required to have a fully automated business system is\n"
"                    dramatically reduced, enabling the small and medium\n"
"                    enterprise to compete at a level of functionality and\n"
"                    performance previously reserved for the big IT investors.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"This is another clever and highly disruptive move by\n"
"                    Odoo,which will force other technology providers to\n"
"                    take another look at the value they are providing to ensure\n"
"                    that their 'solutions' can still compete.\""
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "&amp;mdash;"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
msgid ""
"(Un)archiving a blog automatically (un)archives its posts. Do you want to "
"proceed?"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "1 post"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "10 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "2 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "3 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "4 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "5 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "6 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "7 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "8 posts"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "9 posts"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:53
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:69
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:73
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:44
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "<span class=\"fa fa-folder-open\"/> In"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A mix of push &amp; pull: Today, people\n"
"                        are victims of what others decide to push to them.\n"
"                        Odoo differentiates:"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "About us"
msgstr "O nama"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Aktivan"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Adding to industry leading technology"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "All blogs"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/s_latest_posts_frontend.js:59
#, python-format
msgid ""
"An error occured with this latest posts block. If the problem persists, "
"please consider deleting it and adding a new one"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Arhivirano"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_history
msgid "Archives"
msgstr "Arhive"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"As it comes, there is a default website based on Bootstrap\n"
"                    3, the latest industry standard for rapid development of\n"
"                    multi-device websites backed by Twitter, so can be directly\n"
"                    integrated with many web tools and works across all devices\n"
"                    by default."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"At Odoo, we build tools to bring productivity to\n"
"                    enterprises. As emails and information flows are one of\n"
"                    the biggest wastes of time in companies, we have to fix\n"
"                    this."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Black"
msgstr "Crna"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#: model:website.menu,name:website_blog.menu_news
msgid "Blog"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website.py:22
#: code:addons/website_blog/models/website.py:47
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website.py:53
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website.py:28
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_complete_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website.py:24
#: code:addons/website_blog/models/website.py:49
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blue"
msgstr ""

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "Building your company's website and selling your products online easy."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Kategorija"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:29
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
#, python-format
msgid "Change Cover"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "Choose a blog"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:35
#, python-format
msgid "Choose an image from the library."
msgstr "Odaberite sliku iz bibllioteke:"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:30
#, python-format
msgid "Click here to change your post cover."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid ""
"Click on \"New Page\" in the top-right corner to write your first blog post."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:40
#, python-format
msgid "Click on <b>Save</b> to set the picture as cover."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "Close"
msgstr "Zatvori"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Sadržaj"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr ""

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "Dupliciraj"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Emails are broken."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Emails make me waste my time. But I need them.\n"
"                    Given the importance that emails have in our lives,\n"
"                    it's incredible it's still one of the only software\n"
"                    areas that did not evolve in the past 20 years!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Filter Color"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Filter Intensity"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Focus on the Content: Everything is\n"
"                        stripped to emphasize on the real message. No more\n"
"                        welcome introductions, greetings, signatures and legal\n"
"                        notes.We standardize the layout of each message.\n"
"                        (signatures are on the profile of a contact, not in\n"
"                        every message)"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Folders and mailing lists are great tools but too\n"
"                        complex in traditional email clients. In Odoo, a\n"
"                        group of contacts that share a discussion can be\n"
"                        created with one click. Every group should have it's\n"
"                        own email address."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_channel_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Full Screen"
msgstr "Cijeli ekran"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Get Things Done: your inbox is a\n"
"                        todo list. You should be able to process (not only\n"
"                        read) the inbox and easily mark messages for future\n"
"                        actions. Every inbox should be empty after having\n"
"                        been processed; no more overload of information."
msgstr ""

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Get in touch with us"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Github"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Google Plus"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Green"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Grupiši po"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Here are the ideas behind the Odoo communication tools:"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "High"
msgstr "Visoki"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"However, unlike other web content management systems, it\n"
"                    fully integrates into the back-end database. This means\n"
"                    that when you edit a product description, image or price,\n"
"                    it updates the product database in real time, providing a\n"
"                    true self-service window into the business."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
#: model:blog.post,website_meta_description:website_blog.blog_post_1
msgid "Ideas behind the Odoo communication tools."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Instagram"
msgstr ""

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Integrating your CMS and E-Commerce"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
msgid "Is published"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Keep control of what you want to receive or don't want\n"
"                        to receive. People should never receive spam. You\n"
"                        should follow/unfollow any kind of information in one\n"
"                        click."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_big_picture
msgid "Latest Posts"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Like many modern website editors, with Odoo you can edit\n"
"                    content in-line, enabling you to see exactly what you are\n"
"                    changing and ensure your changes suit the context."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "LinkedIn"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Low"
msgstr "Nizak"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Medium"
msgstr "Medijum"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for action\": they\n"
"                                require your immediate attention and you need\n"
"                                to process them all. This accounts for 10%\n"
"                                of your daily emails. Use the \"To: me\" menu\n"
"                                for these."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for information\":\n"
"                                you can pull them when you need some specific\n"
"                                information; they are not required to be read\n"
"                                every day.You receive only what you decided\n"
"                                to follow.This accounts for 90% of your daily\n"
"                                emails.Use the \"Inbox\" menu for these."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Mid screen"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Naziv:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Narrow"
msgstr "Usko"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:37
#, python-format
msgid "New Blog Post"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "New Features Launched"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_history
msgid "Next year"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "No Cover"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/s_latest_posts_frontend.js:44
#, python-format
msgid "No blog post was found. Make sure your posts are published."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No blog post yet."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "No tags defined!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "None"
msgstr "Ništa"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_options
msgid "Number of posts"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Odoo claims to be 'the Open Source software that makes\n"
"                    building your company's website and selling your products\n"
"                    online easy'. So how true is this statement?"
msgstr ""

#. module: website_blog
#: model:blog.post,website_meta_keywords:website_blog.blog_post_1
msgid "Odoo, email"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:63
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_tags
msgid "Others"
msgstr ""

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Our Blog"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_blogs
msgid "Our Blogs"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Participate on our social stream."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Productivity is key: our smart user\n"
"                        interface does not require you to click on every mail\n"
"                        to read a thread. Reading a full thread, replying,\n"
"                        attaching documents is super fast."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_status
msgid "Published"
msgstr "Objavljeno"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr ""

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Purple"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "RSS"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__ranking
msgid "Ranking"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_big_picture_template
#: model_terms:ir.ui.view,arch_db:website_blog.s_latest_posts_list_template
msgid "Read More"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_complete_read_next
msgid "Read Next <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Reading my inbox is the most unproductive task I do\n"
"                    on a daily basis. I have to spend one full hour a\n"
"                    day to process my emails. All the junk flows in the\n"
"                    same inbox; spams, information that doesn't matter,\n"
"                    quoted answers of quoted answers, etc. At the end\n"
"                    of the hour, only 10 emails actually requested an\n"
"                    answer from me. With a good tool, I could have done\n"
"                    my job in 10 minutes!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Red"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:38
#, python-format
msgid "Select Blog"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:15
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:26
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Size"
msgstr "Veličina"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Mala slika ovog kontakta. Veličina je automatski promijenjena na 64x64 px "
"sliku. sa očuvanim proporcijama. Koristite ovo polje gdje god je potrebna "
"mala slika."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:138
#: code:addons/website_blog/static/src/js/website.tour.blog.js:52
#, python-format
msgid "Start writing here..."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_status
msgid "Status"
msgstr "Status"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Stay updated :"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_complete_read_next
msgid "Subtitle"
msgstr "Podnaslov"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr ""

#. module: website_blog
#: sql_constraint:blog.tag.category:0
msgid "Tag category already exists !"
msgstr ""

#. module: website_blog
#: sql_constraint:blog.tag:0
msgid "Tag name already exists !"
msgstr "Naziv oznake već postoji!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_tags
msgid "Tags"
msgstr "Oznake"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Technical"
msgstr "Tehnički detalji"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "The Communication Mechanism of Odoo"
msgstr ""

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "The Future of Emails"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "Kompletan URL za pristup dokumentu putem website-a."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"This page is great to improve your <strong>Search Engine Optimization</strong>;\n"
"                   You can review titles, keywords and descriptions of all blogs at once."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"This provides a single source of data for your company and\n"
"                    removes the need to create offline synchronisation between\n"
"                    website and product database."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
msgid "Title"
msgstr "Naslov"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"To add to an already comprehensive set of Odoo\n"
"                features, a website content management system (CMS or WMS) has been developed and a beta release is\n"
"                available from today, 31st January 2014."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "To blog content"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"To disrupt emails, you need more than just another user\n"
"                    interface. We need to rethink the whole communication flow."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_status
msgid "Unpublished"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Untitled Post"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:58
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Web stranica"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "Website URL"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Website meta opis"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta ključne riječi"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Website meta naslov"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "White"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid ""
"Write a small text here for when <b>new visitors</b> find your website\n"
"            through your <b>blog entries</b>, referenced in Google."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:20
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Yellow"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"You should <strong>add a banner on the top</strong> as it is a frequent landing page for new visitors.\n"
"                   <span class=\"text-muted\">This box will not be visible to your visitors.</span>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Youtube"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comment"
msgstr "komentar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comments"
msgstr "komentari"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "float-right"
msgstr ""

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "functional"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "unpublished"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "view"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "views"
msgstr ""

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "website"
msgstr "website"
