# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_payment
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-03-12 06:22+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Chile) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CL/)\n"
"Language: es_CL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Acquirer"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Add a new payment method:"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Amount"
msgstr "Monto"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.website_settings_payment
msgid "Configure payment acquirers"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_config_settings_default_acquirer
msgid "Default Acquirer"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,help:website_payment.field_account_config_settings_default_acquirer
msgid ""
"Default payment acquirer for website payments; your provider needs to be "
"visible in the website."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Delete <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Delete a payment method:"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.website_settings_payment
msgid "E-Commerce"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "From"
msgstr "De"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_meth_link
msgid "Manage your payment methods"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "My Account"
msgstr "Mi Cuenta"

#. module: website_payment
#: code:addons/website_payment/controllers/main.py:43
#, python-format
msgid "Pay Now"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Payment"
msgstr "Pago"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.header_footer_custom_payment
msgid "Payment Method"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Payment Methods"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "Payment processed by"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Reference"
msgstr "Referencia"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "This transaction will be processed by"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_website
msgid "Website"
msgstr "Sitio Web"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_config_settings
msgid "account.config.settings"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "payment_provider_logo"
msgstr ""
