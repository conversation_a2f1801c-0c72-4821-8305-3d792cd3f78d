# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_payment
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-06-13 17:07+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Retornar a Mi Cuenta"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Acquirer"
msgstr "Adquiriente"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Add a new payment method:"
msgstr "Agregar un nuevo un método de pago:"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Amount"
msgstr "Cantidad"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.website_settings_payment
msgid "Configure payment acquirers"
msgstr "Configurar pagos de los adquirientes"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_config_settings_default_acquirer
msgid "Default Acquirer"
msgstr "Adquiriente por Defecto"

#. module: website_payment
#: model:ir.model.fields,help:website_payment.field_account_config_settings_default_acquirer
msgid ""
"Default payment acquirer for website payments; your provider needs to be "
"visible in the website."
msgstr ""
"Pago por defecto del aquiriente para pagos del sitio web; su proveedor "
"necesita estar visible en el sitio web."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Delete <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Eliminar <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Delete a payment method:"
msgstr "Eliminar un método de pago:"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.website_settings_payment
msgid "E-Commerce"
msgstr "E-Commerce"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "From"
msgstr "De"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_meth_link
msgid "Manage your payment methods"
msgstr "Administrar sus métodos de pago"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "My Account"
msgstr "Mi Cuenta"

#. module: website_payment
#: code:addons/website_payment/controllers/main.py:43
#, python-format
msgid "Pay Now"
msgstr "Pagar Ahora"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Payment"
msgstr "Pago"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Pago del Adquiriente"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.header_footer_custom_payment
msgid "Payment Method"
msgstr "Método de Pago"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Payment Methods"
msgstr "Métodos de Pago"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "Payment processed by"
msgstr "Pago procesado por"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Reference"
msgstr "Referencia"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "This transaction will be processed by"
msgstr "Esta transacción será procesada por"

#. module: website_payment
#: model:ir.model,name:website_payment.model_website
msgid "Website"
msgstr "Sitio Web"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_config_settings
msgid "account.config.settings"
msgstr "account.config.settings"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "payment_provider_logo"
msgstr "payment_provider_logo"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través del sitio web"

#~ msgid "Website URL"
#~ msgstr "URL del Sitio Web"
