# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid " Flag"
msgstr "Segnala"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid " Flagged"
msgstr "Segnalati"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "# Answers"
msgstr "N. risposte"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "N. preferiti"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "N. messaggi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "# Views"
msgstr "N visualizzazioni"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to accept or refuse an answer."
msgstr "Richiesti %d karma per accettare o rifiutare una risposta."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to answer a question."
msgstr "Richiesti %d karma per rispondere a una domanda."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to close or reopen a post."
msgstr "Richiesti %d karma per chiudere o riaprire un messaggio."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to comment."
msgstr "Richiesti %d karma per commentare."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert a comment to an answer."
msgstr "Richiesti %d karma per convertire un commento in una risposta."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert an answer to a comment."
msgstr "Richiesti %d karma per convertire una domanda in un commento."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert your comment to an answer."
msgstr ""
"Richiesti %d karma per convertire un proprio commento in una risposta."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new Tag."
msgstr "Richiesti %d karma per creare una nuova etichetta."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new question."
msgstr "Richiesti %d karma per creare una nuova domanda."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to delete or reactivate a post."
msgstr "Richiesti %d karma per eliminare o riattivare un messaggio."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to downvote."
msgstr "Richiesti %d karma per votare negativamente."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to edit a post."
msgstr "Richiesti %d karma per modificare un messaggio."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to flag a post."
msgstr "Richiesti %d karma per segnalare un messaggio."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to mark a post as offensive."
msgstr "Richiesti %d karma per segnare un messaggio come offensivo."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to post an image or link."
msgstr "Richiesti %d karma per pubblicare un'immagine o un link."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to refuse a post."
msgstr "Richiesti %d karma per rifiutare un messaggio."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to retag."
msgstr "Richiesti %d karma per cambiare un'etichetta."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a comment."
msgstr "Richiesti %d karma per separare un commento."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a post."
msgstr "Richiesti %d karma per separare un messaggio."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to upvote."
msgstr "Richiesti %d karma per votare positivamente."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to validate a post."
msgstr "Richiesti %d karma per convalidare un messaggio."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "&amp;nbsp;and&amp;nbsp;"
msgstr "&amp;nbsp;e&amp;nbsp;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr ""
"(La sezione soprastante è stata adattata dalle FAQ di Stack Overflow.)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr "(voti - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ", by"
msgstr ", di"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ", consider <b>adding an example</b>."
msgstr ", valutare l'<b>aggiunta di un esempio</b>."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr "/ (giorni + 2) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "0 Answers"
msgstr "0 risposte"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "45% of questions shared"
msgstr "il 45% delle domande condivise"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"il 65% di possibilità in più di ottenere\n"
"        una risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">È presente un messaggio in attesa</b>\n"
"                        Per rispondere alle domande è necessario attendere che un moderatore convalidi il precedente messaggio."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b> [Offensive]</b>"
msgstr "<b> [Offensivo]</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>Le risposte non devono aggiungere o estendere le domande</b>. Modifica "
"invece la domanda o aggiungi un commento alla stessa."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>Le risposte non devono aggiungere o estendere le domande</b>. Modifica "
"invece la domanda o aggiungi un commento."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>Le risposte non devono commentare altre risposte</b>. Aggiungi invece un "
"commento."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>Le risposte non devono avviare dibattiti</b>. Questa comunità D&R non è "
"un gruppo di discussione. Evita di alimentare dibattiti nelle risposte, "
"rischiano di diluire l'essenza stessa delle domande e delle risposte. Per "
"discussioni brevi, usa la funzione commenti."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>Le risposte non devono solo fare riferimento ad altre domande</b>. "
"Aggiungi invece un commento alla domanda indicando \"Possibile duplicato "
"di...\". Tuttavia, è corretto inserire link ad altre domande o risposte "
"fornendo informazioni aggiuntive rilevanti."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>Le risposte non devono solo fare riferimento ad altre domande</b>. "
"Aggiungi invece un commento indicando <i>\"Possibile duplicato di...\"</i>. "
"Tuttavia, è corretto inserire link ad altre domande o risposte fornendo "
"informazioni aggiuntive rilevanti."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>Le risposte non devono solo fornire un link alla soluzione</b>. Aggiungi "
"invece una descrizione, anche se è solo un copia/incolla. I link sono "
"benvenuti, ma dovrebbero essere complementari alla risposta, con riferimenti"
" o letture aggiuntive."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>Prima di chiedere, effettua una ricerca tra le domande simili.</b> È "
"possibile ricercare per titoli o per etichette. È corretto anche rispondere "
"alle proprie domande."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>Evita le domande troppo soggettive o argomentative</b>, oppure non "
"rilevanti per questa comunità."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>Prova a fornire una risposta sostanziale.</b> Se fosse necessario commentare la domanda o la risposta, \n"
"            <b>usa lo strumento per i commenti.</b> Ricorda che puoi sempre <b>rivedere le risposte</b>\n"
"            - non è necessario rispondere due volte alla stessa domanda. Inoltre, <b>non dimenticarti di votare</b>\n"
"            - selezionare le migliori domande e risposte aiuta molto!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<b>Tags</b> I Follow"
msgstr "<b>Etichette</b> seguite"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"<b>This forum is empty.</b><br/>\n"
"                    Be the first one asking a question"
msgstr ""
"<b>Il forum è vuoto.</b><br/>\n"
"                    Sii il primo a formulare una domanda"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What kinds of questions can I ask here?</b>"
msgstr "<b>Che tipo di domande è possibile porre?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my answers?</b>"
msgstr "<b>Cosa deve essere evitato nelle risposte?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my questions?</b>"
msgstr "<b>Cosa deve essere evitato nelle domande?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""
"<b>Perché altre persone possono modificare le mie domande/risposte?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>È già presente un messaggio in attesa.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr "<b>[Risposta]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy bg-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy bg-gold ms-2\" role=\"img\" aria-label=\"Medaglia d'oro\" title=\"Medaglia d'oro\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<em class=\"d-block mb-2\">or</em>"
msgstr "<em class=\"d-block mb-2\">oppure</em>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\" fa fa-comment text-muted me-1\"/>Comment"
msgstr "<i class=\" fa fa-comment text-muted me-1\"/>Commenta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-bell fa-fw\"/> Followed Posts"
msgstr "<i class=\"fa fa-bell fa-fw\"/> Messaggi seguiti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-bug\"/> Filter Tool"
msgstr "<i class=\"fa fa-bug\"/> Strumento filtro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>Accetta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>You've Completely Caught Up!</b><br/>"
msgstr ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>Tutto completato!</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""
"<i class=\"fa fa-check\"/> Come si configurano le imposte canadesi TPS e "
"TVQ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-check-square-o fa-fw\"/> To Validate"
msgstr "<i class=\"fa fa-check-square-o fa-fw\"/> Da convalidare"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<i class=\"fa fa-chevron-left me-1\"/>Back"
msgstr "<i class=\"fa fa-chevron-left me-1\"/>Indietro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to All Topics"
msgstr "<i class=\"fa fa-chevron-left me-2\"/>Ritorna agli argomenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"fa fa-chevron-left small\"/> Back"
msgstr "<i class=\"fa fa-chevron-left small\"/> Indietro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"
msgstr "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-1\" title=\"Visualizzazioni\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-flag fa-fw\"/> Flagged"
msgstr "<i class=\"fa fa-flag fa-fw\"/> Segnalati"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-flag ms-4 mr4\"/>\n"
"                                    Flagged"
msgstr ""
"<i class=\"fa fa-flag ms-4 mr4\"/>\n"
"                                    Segnalati"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> Nazione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Sito web\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Modifica<span class=\"d-none d-lg-inline\"> la risposta</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-question-circle-o fa-fw\"/> My Posts"
msgstr "<i class=\"fa fa-question-circle-o fa-fw\"/> I miei messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-reply me-1\"/>Answer"
msgstr "<i class=\"fa fa-reply me-1\"/>Rispondi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-share-alt text-muted me-1\"/>Share"
msgstr "<i class=\"fa fa-share-alt text-muted me-1\"/>Condividi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Share"
msgstr ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Condividi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-star fa-fw\"/> Favourites"
msgstr "<i class=\"fa fa-star fa-fw\"/> Preferiti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-tags fa-fw\"/> Followed Tags"
msgstr "<i class=\"fa fa-tags fa-fw\"/> Etichette seguite"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Voti negativi\" title=\"Voti negativi\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Voti "
"positivi\" title=\"Voti positivi\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw me-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw me-1\"/>Rifiuta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> Buongiorno a tutti! Qualcuno può aiutarmi a "
"risolvere il problema di calcolo dell'imposta in Canada? Grazie!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> Utente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"fw-bold\">Votes</small>"
msgstr "<small class=\"fw-bold\">Voti</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                    Segnalato\n"
"                </small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span class=\"badge text-bg-info\">Closed</span>"
msgstr "<span class=\"badge text-bg-info\">Chiuso</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy bg-bronze ms-2\" role=\"img\" aria-label=\"Bronze"
" badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy bg-bronze ms-2\" role=\"img\" aria-"
"label=\"Medaglia di bronzo\" title=\"Medaglia di bronzo\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy bg-silver ms-2\" role=\"img\" aria-label=\"Silver"
" badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy bg-silver ms-2\" role=\"img\" aria-"
"label=\"Medaglia d'argento\" title=\"Medaglia d'argento\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"fw-bold\">No answer posted yet.</span>"
msgstr "<span class=\"fw-bold\">Ancora nessuna risposta pubblicata.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"fw-bold\">No question posted yet.</span>"
msgstr "<span class=\"fw-bold\">Ancora nessuna domanda pubblicata.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"
msgstr "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"
msgstr "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1\">|</span>"
msgstr "<span class=\"mx-1\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"
msgstr ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"
msgstr "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<span class=\"navbar-text me-1\">Go to:</span>"
msgstr "<span class=\"navbar-text me-1\">Vai a:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<span class=\"navbar-text me-3\">Show Tags Starting By</span>"
msgstr "<span class=\"navbar-text me-3\">Mostra etichette partendo da</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">Preferiti</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Vai al <br/>sito web</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">Messaggi</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill fw-bold text-success ms-2 px-2\">\n"
"                            Best Answer\n"
"                        </span>"
msgstr ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill fw-bold text-success ms-2 px-2\">\n"
"                            Migliore risposta\n"
"                        </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>By </span>"
msgstr "<span>Di </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "A clear, explicit and concise title"
msgstr "Un titolo chiaro, esplicito e conciso"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "Pubblicata una nuova risposta a '"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "Pubblicata una nuova domanda in"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "About"
msgstr "Informazioni"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Accettare una risposta alle proprie domande"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Accettare una risposta per qualsiasi domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr "Risposta accettata"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Accettare una risposta"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Access Denied"
msgstr "Accesso negato"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "Attivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "Attività"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "Attività"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"Dopo l'invio di un messaggio all'utente verrà proposto di condividere la sua"
" domanda o risposta nei social network, abilitando così la propagazione del "
"contenuto del forum. "

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "Tutti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All Forums"
msgstr "Tutti i forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "All Tags"
msgstr "Tutte le etichette"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "Tutti gli argomenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All forums"
msgstr "Tutti i forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr "Consenti spostamento messaggio in cima"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Amazing! There are no unanswered questions left!"
msgstr "Magnifico! Non è rimasta alcuna domanda senza risposta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Answer"
msgstr "risposta"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr "Risposta modificata"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Risposta accettata"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Risposta accettata con almeno 15 voti"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Risposta votata negativamente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Risposta segnalata"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "Rispondere alle domande"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Risposta votata positivamente"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "Risposta votata positivamente 15 volte"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "Risposta votata positivamente 4 volte"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "Risposta votata positivamente 6 volte"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "Risposta accettata con almeno 3 voti"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "Con risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Posts"
msgstr "Messaggi con risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr "Con risposta di"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "Risposta alla propria domanda con almeno 4 voti positivi "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "Risposte"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Compare in"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "In archivio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "Porre domande"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "Porre domande senza convalida"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "Domanda con risposta accettata"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "Domanda con almeno 150 visualizzazioni"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "Domanda con almeno 250 visualizzazioni"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "Domanda con almeno 500 visualizzazioni"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "Prima domanda con almeno un voto positivo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "Domande del"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "Porre una domanda"

#. module: website_forum
#: model:ir.model,name:website_forum.model_ir_attachment
msgid "Attachment"
msgstr "Allegato"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "Autore"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "Gruppo autorizzato"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "Autobiografo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Avatar"
msgstr "Avatar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Back"
msgstr "Indietro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "Ritorna alla domanda"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Bad Request"
msgstr "Richiesta errata"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Badges"
msgstr "Riconoscimenti"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "Di base"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Be less specific in your wording for a wider search result"
msgstr "Per un risultato di ricerca più ampio, usare parole meno specifiche"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Browse All"
msgstr "Sfoglia tutti"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr "Messaggio portato in cima il"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr "Condividendo la risposta, otterrai"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "Può accettare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "Può rispondere"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "Può chiedere"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "Può essere convalidato automaticamente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "Può chiudere"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "Può commentare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "Può convertire in commento"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "Può votare negativamente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "Può modificare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "Può segnalare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "Può moderare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "Può separare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "Può votare positivamente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_use_full_editor
msgid "Can Use Full Editor"
msgstr "Può usare l'editor completo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "Può visualizzare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "Cambiare etichette delle domande"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""
"Selezionare la casella per visualizzare una finestra a comparsa che propone "
"di condividere nei social network i messaggi senza risposta da più di 10 "
"giorni. Una volta condivisa una domanda, viene posizionata in alto nel "
"forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Check your spelling and try again"
msgstr "Controllare l'ortografia e riprovare"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Commentatore capo"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click here to accept this answer."
msgstr "Fai clic per accettare questa risposta."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to answer."
msgstr "Fai clic per rispondere."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your answer."
msgstr "Fai clic per pubblicare la risposta."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your question."
msgstr "Fai clic per pubblicare la domanda."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Close"
msgstr "Chiudi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Close Post"
msgstr "Messaggio chiuso"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "Motivi chiusura"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "Chiudere qualsiasi messaggio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "Chiudere i propri messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Close post"
msgstr "Chiudi messaggio"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
msgid "Closed"
msgstr "Chiuso"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "Chiuso da"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "Chiuso il"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Closing"
msgstr "Chiusura"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "Motivo chiusura"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment"
msgstr "commento"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "Commentare qualsiasi messaggio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "Commentare i propri messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr "Commenta questo messaggio..."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Commentatore"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comments"
msgstr "commenti"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Completamento biografia"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "Biografia completata"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "Contiene osservazioni offensive o calunniose"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "Contenuto"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr "Convertire tutte le risposte in commenti e viceversa"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as a answer"
msgstr "Converti in risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr "Converti in commento"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "Convertire commento in risposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr "Convertire le proprie risposte in commenti e viceversa"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "Corretta"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "Risposta corretta o risposta accettata"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "Data creazione"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr "Crea un nuovo messaggio nel forum"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Create a new post in this forum by clicking on the button."
msgstr "Fai clic sul pulsante per creare un nuovo messaggio nel forum."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "Crea una nuova etichetta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "Creare nuove etichette"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "Creata un'etichetta usata per 15 domande"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "Creata da"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Domanda attendibile"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Critico"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "Data"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "Data (in ordine decrescente)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "Data (in ordine crescente)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "Predefinito"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Default Sort"
msgstr "Ordinamento predefinito"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definisce la visibilità della sfida attraverso i menù"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Delete"
msgstr "Elimina"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "Eliminare qualsiasi messaggio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "Eliminare i propri messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Deleted"
msgstr "Eliminato"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "Eliminato un proprio messaggio con almeno 3 voti negativi"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "Eliminato un proprio messaggio con almeno 3 voti positivi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "Descrizione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Description visible on website"
msgstr "Descrizione visibile nel sito web"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Discard"
msgstr "Abbandona"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Disciplinato"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "Discussioni (risposte multiple)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "Visualizzare la biografia dettagliata dell'utente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr "Votare negativamente"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "Messaggio duplicato"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Edit"
msgstr "Modifica"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Answer"
msgstr "Modifica risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Edit Forum in Backend"
msgstr "Modifica forum da interfaccia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Question"
msgstr "Modifica domanda"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "Modificare qualsiasi messaggio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "Modificare i propri messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Modifica messaggio"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Redattore"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
#: model:ir.model.fields,help:website_forum.field_forum_post__can_use_full_editor
msgid "Editor Features: image and links"
msgstr "Funzionalità da redattore: immagine e link"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Saggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                        <i class=\"fa fa-question-circle\"/>"
msgstr ""
"Esempio\n"
"                        <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Domanda celebre"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite"
msgstr "Preferito"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Domanda preferita"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "Preferito"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Domanda preferita (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Domanda preferita (25)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Domanda preferita (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "Domande preferite"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Favourites"
msgstr "Preferiti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "Filtra per:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Primo parametro di rilevanza"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Primo voto negativo"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Prima modifica"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Primo voto positivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flag"
msgstr "Segnala"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "Segnalare messaggio come offensivo"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flagged"
msgstr "Segnalato"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "Segnalato da"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "Domande seguite"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Following"
msgstr "Seguiti"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"Ad esempio, se poni una domanda interessante o fornisci una risposta utile, "
"il tuo contributo verrà votato positivamente. Se invece la risposta è "
"fuorviante, otterrà un voto negativo. Ciascun voto a favore genera 10 punti,"
" quelli  contrari tolgono 2 punti. Per una singola domanda o risposta "
"possono essere accumulati un massimo di 200 punti al giorno. La tabella "
"riportata in fondo illustra i punti reputazione necessari per ciascun tipo "
"di attività di moderazione."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/website.py:0
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#, python-format
msgid "Forum"
msgstr "Forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Mode"
msgstr "Modalità forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Name"
msgstr "Nome forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Forum Page"
msgstr "Pagina forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr "Messaggio forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
msgid "Forum Post Pages"
msgstr "Pagine post forum"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_pages
msgid "Forum Posts"
msgstr "Messaggi forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Etichetta forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr "Forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forums_count
msgid "Forums Count"
msgstr "Numero forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Sfida di gamification"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Give your post title."
msgstr "Dai un titolo al messaggio."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Buona risposta"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Buona risposta (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Buona domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr "Grafico dei messaggi"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Ottima risposta"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Ottima risposta (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Ottima domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Grid"
msgstr "Griglia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Guidelines"
msgstr "Linee guida"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Guru"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Guru (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "Ha risposto"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Assistenza"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "Qui una tabella con privilegi e livello del karma"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr "Nascondi introduzione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "I'm <b>Following</b>"
msgstr "Sto <b>seguendo</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "I'm Following"
msgstr "Sto seguendo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Se l'autore non ha karma sufficiente, ai link viene aggiunto un attributo "
"nofollow"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr "Se questo approccio non fa per te, rispetta la comunità."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Se chiudi questo messaggio, verrà nascosto a tutti gli utenti\n"
"            tranne quelli con karma alto, in modo da permetterne la\n"
"            moderazione. "

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"Se l'idea di partenza corrisponde a uno di questi esempi o il motivo della "
"domanda è \"Vorrei partecipare a una discussione su ______”, è meglio che "
"effettui la richiesta nelle nostre mailing list. Tuttavia, se la motivazione"
" è “Vorrei che gli altri mi spiegassero ______”, questo è probabilmente il "
"posto giusto."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Se contrassegni questo messaggio come offensivo, verrà nascosto a tutti\n"
"            gli utenti tranne quelli con karma alto, in modo da permetterne la\n"
"            moderazione."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "Immagine"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "Immagine a 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "Immagine a 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "Immagine a 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "Immagine a 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "Dichiarazioni inopportune e inaccettabili"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Insert tags related to your question."
msgstr "Inserisci etichette relative alla domanda."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "Linguaggio insultante e offensivo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "È un preferito"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "Ha una risposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr " È la biografia dell'autore visibile dal suo messaggio"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to modify someone else's vote."
msgstr "Non è consentito modificare il voto di qualcun altro."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr "Non è consentito votare i propri messaggi."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Karma"
msgstr "Karma"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Karma Error"
msgstr "Errore karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr "Acquisizioni karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr "Diritti relativi al karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma per chiudere"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma per commentare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Karma per convertire commento in risposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Karma per modificare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma per separare"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Last Post"
msgstr "Ultimo messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Last Post:"
msgstr "Ultimo messaggio:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__write_date_desc
msgid "Last Updated"
msgstr "Ultimo aggiornamento"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr "Data ultima attività"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Layout"
msgstr "Struttura"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "Lasciate 10 risposte con punteggio di almeno 10 punti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "List"
msgstr "Elenco"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Mark as Best Answer"
msgstr "Segna come migliore risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Mark as Offensive"
msgstr "Segna come offensivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "Segna come offensivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "Segna come spam"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__menu_id
msgid "Menu"
msgstr "Menù"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "Modalità"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "Moderare messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation"
msgstr "Moderazione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Moderation tools"
msgstr "Strumenti di moderazione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "More"
msgstr "Altro"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "Inoltre:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "Più votati"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr "Più risposti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr "Più votati"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr ""
"Porta la domanda in cima all'elenco condividendola sui social network."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Favourites</b>"
msgstr "I miei <b>preferiti</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Posts</b>"
msgstr "I miei <b>messaggi</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Favourites"
msgstr "I miei preferiti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Posts"
msgstr "I miei messaggi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "Il mio voto"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "My profile"
msgstr "Il mio profilo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "Nome"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr "Voto negativo"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Nuova risposta"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action_add
msgid "New Forum"
msgstr "Nuovo forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Post"
msgstr "Nuovo messaggio"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Nuova domanda"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Newest"
msgstr "Più recenti"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Bella risposta"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Bella risposta (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Bella domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "No activities yet!"
msgstr "Ancora nessuna attività."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "No favourite questions in this forum (yet).<br/>"
msgstr "Nel forum non sono presenti argomenti preferiti (per ora).<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No flagged posts"
msgstr "Nessun messaggio segnalato"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "Ancora nessun forum disponibile."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No post to be validated"
msgstr "Nessun messaggio da convalidare"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "No tags"
msgstr "Nessuna etichetta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "No vote given by you yet!"
msgstr "Ancora nessun voto da parte tua."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "Link di tipo nofollow"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "Non è un messaggio autentico"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "Non rilevante od obsoleto "

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Domanda rilevante"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "Numero di messaggi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "Numero di messaggi segnalati"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Numero di messaggi in attesa di convalida"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "Fuori tema o non rilevante"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Offensive"
msgstr "Offensivo"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.header
#, python-format
msgid "Offensive Post"
msgstr "Messaggio offensivo"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "On average,"
msgstr "In media,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Only one answer per question is allowed"
msgstr "È consentita una sola risposta per ciascuna domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "Opzioni"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Order and Visibility"
msgstr "Ordinamento e visibilità"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Order by"
msgstr "Ordina per"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Our forums"
msgstr "I nostri forum"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Condizionato dal gruppo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "People"
msgstr "Persone"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "Contenuto non formattato"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""
"Prima di continuare, attendere che un moderatore convalidi il messaggio "
"precedente."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Domanda popolare"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Domanda popolare (150)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Domanda popolare (250)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Domanda popolare (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr "Voto positivo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "Messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr "Pubblica risposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "Risposte al messaggio"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reasons_action
msgid "Post Close Reasons"
msgstr "Motivi per chiusura messaggio"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Motivo chiusura messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Post Comment"
msgstr "Pubblica commento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Post Count"
msgstr "Numero messaggi"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "Voto al messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Pubblica domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Post:"
msgstr "Messaggio:"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "Pubblicati 10 commenti"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "Pubblicati 100 commenti"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""
"Impossibile pubblicare la risposta a un quesito [Eliminato] o [Chiuso]."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_posts
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Posts"
msgstr "Messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Posts:"
msgstr "Messaggi:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
msgid "Privacy"
msgstr "Privacy"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
msgid "Public"
msgstr "Pubblico"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"Pubblico: il forum è pubblico\n"
"Con accesso: il forum è visibile agli utenti che hanno effettuato l'accesso\n"
"Alcuni utenti: il forum e i suoi contenuti vengono nascosti a chi non è iscritto al gruppo selezionato"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Esperto"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your answer here."
msgstr "Scrivi qui la tua risposta."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your question here."
msgstr "Poni qui la tua domanda."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question"
msgstr "Domanda"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr "Domanda modificata"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question by"
msgstr "Domanda di"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Domanda votata negativamente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Domanda non trovata."

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Domanda indicata come preferita da 1 utente"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Domanda indicata come preferita da 25 utenti"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Domanda indicata come preferita da 5 utenti"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Question should not be empty."
msgstr "La domanda non deve essere vuota."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Domanda votata positivamente"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "Domanda votata positivamente 15 volte"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "Domanda votata positivamente 4 volte"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "Domanda votata positivamente 6 volte"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "Domande"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "Domande (1 risposta)"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"Modalità domande: consentita una sola risposta\n"
" Modalità discussioni: consentite risposte multiple"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "Discorso razzista e sprezzante"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "Livelli"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Re: %s"
msgstr "Re: %s"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Read the guidelines to know how to gain karma."
msgstr "Leggere le linee guida per sapere come ottenere karma."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Read: #{question.name}"
msgstr "Leggi: #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "Motivo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "Tipologia motivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Reason:"
msgstr "- Motivo:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "Motivi"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "Ricevuti per la prima volta almeno 3 voti positivi per una domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "Respingi"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr "Registrati"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "Rilevanza"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr "Calcolo rilevanza"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Reopen"
msgstr "Riapri"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Reply should not be empty."
msgstr "La risposta non deve essere vuota."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "Risposta alla propria domanda"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr "Limita la pubblicazione a questo sito web."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the forum."
msgstr "Ritorna al forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Return to the question list."
msgstr "Ritorna all'elenco domande."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "Revisionato da"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Ottimizzato SEO"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "Salva modifiche"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "Studente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Search Tips"
msgstr "Suggerimenti di ricerca"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr "Ricerca nel messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "Ricerca..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Secondo parametro di rilevanza"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "See"
msgstr "Vedi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "Visualizza messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "Visualizza domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "Seleziona tutti"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Autodidatta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "Nome SEO"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"Condividi questo contenuto per incrementare le possibilità di comparire in "
"prima pagina e attirare più visitatori."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "Opzioni di condivisione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Show"
msgstr "Mostra"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Show Tags Starting By"
msgstr "Mostra etichette partendo da"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Showing results for"
msgstr "Risultati per"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Sign in"
msgstr "Accedi"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
msgid "Signed In"
msgstr "Con accesso"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Solved"
msgstr "Con soluzione"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "Alcuni utenti"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr "Per eseguire questa azione è necessario effettuare l'accesso"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr "Per segnalare un messaggio è necessario effettuare l'accesso"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to vote"
msgstr "Per votare è necessario effettuare l'accesso"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr "Gli utenti anonimi non possono scegliere la risposta corretta. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Questa domanda non è più disponibile."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sorry, we could not find any <b>%s</b> result <b>%s</b> %s%s%s."
msgstr "Nessun risultato <b>%s</b> trovato <b>%s</b> %s%s%s."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr "Non è possibile votare i propri messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "Segna come spam tutti i messaggi"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "Spam o pubblicità"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "Stato"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Domanda stellare"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Studente"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Sostenitore"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "Etichetta"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Nome etichetta già esistente."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Tags"
msgstr "Etichette"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Tags I Follow"
msgstr "Etichette seguite"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Tassonomista"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Insegnante"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "Anteprima"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""
"Campo tecnico che consente di promuovere una domanda. La scrittura del campo"
" attiva la modifica di write_date che riposizionerà il messaggio. La "
"scrittura diretta su write_date non è attualmente supportata, questo campo è"
" una soluzione temporanea."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "Thanks for posting!"
msgstr "Grazie del messaggio!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"L'obiettivo di questo sito è di creare una base rilevante di conoscenze che "
"risponda a domande relative a Odoo. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "La domanda è stata chiusa"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"Pertanto le domande e le risposte possono essere modificate, come pagine "
"wiki, da utenti esperti di questo sito per migliorare la qualità complessiva"
" dei contenuti per questa base di conoscenze. Tali privilegi sono concessi "
"in base al livello di karma dell'utente, con un livello sufficiente si "
"potranno ottenere gli stessi vantaggi. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers."
msgstr ""
"Questa comunità è per utenti, partner e programmatori professionisti e "
"appassionati."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"Questa comunità è per utenti, partner e programmatori professionisti e "
"appassionati. È possibile porre domande riguardo:"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and services.\n"
"                                        <br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""
"Questa comunità è per professionisti ed entusiasti dei nostri prodotti e servizi.\n"
"                                        <br>Condividi e discuti i migliori contenuti e le nuove idee di marketing, crea il tuo profilo personale e diventa insieme a noi un promotore migliore."

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Questa comunità è per professionisti ed entusiasti dei nostri prodotti e "
"servizi. Condividi e discuti i migliori contenuti e le nuove idee di "
"marketing, crea il tuo profilo personale e diventa insieme a noi un "
"promotore migliore."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Questa formula è utilizzata per ordinare in base alla rilevanza. La "
"variabile \"voti\" rappresenta il numero di voti per un messaggio, "
"\"giorni\" è il numero di giorni dalla sua creazione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "Questo forum è stato archiviato."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post can not be flagged"
msgstr "Questo messaggio non può essere segnalato"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post is already flagged"
msgstr "Questo messaggio è già stato segnalato"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and it's not published yet.<br/>\n"
"                Do you want <b>Accept</b> or <b>Reject</b> this post ?"
msgstr ""
"Il messaggio è ora in attesa di moderazione e non è ancora stato pubblicato.<br/>\n"
"                <b>Accetta</b> o <b>Rifiuta</b> il messaggio."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "Linguaggio minaccioso"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "Titolo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "Il titolo non può essere vuoto"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Title should not be empty."
msgstr "Il titolo non deve essere vuoto."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "A"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "To Validate"
msgstr "Da convalidare"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"Per evitare che la domanda sia segnalata ed eventualmente rimossa, non porre"
" domande soggettive dove …"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr "Commuta stato preferito"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "Troppo circoscritto"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "Troppo soggettivo e argomentativo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Toolbar with button groups"
msgstr "Barra degli strumenti con gruppi pulsanti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Topics"
msgstr "Argomenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Answers"
msgstr "Risposte totali"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Total Posts"
msgstr "Post in totale"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Views"
msgstr "Visualizzazioni totali"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "Voti totali"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Trending"
msgstr "Più rilevanti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Try searching for one or two words"
msgstr "Provare con la ricerca di una o due parole"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr "Senza risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Undelete"
msgstr "Annulla eliminazione"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr "Separare tutti i commenti"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr "Separare i propri commenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Unmark as Best Answer"
msgstr "Deseleziona come migliore risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unsolved"
msgstr "Senza soluzione"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "Aggiornamento di"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "Aggiornamento del"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr "Votare positivamente"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Domanda con voto positivo (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Domanda con voto positivo (15)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Domanda con voto positivo (4)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Domanda con voto positivo (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "Usa un titolo chiaro, esplicito e conciso"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "Utente"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_favorites
msgid "Users favorite posts"
msgstr "Messaggi preferiti degli utenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Validate"
msgstr "Convalida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "Convalida domanda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr "visualizzazione"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr "Visualizzazioni"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "Linguaggio violento"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "Voto"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists !"
msgstr "Voto già esistente."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "Voti"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "In attesa di convalida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "In attesa di convalida"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr "Messaggio in attesa"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "Sito web"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "Sito web / Forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_url
msgid "Website URL"
msgstr "URL sito web"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Meta descrizione sito web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta parole chiave sito web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Meta titolo sito web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Immagine Open Graph sito web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "Messaggio di benvenuto"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "Benvenuto!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"Quando una domanda o una risposta ottiene un voto positivo, l'utente che "
"l'ha pubblicata guadagna alcuni punti chiamati \"punti karma\". È una misura"
" approssimativa della fiducia riposta dalla comunità in questa persona e "
"servono anche per assegnare in modo graduale varie attività di moderazione."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You already have a pending post"
msgstr "È già presente un messaggio in attesa"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "You can share your question once it has been validated"
msgstr "Una volta convalidata, puoi condividere la domanda"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "You cannot create recursive forum posts."
msgstr "Impossibile creare messaggi ripetuti sul forum. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "Impossibile pubblicare una risposta vuota"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You have no posts in this forum (yet)."
msgstr "Non hai messaggi nel forum (per ora)."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "Ora puoi partecipare ai nostri forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "Per modificare le etichette è necessario avere karma sufficiente"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"Dovrebbero essere poste solo domande pratiche a cui è possibile dare una "
"risposta in base ai problemi riscontrati. Domande discorsive e inconcludenti"
"  diminuiscono l'utilità del sito e tolgono altre domande dalla prima "
"pagina."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You're not following any topic in this forum (yet).<br/>"
msgstr "Non stai seguendo alcun argomento nel forum (per ora).<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr "Risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "Preferito"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Closed]"
msgstr "[Chiuso]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Deleted]"
msgstr "[Eliminato]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Offensive]"
msgstr "[Offensivo]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "Accettare qualsiasi risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "activity date"
msgstr "data attività"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "and join this Forum"
msgstr "e aggiungiti al forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "breadcrumb"
msgstr "percorso di navigazione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr "da"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "Chiudere qualsiasi messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "Eliminare qualsiasi commento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "Eliminare qualsiasi domanda o risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "Eliminare i propri commenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "Votare negativamente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "e.g. Help"
msgstr "es. Aiuto"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "e.g. Technical Assistance"
msgstr "ad es. assistenza tecnica"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "e.g. When should I plant my tomatoes?"
msgstr "ad es. Quando dovrei seminare i pomodori?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr ""
"Modificare qualsiasi messaggio, visualizzare le segnalazioni offensive"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr ""
"tutte le risposte sono ugualmente valide: “Qual è il tuo  ______ preferito?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "Segnalare messaggi, chiudere le proprie domande"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "con motivazione:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"è stato pubblicata e richiede una convalida. Fare clic per accedere alla "
"domanda:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "'. Fare clic per accedere al messaggio:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "'. Fare clic per accedere alla domanda:"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "here"
msgstr "qui"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"come configurare o personalizzare Odoo per particolari esigenze lavorative,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "come sviluppare moduli per le proprie esigenze,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "come installare Odoo in una particolare infrastruttura,"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"aggiuntivi se verrà\n"
"        selezionata come quella corretta. Per scoprire cosa puoi fare con il karma fai clic"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your favourites"
msgstr "nei preferiti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your followed list"
msgstr "nell'elenco dei seguiti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your posts"
msgstr "nei miei messaggi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "Inserire link testuali, caricare file"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "instead."
msgstr "invece."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "è una invettiva mascherata da domanda: “_____ fa schifo, giusto?”"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "karma points"
msgstr "punti karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "matching \""
msgstr "che corrisponde a \""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most answered"
msgstr "più risposti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most voted"
msgstr "più votati"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "newest"
msgstr "più recenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "-"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"sui social network ottiene una risposta entro\n"
"        5 ore. Le domande condivise su due social network hanno"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "post"
msgstr "messaggio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "solved"
msgstr "con soluzione"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "domande su proposte di servizi Odoo particolari ecc."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "tag"
msgstr "come etichetta"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"non ci sono reali problemi da risolvere: “Sono curioso di sapere se altri la"
" pensano come me.”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "to partecipate"
msgstr "per partecipare"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "trending"
msgstr "più rilevanti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unanswered"
msgstr "senza risposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unsolved"
msgstr "senza soluzione"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "Votare positivamente, aggiungere commenti"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "using the"
msgstr "utilizza"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"viene posta una domanda aperta e ipotetica: “Che cosa accadrebbe se ______ "
"?”"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"qual è il modo migliore di usare Odoo per una particolare attività "
"lavorativa,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "xp"
msgstr "pe"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"la risposta è fornita insieme alla domanda, e si attendono ulteriori "
"risposte: “Io uso_____ per ______, voi cosa usate”?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "La propria biografia può essere visualizzata come suggerimento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "| Flagged"
msgstr "| Segnalato"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "圾 Text"
msgstr "圾 Testo"
