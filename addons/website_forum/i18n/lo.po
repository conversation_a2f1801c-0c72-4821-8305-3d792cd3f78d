# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:45+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: ສີສຸວັນ ສັງບົວບຸລົມ <<EMAIL>>, 2023\n"
"Language-Team: Lao (https://app.transifex.com/odoo/teams/41243/lo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lo\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid " Flag"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid " Flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "# Answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "# Views"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to accept or refuse an answer."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to answer a question."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to close or reopen a post."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to comment."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert a comment to an answer."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert an answer to a comment."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert your comment to an answer."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new Tag."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new question."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to delete or reactivate a post."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to downvote."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to edit a post."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to flag a post."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to mark a post as offensive."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to post an image or link."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to refuse a post."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to retag."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a comment."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a post."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to upvote."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to validate a post."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "&amp;nbsp;and&amp;nbsp;"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ", by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ", consider <b>adding an example</b>."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "0 Answers"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "45% of questions shared"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b> [Offensive]</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<b>Tags</b> I Follow"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"<b>This forum is empty.</b><br/>\n"
"                    Be the first one asking a question"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What kinds of questions can I ask here?</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my answers?</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my questions?</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy bg-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<em class=\"d-block mb-2\">or</em>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\" fa fa-comment text-muted me-1\"/>Comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-bell fa-fw\"/> Followed Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-bug\"/> Filter Tool"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Accept"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>You've Completely Caught Up!</b><br/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-check-square-o fa-fw\"/> To Validate"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<i class=\"fa fa-chevron-left me-1\"/>Back"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to All Topics"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"fa fa-chevron-left small\"/> Back"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-flag fa-fw\"/> Flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-flag ms-4 mr4\"/>\n"
"                                    Flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-question-circle-o fa-fw\"/> My Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-reply me-1\"/>Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-share-alt text-muted me-1\"/>Share"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Share"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-star fa-fw\"/> Favourites"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-tags fa-fw\"/> Followed Tags"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw me-1\"/>Reject"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"fw-bold\">Votes</small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span class=\"badge text-bg-info\">Closed</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy bg-bronze ms-2\" role=\"img\" aria-label=\"Bronze"
" badge\" title=\"Bronze badge\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy bg-silver ms-2\" role=\"img\" aria-label=\"Silver"
" badge\" title=\"Silver badge\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"fw-bold\">No answer posted yet.</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"fw-bold\">No question posted yet.</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1\">|</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<span class=\"navbar-text me-1\">Go to:</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<span class=\"navbar-text me-3\">Show Tags Starting By</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill fw-bold text-success ms-2 px-2\">\n"
"                            Best Answer\n"
"                        </span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>By </span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "A clear, explicit and concise title"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "About"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Access Denied"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "ໃຊ້ຢູ່"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "ກິດຈຳກຳ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "ກິດຈະກຳ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "ທັງໝົດ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All Forums"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "All Tags"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All forums"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Amazing! There are no unanswered questions left!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Answer"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "ສຳເນົາໄວ້ແລ້ວ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Avatar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Back"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Bad Request"
msgstr ""

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Badges"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Be less specific in your wording for a wider search result"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Browse All"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_use_full_editor
msgid "Can Use Full Editor"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Check your spelling and try again"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click here to accept this answer."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to answer."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your answer."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your question."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Close"
msgstr "ປິດອອກ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Close Post"
msgstr ""

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Close post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
msgid "Closed"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Closing"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comments"
msgstr ""

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as a answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "ວັນທີສ້າງ"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Create a new post in this forum by clicking on the button."
msgstr ""

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "ສ້າງໂດຍ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "ສ້າງເມື່ອ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "ວັນທີ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "ຄ່າເລີ່ມຕົ້ນ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Default Sort"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Delete"
msgstr "ລຶບ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Deleted"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "ຄຳອະທິບາຍ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Description visible on website"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Discard"
msgstr "ປະລະ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "ຊື່ເຕັມ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Edit"
msgstr "ແກ້ໄຂ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Edit Forum in Backend"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
#: model:ir.model.fields,help:website_forum.field_forum_post__can_use_full_editor
msgid "Editor Features: image and links"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                        <i class=\"fa fa-question-circle\"/>"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Favourites"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Following"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/website.py:0
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#, python-format
msgid "Forum"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Mode"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Name"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Forum Page"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
msgid "Forum Post Pages"
msgstr ""

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_pages
msgid "Forum Posts"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forums_count
msgid "Forums Count"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Give your post title."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Grid"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "ຈັດຂໍ້ມູນຕາມ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Guidelines"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr ""

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "I'm <b>Following</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "I'm Following"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ເລກລຳດັບ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "ຮູບພາບ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Insert tags related to your question."
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to modify someone else's vote."
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Karma"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Karma Error"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "ແກ້ໄຂລ້າສຸດເມື່ອ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Last Post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Last Post:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__write_date_desc
msgid "Last Updated"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "ປັບປຸງລ້າສຸດໂດຍ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "ປັບປຸງລ້າສຸດເມື່ອ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Layout"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "List"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Mark as Best Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Mark as Offensive"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__menu_id
msgid "Menu"
msgstr "ລາຍການຄຳສັ່ງ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Moderation tools"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "More"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Favourites</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Posts</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Favourites"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "My profile"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "ຊື່"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr ""

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action_add
msgid "New Forum"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Post"
msgstr ""

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Newest"
msgstr "ໃໝ່ສຸດ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "No activities yet!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "No favourite questions in this forum (yet).<br/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No flagged posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No post to be validated"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "No tags"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "No vote given by you yet!"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Offensive"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.header
#, python-format
msgid "Offensive Post"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "On average,"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Only one answer per question is allowed"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "ທາງເລືອກ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Order and Visibility"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Order by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Our forums"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "People"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reasons_action
msgid "Post Close Reasons"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Post Comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Post Count"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Post:"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_posts
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Posts:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
msgid "Privacy"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
msgid "Public"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your answer here."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your question here."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Question should not be empty."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr ""

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Re: %s"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Read the guidelines to know how to gain karma."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Read: #{question.name}"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Reason:"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr ""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Reopen"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Reply should not be empty."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the forum."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Return to the question list."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Search Tips"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "See"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "ລຳດັບ"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Show"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Show Tags Starting By"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Showing results for"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Sign in"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
msgid "Signed In"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Solved"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to vote"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sorry, we could not find any <b>%s</b> result <b>%s</b> %s%s%s."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "ສະຖານະພາບ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "ເປົ້າໝາຍ"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists !"
msgstr "ຊື່ເປົ້າໝາຍນີ້ມີແລ້ວ!"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Tags"
msgstr "ເປົ້າໝາຍ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Tags I Follow"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "Thanks for posting!"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and services.\n"
"                                        <br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post can not be flagged"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post is already flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and it's not published yet.<br/>\n"
"                Do you want <b>Accept</b> or <b>Reject</b> this post ?"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "ຫົວເລື່ອງ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Title should not be empty."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "To Validate"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Toolbar with button groups"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Topics"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Answers"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Total Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Views"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Trending"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Try searching for one or two words"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Undelete"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Unmark as Best Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unsolved"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "ຜູ້ໃຊ້"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_favorites
msgid "Users favorite posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Validate"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr "ເບຶ່ງ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr ""

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists !"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr ""

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_url
msgid "Website URL"
msgstr "ທີ່ຢູ່ເວັບໄຊຕ໌ URL"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "ລາຍລະອຽດ ເມຕາ ເວັບໄຊຕ໌"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "ຄຳໄຂລະຫັດ ເມຕາ ເວັບໄຊຕ໌"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "ຫົວເລື່ອງ ເມຕາ ເວັບໄຊຕ໌"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You already have a pending post"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "You can share your question once it has been validated"
msgstr ""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "You cannot create recursive forum posts."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You have no posts in this forum (yet)."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You're not following any topic in this forum (yet).<br/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Closed]"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Deleted]"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Offensive]"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "activity date"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "and join this Forum"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "breadcrumb"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "e.g. Help"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "e.g. Technical Assistance"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "e.g. When should I plant my tomatoes?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "here"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your favourites"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your followed list"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "instead."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid "karma points"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "matching \""
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most voted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "newest"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr ""

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "solved"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "tag"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "to partecipate"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "trending"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unanswered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unsolved"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "using the"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "xp"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "| Flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "圾 Text"
msgstr ""
