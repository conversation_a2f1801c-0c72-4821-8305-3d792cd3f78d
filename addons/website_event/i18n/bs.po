# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:07+0000\n"
"PO-Revision-Date: 2018-10-02 10:07+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "(Free)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.brand_promotion
msgid ""
".\n"
"            Try the"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>End of Registration</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>Price</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>Quantity</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>Ticket Type</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>Upcoming Events</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"<br/>\n"
"                    <i class=\"fa fa-clock-o\"/> To"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<em><i class=\"fa fa-plus-circle\" role=\"img\" aria-label=\"Create\" title=\"Create\"/>\n"
"                    Configure and Launch Event Registration</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-clock-o\"/> From"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_country_event
msgid ""
"<i class=\"fa fa-flag fa-5x\"/>\n"
"                            Events from Your Country"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to Google Calendar"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to iCal/Outlook"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i>to</i>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid "<small>Author</small>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "<span class=\"oe_snippet_thumbnail_title\">Local Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\">\n"
"                        Sold Out\n"
"                    </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span itemprop=\"name\">Event Registration</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"price\" content=\"0\">\n"
"                        Free\n"
"                    </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span>Unlimited</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Attendees</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Email</strong>"
msgstr "<strong>Email</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Name</strong>"
msgstr "<strong>Naziv</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<strong>Participate on Twitter</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Phone</strong> <span class=\"text-muted\">(Optional)</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid "<strong>Ticket Type:</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<strong>Use this tag:</strong>"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:101
#, python-format
msgid "All Categories"
msgstr "Sve kategorije"

#. module: website_event
#: code:addons/website_event/controllers/main.py:108
#, python-format
msgid "All Countries"
msgstr "Sve zemlje"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "All Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Ask questions to attendees when registering online"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendee #%s"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel Registration"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form_inherit_website
msgid ""
"Check this option to have menus for your event on the\n"
"                                website: registrations, schedule, map, ..."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:24
#, python-format
msgid "Click <em>Continue</em> to create the event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:15
#, python-format
msgid "Click here to create a new event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:43
#, python-format
msgid "Click here to customize your event further."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:38
#, python-format
msgid "Click to publish your event."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "Zatvori"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Continue"
msgstr "Nastavi"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:19
#, python-format
msgid ""
"Create a name for your new event and click <em>\"Continue\"</em>. e.g: "
"Technical Training"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid ""
"Creates menus Introduction, Location and Register on the page  of the event "
"on the website."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Dedicated Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:28
#, python-format
msgid "Drag this block and drop it in your page."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "E-mail"
msgstr "E-mail"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid "End of Registration"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
msgid "Event"
msgstr "Dogadaj"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Category"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Event Details"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "Event Introduction"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:31
#, python-format
msgid "Event Name"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event registration is closed."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event registration not yet started."
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr ""

#. module: website_event
#: model:website.menu,name:website_event.menu_events
msgid "Events"
msgstr "Događaji"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "Events in visitor's country"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Events:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event,\n"
"                            and join the conversation."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google Maps"
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:70
#, python-format
msgid "Introduction"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
msgid "Is published"
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:71
#, python-format
msgid "Location"
msgstr "Lokacija"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Name"
msgstr "Naziv:"

#. module: website_event
#. openerp-web
#: code:addons/website_event/controllers/main.py:200
#: code:addons/website_event/static/src/js/website_event.editor.js:30
#, python-format
msgid "New Event"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:39
#, python-format
msgid "Next Events"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:48
#, python-format
msgid "Next Week"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:56
#, python-format
msgid "Next month"
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:150
#, python-format
msgid "No date has been specified for the event, no file will be generated."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "No event found"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.brand_promotion
msgid "Odoo"
msgstr "Odoo"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:33
#, python-format
msgid "Once you click on save, your event is updated."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Online"
msgstr "Na mreži"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Organized by:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizator"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Our Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Our Trainings"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Participating"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:60
#, python-format
msgid "Past Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Phone"
msgstr "Telefon"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Photos of Past Events"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.js:55
#, python-format
msgid "Please select at least one ticket."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.brand_promotion
msgid "Powered by"
msgstr "Podržano od strane"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_inherit_website
msgid "Preview Badges"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid "Price"
msgstr "Cijena"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid "Quantity"
msgstr "Količina"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Questions"
msgstr "Pitanja"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Reference"
msgstr "Referenca"

#. module: website_event
#: code:addons/website_event/models/event.py:72
#, python-format
msgid "Register"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register Now"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Registration for this event is now closed"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all upcoming events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Social Stream"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:44
#, python-format
msgid "This Week"
msgstr "Ove sedmice"

#. module: website_event
#: code:addons/website_event/controllers/main.py:52
#, python-format
msgid "This month"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket Type #"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:40
#, python-format
msgid "Today"
msgstr "Danas"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Use the top menu <i>'Content'</i> to create your first event."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Visualize on Google Maps"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "We are glad to confirm your registration to our event"
msgstr ""

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_inherit_website
msgid "Website Menu"
msgstr "Website Meni"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "When"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Where"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid ""
"Write here a quote from one of your attendees.\n"
"                        It gives confidence in your\n"
"                        events."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Your registration"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.brand_promotion
msgid "open source website builder"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "to"
msgstr "za"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "unpublished"
msgstr ""
