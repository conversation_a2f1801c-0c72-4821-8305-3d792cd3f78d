# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-30 09:42+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>Event registration is closed.</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<b>Event registration not yet started.</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>View all</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<br/> <b>End</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check mr-2\"/>Registered"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<i class=\"fa fa-flag mr-2\"/>Events:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
#: model_terms:ir.ui.view,arch_db:website_event.s_country_events
msgid "<i class=\"fa fa-globe mr-2\"/>Upcoming Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>All Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<i class=\"fa fa-pencil mr-2\" role=\"img\" aria-label=\"Create\" "
"title=\"Create\"/><em>Configure Event Registration</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid "<span class=\"font-weight-bold align-middle pr-2\">Qty</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 mr-4\">Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<span content=\"0\" class=\"font-weight-bold text-uppercase\">Free</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span content=\"0\" itemprop=\"price\" class=\"font-weight-bold text-"
"uppercase\">Free</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.ticket
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                        <i class=\"fa fa-ban mr-2\"/>Sold Out\n"
"                    </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<span>&amp;times;</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_category
#, python-format
msgid "All Categories"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Ask questions to attendees when registering online"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_categories
msgid "Categories"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form_inherit_website
msgid ""
"Check this option to have menus for your event on the\n"
"                                website: registrations, schedule, map, ..."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid "Click <em>Continue</em> to create the event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid "Click here to create a new event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid "Click here to customize your event further."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid "Click to publish your event."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Continue"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid ""
"Create a name for your new event and click <em>\"Continue\"</em>. e.g: "
"Technical Training"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid ""
"Creates menus Introduction, Location and Register on the page  of the event "
"on the website."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Dedicated Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid "Drag this block and drop it in your page."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "E-mail"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Email"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "End on"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
msgid "Event"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Category"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Event Name"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr ""

#. module: website_event
#: model:website.menu,name:website_event.menu_events
msgid "Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "Follow Us"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Hashtag:"
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
#, python-format
msgid "Introduction"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Name"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/controllers/main.py:0
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "New Event"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Next Events"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Next Week"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Next month"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/website_event.js:0
#, python-format
msgid "Once you click on save, your event is updated."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Online"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Organizer:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Past Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Phone"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Phone <small>(Optional)</small>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Photos"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Please select at least one ticket."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_inherit_website
msgid "Preview Badges"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Questions"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Reference"
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.ticket
#, python-format
msgid "Register"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This Week"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This month"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Today"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Unpublished"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragrah to write a short text about your events or company."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_inherit_website
msgid "Website Menu"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "o_wevent_sidebar_social mx-n1"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "o_wevent_social_link"
msgstr ""
