# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-11 19:59+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event
#: code:addons/website_event/controllers/main.py:219
#, python-format
msgid "%(month)s %(start_day)s%(end_day)s"
msgstr "%(month)s %(start_day)s%(end_day)s"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>Upcoming Events</b>"
msgstr "<b>Próximos Eventos</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"<br/>\n"
"                    <i class=\"fa fa-clock-o\"/> To"
msgstr ""
"<br/>\n"
"                    <i class=\"fa fa-clock-o\"/> Hasta"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<em>Configure and Launch Event Registration</em>"
msgstr "<em>Configurar y Lanzar Inscripción al Evento</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-clock-o\"/> From"
msgstr "<i class=\"fa fa-clock-o\"/> Desde"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i>to</i>"
msgstr "<i>a</i>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid "<small>Author</small>"
msgstr "<small>Autor</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "<span class=\"oe_snippet_thumbnail_title\">Local Events</span>"
msgstr "<span class=\"oe_snippet_thumbnail_title\">Eventos Locales</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Eventos En Línea</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span>Unlimited</span>"
msgstr "<span>Ilimitado</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Attendees</strong>"
msgstr "<strong>Asistentes</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Email</strong>"
msgstr "<strong>Correo</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Name</strong>"
msgstr "<strong>Nombre</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<strong>Participate on Twitter</strong>"
msgstr "<strong>Participar en Twitter</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Phone</strong> <span class=\"text-muted\">(Optional)</span>"
msgstr "<strong>Teléfono</strong> <span class=\"text-muted\">(Opcional)</span>"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:25
#, python-format
msgid "Add Content"
msgstr "Añadir Contenido"

#. module: website_event
#: code:addons/website_event/controllers/main.py:106
#, python-format
msgid "All Categories"
msgstr "Todas las Categorías"

#. module: website_event
#: code:addons/website_event/controllers/main.py:117
#, python-format
msgid "All Countries"
msgstr "Todos los Países"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "All Events"
msgstr "Todos los Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendee #%s"
msgstr "Asistente #%s"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel Registration"
msgstr "Cancelar Inscripción"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:48
#, python-format
msgid "Click <em>Continue</em> to create the event."
msgstr "Clic en <em>Siguiente</em> para crear el evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:33
#, python-format
msgid "Click here to create a new event."
msgstr "Clic aquí para crear un nuevo evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:82
#, python-format
msgid "Click here to customize your event further."
msgstr "Clic aquí para personalizar más su evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:75
#, python-format
msgid "Click to publish your event."
msgstr "Clic para publicar su evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:54
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Continue"
msgstr "Siguiente"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:47
#, python-format
msgid "Create Event"
msgstr "Crear Evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:41
#, python-format
msgid ""
"Create a name for your new event and click <em>'Continue'</em>. e.g: "
"Technical Training"
msgstr ""
"Cree un nombre para su evento y haga clic en <em>'Siguiente'</em>. ej. "
"Entrenamiento Técnico"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:18
#, python-format
msgid "Create an Event"
msgstr "Crear un Evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:40
#, python-format
msgid "Create an Event Name"
msgstr "Crear un Nombre de Evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:15
#, python-format
msgid "Create an event"
msgstr "Crear un evento"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event_show_menu
msgid ""
"Creates menus Introduction, Location and Register on the page  of the event "
"on the website."
msgstr ""
"Crea menús de Introducción, Ubicación e Inscripción en la página del evento "
"en el sitio web."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:81
#, python-format
msgid "Customize your event"
msgstr "Personalizar su evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_show_menu
msgid "Dedicated Menu"
msgstr "Menú Dedicado"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:59
#, python-format
msgid "Drag & Drop a block"
msgstr "Arrastre y Suelte un bloque"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:60
#, python-format
msgid "Drag the 'Image-Text' block and drop it in your page."
msgstr "Arrastre el bloque 'Imagen-Texto' y suéltelo en su página."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "E-mail"
msgstr "Correo"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.view_event_sale_form
msgid "Edit Badges"
msgstr "Editar Presupuestos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "End of Subscription"
msgstr "Fin de la Suscripción"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
msgid "Event"
msgstr "Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Event Details"
msgstr "Detalles del Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "Event Introduction"
msgstr "Introducción del Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Ubicación del Evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_menu_id
msgid "Event Menu"
msgstr "Menú del Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event Subscription"
msgstr "Suscripción al Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Evento no encontrado!"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Evento publicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event registration not yet started."
msgstr "La inscripción al evento no ha comenzado aún."

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Evento no publicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.header_footer_custom
#: model:website.menu,name:website_event.menu_events
msgid "Events"
msgstr "Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_country_event
msgid "Events from Your Country"
msgstr "Eventos en su País"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "Events in visitor's country"
msgstr "Eventos en el país del visitante"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Events:"
msgstr "Eventos:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event,\n"
"                            and join the conversation."
msgstr ""
"Encuentre lo que la gente ve y dice sobre este evento, y únase a la "
"conversación."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Free"
msgstr "Libre"

#. module: website_event
#: code:addons/website_event/models/event.py:44
#, python-format
msgid "Introduction"
msgstr "Introducción"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:19
#, python-format
msgid "Let's go through the first steps to publish a new event."
msgstr "Vayamos a través de los primeros pasos para publicar un nuevo evento."

#. module: website_event
#: code:addons/website_event/models/event.py:45
#, python-format
msgid "Location"
msgstr "Ubicación"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr "N/A"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Name"
msgstr "Nombre"

#. module: website_event
#. openerp-web
#: code:addons/website_event/controllers/main.py:201
#: code:addons/website_event/static/src/js/website.tour.event.js:32
#: code:addons/website_event/static/src/js/website_event.editor.js:14
#: model_terms:ir.ui.view,arch_db:website_event.content_new_event
#, python-format
msgid "New Event"
msgstr "Nuevo Evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:52
#, python-format
msgid "New Event Created"
msgstr "Nuevo Evento Creado"

#. module: website_event
#: code:addons/website_event/controllers/main.py:38
#, python-format
msgid "Next Events"
msgstr "Próximos Eventos"

#. module: website_event
#: code:addons/website_event/controllers/main.py:47
#, python-format
msgid "Next Week"
msgstr "Próxima Semana"

#. module: website_event
#: code:addons/website_event/controllers/main.py:55
#, python-format
msgid "Next month"
msgstr "Próximo mes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "No event found"
msgstr "No se encontró ningún evento"

#. module: website_event
#: code:addons/website_event/controllers/main.py:59
#, python-format
msgid "Old Events"
msgstr "Eventos Pasados"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:67
#, python-format
msgid "Once you click on save, your event is updated."
msgstr "Una vez haga click en guardar, su evento será actualizado."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Online"
msgstr "En línea"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Organized by:"
msgstr "Organizado por:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizador"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Our Events"
msgstr "Nuestros Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Our Trainings"
msgstr "Nuestras Capacitaciones"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Phone"
msgstr "Teléfono"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Photos of Past Events"
msgstr "Fotos de Eventos Anteriores"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Price"
msgstr "Precio"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:74
#, python-format
msgid "Publish your event"
msgstr "Publicar su evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Quantity"
msgstr "Cantidad"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Reference"
msgstr "Referencia"

#. module: website_event
#: code:addons/website_event/models/event.py:53
#, python-format
msgid "Register"
msgstr "Inscribirse"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register Now"
msgstr "Regístrese Ahora"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Regresar a la lista de eventos."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:66
#, python-format
msgid "Save your modifications"
msgstr "Guardar sus cambios"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr "Ver todos los eventos de"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all upcoming events"
msgstr "Ver todos los próximos eventos"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:20
#, python-format
msgid "Skip It"
msgstr "Omitir"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Social Stream"
msgstr "Stream Social"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "Agotado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Lo sentimos, el evento solicitado no está ya disponible."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:20
#, python-format
msgid "Start Tutorial"
msgstr "Comenzar Tutorial"

#. module: website_event
#: code:addons/website_event/controllers/main.py:43
#, python-format
msgid "This Week"
msgstr "Esta Semana"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:26
#, fuzzy, python-format
msgid "This button allows you to create new pages, events, menus, etc."
msgstr ""
"El menú <em>Contenido</em> le permite crear nuevas páginas, eventos, menús, "
"etc."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:53
#, python-format
msgid "This is your new event page. We will edit the event presentation page."
msgstr ""
"Ésta es la página de su nuevo evento. A continuación editaremos la página de "
"presentación del evento."

#. module: website_event
#: code:addons/website_event/controllers/main.py:51
#, python-format
msgid "This month"
msgstr "Este mes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Type"
msgstr "Tipo de Entrada"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket Type #"
msgstr "Tipo de Entrada #"

#. module: website_event
#: code:addons/website_event/controllers/main.py:39
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_twitter_hashtag
msgid "Twitter Hashtag"
msgstr "Hashtag de Twitter"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Use the top menu <i>'Content'</i> to create your first event."
msgstr "Use el menú superior <i>'Contenido'</i> para crear su primer evento."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Use this tag:"
msgstr "Utilice esta etiqueta:"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_website_published
msgid "Visible in Website"
msgstr "Visible en el Sitio Web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "We are glad to confirm your subscription to our event"
msgstr "Nos complace confirmar su suscripción a nuestro evento"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Página de Inicio del Sitio Web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "When"
msgstr "Cuando"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Where"
msgstr "Donde"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid ""
"Write here a quote from one of your attendees.\n"
"                        It gives confidence in your\n"
"                        events."
msgstr ""
"Escriba aquí una cita de uno de los asistentes.\n"
"Eso dará credibilidad a sus\n"
"eventos."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Your subscription"
msgstr "Su suscripción"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "not published"
msgstr "no publicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "or"
msgstr "o"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "pull-right"
msgstr "pull-right"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "to"
msgstr "a"

#~ msgid "Drag the 'Text Block' in your event page."
#~ msgstr "Arrastre el bloque 'Texto' en la página de su evento."

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través del sitio web."

#~ msgid "Website Messages"
#~ msgstr "Mensajes del Sitio Web"

#~ msgid "Website URL"
#~ msgstr "URL del Sitio Web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicación del sitio web"

#~ msgid "Website meta description"
#~ msgstr "Meta descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta palabras clave del sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta título del sitio web"
