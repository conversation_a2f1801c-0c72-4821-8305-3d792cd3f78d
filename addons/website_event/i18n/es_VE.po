# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-01 09:20+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Venezuela) (http://www.transifex.com/odoo/odoo-9/"
"language/es_VE/)\n"
"Language: es_VE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event
#: code:addons/website_event/controllers/main.py:219
#, python-format
msgid "%(month)s %(start_day)s%(end_day)s"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>Upcoming Events</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"<br/>\n"
"                    <i class=\"fa fa-clock-o\"/> To"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<em>Configure and Launch Event Registration</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-clock-o\"/> From"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i>to</i>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid "<small>Author</small>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "<span class=\"oe_snippet_thumbnail_title\">Local Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span>Unlimited</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Attendees</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Email</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Name</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<strong>Participate on Twitter</strong>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Phone</strong> <span class=\"text-muted\">(Optional)</span>"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:25
#, python-format
msgid "Add Content"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:106
#, python-format
msgid "All Categories"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:117
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "All Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendee #%s"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel Registration"
msgstr "Cancelar registro"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:48
#, python-format
msgid "Click <em>Continue</em> to create the event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:33
#, python-format
msgid "Click here to create a new event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:82
#, python-format
msgid "Click here to customize your event further."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:75
#, python-format
msgid "Click to publish your event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:54
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Continue"
msgstr "Continuar"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:47
#, python-format
msgid "Create Event"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:41
#, python-format
msgid ""
"Create a name for your new event and click <em>'Continue'</em>. e.g: "
"Technical Training"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:18
#, python-format
msgid "Create an Event"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:40
#, python-format
msgid "Create an Event Name"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:15
#, python-format
msgid "Create an event"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event_show_menu
msgid ""
"Creates menus Introduction, Location and Register on the page  of the event "
"on the website."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:81
#, python-format
msgid "Customize your event"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_show_menu
msgid "Dedicated Menu"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:59
#, python-format
msgid "Drag & Drop a block"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:60
#, python-format
msgid "Drag the 'Image-Text' block and drop it in your page."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "E-mail"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.view_event_sale_form
msgid "Edit Badges"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "End of Subscription"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
msgid "Event"
msgstr "Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Event Details"
msgstr "Detalles de evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "Event Introduction"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_menu_id
msgid "Event Menu"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event Subscription"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event registration not yet started."
msgstr ""

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.header_footer_custom
#: model:website.menu,name:website_event.menu_events
msgid "Events"
msgstr "Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_country_event
msgid "Events from Your Country"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "Events in visitor's country"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Events:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event,\n"
"                            and join the conversation."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Free"
msgstr "Libre"

#. module: website_event
#: code:addons/website_event/models/event.py:44
#, python-format
msgid "Introduction"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:19
#, python-format
msgid "Let's go through the first steps to publish a new event."
msgstr ""

#. module: website_event
#: code:addons/website_event/models/event.py:45
#, python-format
msgid "Location"
msgstr "Lugar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Name"
msgstr "Nombre"

#. module: website_event
#. openerp-web
#: code:addons/website_event/controllers/main.py:201
#: code:addons/website_event/static/src/js/website.tour.event.js:32
#: code:addons/website_event/static/src/js/website_event.editor.js:14
#: model_terms:ir.ui.view,arch_db:website_event.content_new_event
#, python-format
msgid "New Event"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:52
#, python-format
msgid "New Event Created"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:38
#, python-format
msgid "Next Events"
msgstr "Próximos eventos"

#. module: website_event
#: code:addons/website_event/controllers/main.py:47
#, python-format
msgid "Next Week"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:55
#, python-format
msgid "Next month"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "No event found"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:59
#, python-format
msgid "Old Events"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:67
#, python-format
msgid "Once you click on save, your event is updated."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Online"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Organized by:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizador"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Our Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Our Trainings"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Phone"
msgstr "Teléfono"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Photos of Past Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Price"
msgstr "Precio"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:74
#, python-format
msgid "Publish your event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Quantity"
msgstr "Cantidad"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Reference"
msgstr "Referencia"

#. module: website_event
#: code:addons/website_event/models/event.py:53
#, python-format
msgid "Register"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register Now"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:66
#, python-format
msgid "Save your modifications"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all upcoming events"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:20
#, python-format
msgid "Skip It"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Social Stream"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:20
#, python-format
msgid "Start Tutorial"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:43
#, python-format
msgid "This Week"
msgstr "Esta semana"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:26
#, python-format
msgid "This button allows you to create new pages, events, menus, etc."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:53
#, python-format
msgid "This is your new event page. We will edit the event presentation page."
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:51
#, python-format
msgid "This month"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Type"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket Type #"
msgstr ""

#. module: website_event
#: code:addons/website_event/controllers/main.py:39
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_twitter_hashtag
msgid "Twitter Hashtag"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Use the top menu <i>'Content'</i> to create your first event."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Use this tag:"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_website_published
msgid "Visible in Website"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "We are glad to confirm your subscription to our event"
msgstr ""

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "When"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Where"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid ""
"Write here a quote from one of your attendees.\n"
"                        It gives confidence in your\n"
"                        events."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Your subscription"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "not published"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "or"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "pull-right"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "to"
msgstr "hasta"
