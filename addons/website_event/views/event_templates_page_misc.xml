<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Multipage event - Default template when creating a new page -->
<template id="default_page">
    <t t-call="website.layout">
        <div class="oe_structure oe_empty"/>
    </t>
</template>

<!-- Multipage event - Default template for the Introduction page -->
<template id="template_intro">
    <t t-call="website_event.layout">
        <div class="oe_structure oe_empty" id="oe_structure_website_event_intro_1"/>
        <section class="s_title pt32 pb32" data-vcss="001" data-snippet="s_title" data-name="Title">
            <div class="container s_allow_columns">
                <h1 style="text-align: center;">
                    <font style="font-size: 62px;" class="o_default_snippet_text">Introduction</font>
                </h1>
            </div>
        </section>
        <div class="oe_structure oe_empty" id="oe_structure_website_event_intro_2"/>
    </t>
</template>

<!-- Multipage event - Default template for the Location page -->
<template id="template_location">
    <t t-call="website_event.layout">
        <div class="oe_structure" id="oe_structure_website_event_location_1"/>
        <section class="pt32 pb32">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h1 class="o_page_header mb-3">Event Location</h1>
                        <h4 class="mb-3" t-field="event.address_id" t-options='{
                            "widget": "contact",
                            "fields": ["name"],
                        }'/>
                        <div class="mb-3" t-field="event.address_id" t-options='{
                            "widget": "contact",
                            "fields": ["address"],
                            "no_marker": True
                        }'/>
                        <div class="mb-3" t-field="event.address_id" t-options='{
                            "widget": "contact",
                            "fields": ["phone", "mobile", "email"],
                            "no_marker": True
                        }'/>
                    </div>
                </div>
            </div>
        </section>
        <div class="oe_structure" id="oe_structure_website_event_location_2"/>
    </t>
</template>

<template id="404" name="Event 404">
    <t t-call="website.layout">
        <div id="wrap">
            <div class="oe_structure oe_empty">
                <div class="container">
                    <h1 class="mt-4">Event not found!</h1>
                    <p>Sorry, the requested event is not available anymore.</p>
                    <p><a t-attf-href="/event">Return to the event list.</a></p>
                </div>
            </div>
        </div>
    </t>
</template>

</odoo>
