<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data>

    <record id="event_booth_category_view_form" model="ir.ui.view">
        <field name="name">event.booth.category.view.form.inherit.website.event.booth.exhibitor</field>
        <field name="model">event.booth.category</field>
        <field name="inherit_id" ref="event_booth.event_booth_category_view_form"/>
        <field name="priority" eval="2"/>
        <field name="arch" type="xml">
            <group name="main" position="inside">
                <group string="Sponsorship" name="sponsor">
                    <field name="use_sponsor"/>
                    <field name="sponsor_type_id" attrs="{'invisible': [('use_sponsor', '=', False)], 'required': [('use_sponsor', '=', True)]}"/>
                    <field name="exhibitor_type" attrs="{'invisible': [('use_sponsor', '=', False)], 'required': [('use_sponsor', '=', True)]}"/>
                </group>
            </group>
        </field>
    </record>

    <record id="event_booth_category_view_tree" model="ir.ui.view">
        <field name="name">event.booth.category.view.tree.inherit.website.event.booth.exhibitor</field>
        <field name="model">event.booth.category</field>
        <field name="inherit_id" ref="event_booth.event_booth_category_view_tree"/>
        <field name="priority">5</field>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="use_sponsor"/>
                <field name="sponsor_type_id" optional="hide"/>
                <field name="exhibitor_type" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="event_booth_category_view_search" model="ir.ui.view">
        <field name="name">event.booth.category.view.search.inherit.website.event.booth.exhibitor</field>
        <field name="model">event.booth.category</field>
        <field name="inherit_id" ref="event_booth.event_booth_category_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="use_sponsor"/>
                <field name="sponsor_type_id"/>
                <field name="exhibitor_type"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_sponsor_type" string="Sponsor type" context="{'group_by': 'sponsor_type_id'}"/>
                    <filter name="group_by_exhibitor_type" string="Exhibitor type" context="{'group_by':'exhibitor_type'}"/>
                </group>
            </xpath>
        </field>
    </record>

</data></odoo>
