# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_hr_recruitment
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Man<PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_job.py:0
#, python-format
msgid ""
"\n"
"            <span class=\"text-muted small\">Time to Answer</span>\n"
"            <h6>2 open days</h6>\n"
"            <span class=\"text-muted small\">Process</span>\n"
"            <h6>1 Phone Call</h6>\n"
"            <h6>1 Onsite Interview</h6>\n"
"            <span class=\"text-muted small\">Days to get an Offer</span>\n"
"            <h6>4 Days after Interview</h6>\n"
"        "
msgstr ""
"\n"
"            <span class=\"text-muted small\">Du temps pour répondre</span>\n"
"            <h6>2 portes ouvertes</h6>\n"
"            <span class=\"text-muted small\">Processus</span>\n"
"            <h6>1 Appel</h6>\n"
"            <h6>1 Entretien sur place</h6>\n"
"            <span class=\"text-muted small\">Jours pour recevoir une offre</span>\n"
"            <h6>4 Jours après un entretien</h6>\n"
"        "

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "%s's Application"
msgstr "Candidature de %s"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "12 days / year, including <br/>6 of your choice."
msgstr "12 jours / an, y compris <br/>6 de votre choix."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""
"<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Téléphone\" "
"title=\"Téléphone\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<i class=\"fa fa-arrow-left\"/> Job Description"
msgstr "<i class=\"fa fa-arrow-left\"/> Description du poste"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"E-mail\" title=\"E-mail\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Publication date\" role=\"img\" "
"aria-label=\"Publication date\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Date de publication\" role=\"img\" "
"aria-label=\"Date de publication\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_kanban_referal_extends
msgid ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Job Page"
msgstr ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Page de l'offre d'emploi"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-suitcase\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Employment type\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-suitcase\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Type d'emploi\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Site web\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LIRE</b></small> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "<span class=\"bg-odoo\">Published</span>"
msgstr "<span class=\"bg-odoo\">Publié</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"d-none text-muted\" style=\"margin-left: 200px; font-size: "
"0.8rem\">The resume is optional if you have a Linkedin profile</span>"
msgstr ""
"<span class=\"d-none text-muted\" style=\"margin-left: 200px; font-size: "
"0.8rem\">Le CV est optionnel si vous avez un profil Linkedin</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Department</span>"
msgstr "<span class=\"s_website_form_label_content\">Départment</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Job</span>"
msgstr "<span class=\"s_website_form_label_content\">Poste</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">LinkedIn Profile</span>"
msgstr "<span class=\"s_website_form_label_content\">Profil LinkedIn</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Resume</span>"
msgstr "<span class=\"s_website_form_label_content\">Curriculum vitae</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Short Introduction</span>"
msgstr "<span class=\"s_website_form_label_content\">Brève introduction</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Votre e-mail</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Votre nom</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Phone Number</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Votre numéro de téléphone</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Department</span>"
msgstr "<span class=\"text-muted small\">Département</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Employment Type</span>"
msgstr "<span class=\"text-muted small\">Type d'emploi</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Job</span>"
msgstr "<span class=\"text-muted small\">Poste</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Location</span>"
msgstr "<span class=\"text-muted small\">Lieu de travail</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"text-muted\" style=\"font-size: 0.8rem\">Optional if you "
"provided a Linkedin profile</span>"
msgstr ""
"<span class=\"text-muted\" style=\"font-size: 0.8rem\">Optionnel si vous "
"renseignez un profil Linkedin</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"<span>\n"
"                                            We usually reply between one and three days.<br/>\n"
"                                            Feel free to contact him/her if you have further questions.\n"
"                                        </span>"
msgstr ""
"<span>\n"
" Nous répondons généralement sous un à trois jours.<br/>\n"
" N'hésitez pas à le/la contacter si vous avez d'autres questions.\n"
"</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "A full-time position <br/>Attractive salary package."
msgstr "Un poste à temps plein <br/>Package salarial attractif."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "About us"
msgstr "À propos de nous"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Achieve monthly sales objectives"
msgstr "Atteindre les objectifs mensuels de vente"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Additional languages"
msgstr "Langues additionnelles"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Administrative Work"
msgstr "Travail administratif"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
msgid "All Countries"
msgstr "Tous les pays"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "All Departments"
msgstr "Tous les départements"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "All Employment Types"
msgstr "Tous les types d'emploi"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "All Offices"
msgstr "Tous les bureaux"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr "Candidat"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Applied Job"
msgstr "Poste concerné"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr "Postuler"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply Now!"
msgstr "Postuler maintenant !"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br/><br/>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"En tant qu'employé de notre société, vous <b>collaborerez avec chaque départment afin de créer et déployer des produits disruptifs. </b> Venez travailler dans une société en pleine croissance \n"
"qui vous offre de chouettes avantages, ainsi que des opportunités de progression et d'apprentissage aux côtés de leaders accomplis. \n"
"Nous recherchons un membre de notre équipe expérimenté. <br/><br/>\n"
"Cette position est à la fois <b>créative et rigoureuse </b>, vous devez pouvoir réfléchir hors des habitudes. Nous attendons du candidat d'être proactif et d'avoir un esprit de battant. \n"
"Pour y parvenir, vous aurez de solides compétences en résolution de problèmes. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Autonomy"
msgstr "Autonomie"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Bachelor Degree or Higher"
msgstr "Diplôme de baccalauréat ou supérieur"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__can_publish
msgid "Can Publish"
msgstr "Peut publier"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__job_details
msgid "Complementary information that will appear on the job submission page"
msgstr ""
"Des informations complémentaires qui sont apparaître sur la page de l'offre "
"d'emploi"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Congratulations!"
msgstr "Félicitations !"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Contact us"
msgstr "Contactez-nous"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Continue To Our Website"
msgstr "Retourner à l'accueil de notre site"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Countries Filter"
msgstr "Filtre pays"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Create content that will help our users on a daily basis"
msgstr ""
"Création de contenu qui aidera nos utilisateurs sur une base journalière"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Create new job pages from the <strong><i>+New</i></strong> top-right button."
msgstr ""
"Créez des pages d'emploi depuis le bouton <strong><i>+Nouveau</i></strong> "
"en haut à droite."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Customer Relationship"
msgstr "Relation client"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#: model:ir.model,name:website_hr_recruitment.model_hr_department
#, python-format
msgid "Department"
msgstr "Département"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Departments Filter"
msgstr "Filtre départements"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Discover our products."
msgstr "Découvrez nos produits."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                You can make a real contribution to the success of the company.\n"
"                <br/>\n"
"                Several activities are often organized all over the year, such as weekly\n"
"                sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Chaque employé a la chance de voir l'impact de son travail.\n"
"Vous pouvez réellement contribuer au succès de la société.\n"
"<br/>\n"
"De nombreuses activités sont organisées au cours de l'année, comme des sessions de sports hebdomadaires, des événements de team-building, des after-works et bien plus"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Eat &amp; Drink"
msgstr "Manger & boire"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Employment Types Filter"
msgstr "Filtre types d'emploi"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Expand your knowledge of various business industries"
msgstr "Élargissez vos connaissances de divers secteurs d'activité"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Experience in writing online content"
msgstr "Expérience en rédaction de contenu en ligne. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Fruit, coffee and <br/>snacks provided."
msgstr "Fruits, café <br/>et snacks fournis."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Google Adwords experience"
msgstr "Expérience avec Google Adwords"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""
"Belle équipe de personnes intelligentes, dans une ambiance chaleureuse et "
"ouverte. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Highly creative and autonomous"
msgstr "Hautement créatif et autonome"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "I'm feeling lucky"
msgstr "J'ai de la chance"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "In the meantime,"
msgstr "Dans l'intervalle,"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_published
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_website_published_button
msgid "Is Published"
msgstr "Est publié"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr "Formulaire de recrutement"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Job Detail"
msgstr "Détail de l'offre d'emploi"

#. module: website_hr_recruitment
#: model:ir.actions.act_window,name:website_hr_recruitment.action_job_pages_list
msgid "Job Pages"
msgstr "Pages des offres d'emploi"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Poste"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
#, python-format
msgid "Job Title"
msgstr "Titre du poste"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/website.py:0
#: model:ir.ui.menu,name:website_hr_recruitment.menu_job_pages
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
#, python-format
msgid "Jobs"
msgstr "Postes"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Jobs Page"
msgstr "Page des offres d'emploi"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Join us and help disrupt the enterprise market!"
msgstr "Rejoignez-nous et révolutionnez le marché de l'entreprise !"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Join us, we offer you an extraordinary chance to learn, to\n"
"                                    develop and to be part of an exciting experience and\n"
"                                    team."
msgstr ""
"Rejoignez-nous, nous vous offrons une chance incroyable d'apprendre à\n"
"développer et de faire partie d'une expérience excitante et d'une équipe incroyable."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Lead the entire sales cycle"
msgstr "Diriger l'ensemble du cycle de vente"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "LinkedIn Profile"
msgstr "Profil Linkedin"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Look around on our website:"
msgstr "Parcourez notre site web :"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Master demos of our software"
msgstr "Maîtriser les démonstrations de nos produits"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Must Have"
msgstr "Compétences nécéssaires"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Negotiate and contract"
msgstr "Négocier et conclure"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Nice to have"
msgstr "Agréable d'avoir"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Pas de managers simplets, d'outils idiots à utiliser, d'heures de travail "
"rigides"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Pas de perte de temps dans des processus internes, véritables responsabilité"
" et autonomie"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "Not published"
msgstr "Non publié"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Offices Filter"
msgstr "Filtre bureaux"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Optional introduction, or any question you might have about the job…"
msgstr ""
"Introduction optionnelle ou toute autre question que vous pourriez avoir sur"
" l'emploi..."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Our Job Offers"
msgstr "Nos offres d'emploi"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Our Product"
msgstr "Notre produit"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Passion for software products"
msgstr "Passion pour les produits logiciels"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perfect written English"
msgstr "Anglais écrit parfait"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perks"
msgstr "Avantages"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Personal Evolution"
msgstr "Évolution personnelle"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Numéro de téléphone"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Play any sport with colleagues, <br/>the bill is covered."
msgstr ""
"Pratiquez n'importe quel sport avec des collègues,<br/> la facture est "
"couverte."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__job_details
msgid "Process Details"
msgstr "Détails de la procédure"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_search_view_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_tree_inherit_website
msgid "Published"
msgstr "Publié"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Qualify the customer needs"
msgstr "Qualifez les besoins du client"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"De vraies responsabilités et de vrais défis dans une société évoluant "
"rapidement"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Remote"
msgstr "À distance"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Responsibilities"
msgstr "Responsabilités"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_id
msgid "Restrict publishing to this website."
msgstr "Limiter la publication sur ce site web."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "SEO Optimized"
msgstr "SEO optimisé"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimisé"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__seo_name
msgid "Seo name"
msgstr "SEO nom"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_published
msgid "Set if the application is published on the website of the company."
msgstr "Définir si l'offre est publiée sur le site web de l'entreprise."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Short Introduction"
msgstr "Brève introduction"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Sidebar"
msgstr "Barre latérale"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Source des candidats"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Sport Activity"
msgstr "Activité sportive "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Strong analytical skills"
msgstr "Compétences analytiques avancées"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Technical Expertise"
msgstr "Expertise technique"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_url
msgid "The full URL to access the document through the website."
msgstr "L'URL complète pour accéder au document à travers le site web."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "The job offer has been closed."
msgstr "L'offre a été fermée."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Trainings"
msgstr "Formations"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source__url
msgid "Url Parameters"
msgstr "URL paramètres"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Valid work permit for Belgium"
msgstr "Permis de travail valide pour la Belgique"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_published
msgid "Visible on current website"
msgstr "Visible sur le site web actuel"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products.\n"
"                                We build great products to solve your business problems."
msgstr ""
"Nous sommes une équipe de personnes passionnées dont le but est d'améliorer la vie de tout le monde par des produits hors normes.\n"
"Nous créons de superbes produits pour résoudre les problèmes de votre entreprise."

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_website
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_id
msgid "Website"
msgstr "Site web"

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr "Formulaire de recrutement du site web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_url
msgid "Website URL"
msgstr "URL de site web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_description
msgid "Website description"
msgstr "Description du site web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_description
msgid "Website meta description"
msgstr "Méta description du site web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_keywords
msgid "Website meta keywords"
msgstr "Méta mots-clés du site web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_title
msgid "Website meta title"
msgstr "Méta titre du site web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_og_img
msgid "Website opengraph image"
msgstr "Image opengraph site web"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What We Offer"
msgstr "Ce que nous offrons"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What's great in the job?"
msgstr "Qu'est-ce qui est génial dans ce travail ?"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_inherit_website
msgid ""
"You can write here a short description of your Job Description that will be "
"displayed on the main Jobs' list page."
msgstr ""
"Vous pouvez écrire ici une courte description de votre description de poste "
"qui sera affichée sur la page principale des offres d'emploi."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Votre e-mail"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Votre nom"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your application has been posted successfully."
msgstr "Votre candidature a bien été envoyée."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your application has been sent to:"
msgstr "Votre candidature a été envoyée à :"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "e.g. https://www.linkedin.com/in/fpodoo/"
msgstr "par ex. https://www.linkedin.com/in/fpodoo/"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "for job opportunities."
msgstr "pour accéder aux offres d'emploi."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open position"
msgstr "poste disponible"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr "postes disponibles"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_recruitment_source_kanban_inherit_website
msgid "share it"
msgstr "partages"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "unpublished"
msgstr "non publié"
