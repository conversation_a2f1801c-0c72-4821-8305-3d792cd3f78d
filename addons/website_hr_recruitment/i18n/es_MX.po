# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_hr_recruitment
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>lo<PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_job.py:0
#, python-format
msgid ""
"\n"
"            <span class=\"text-muted small\">Time to Answer</span>\n"
"            <h6>2 open days</h6>\n"
"            <span class=\"text-muted small\">Process</span>\n"
"            <h6>1 Phone Call</h6>\n"
"            <h6>1 Onsite Interview</h6>\n"
"            <span class=\"text-muted small\">Days to get an Offer</span>\n"
"            <h6>4 Days after Interview</h6>\n"
"        "
msgstr ""
"\n"
"            <span class=\"text-muted small\">Tiempo para responder</span>\n"
"            <h6>2 días abiertos</h6>\n"
"            <span class=\"text-muted small\">Proceso</span>\n"
"            <h6>1 llamada telefónica</h6>\n"
"            <h6>1 entrevista presencial</h6>\n"
"            <span class=\"text-muted small\">Días para recibir una oferta</span>\n"
"            <h6>4 días después de la entrevista</h6>\n"
"        "

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "%s's Application"
msgstr "Solicitud de %s"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "12 days / year, including <br/>6 of your choice."
msgstr "12 días al año, incluyendo <br/>6 a su elección."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""
"<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Teléfono\" "
"title=\"Teléfono\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<i class=\"fa fa-arrow-left\"/> Job Description"
msgstr "<i class=\"fa fa-arrow-left\"/> Descripción del trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Correo electrónico\" "
"title=\"Correo electrónico\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Publication date\" role=\"img\" "
"aria-label=\"Publication date\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Fecha de publicación\" role=\"img\""
" aria-label=\"Fecha de publicación\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_kanban_referal_extends
msgid ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Job Page"
msgstr ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Página del trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-suitcase\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Employment type\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-suitcase\" title=\"Tipo de empleo\" role=\"img\" "
"aria-label=\"Tipo de empleo\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Sitio web\"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "<small><b>READ</b></small>"
msgstr "<small><b>MÁS INFORMACIÓN</b></small>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "<span class=\"bg-odoo\">Published</span>"
msgstr "<span class=\"bg-odoo\">Publicado</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"d-none text-muted\" style=\"margin-left: 200px; font-size: "
"0.8rem\">The resume is optional if you have a Linkedin profile</span>"
msgstr ""
"<span class=\"d-none text-muted\" style=\"margin-left: 200px; font-size: "
"0.8rem\">El currículum es opcional si tiene un perfil de LinkedIn</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Department</span>"
msgstr "<span class=\"s_website_form_label_content\">Departamento</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Job</span>"
msgstr "<span class=\"s_website_form_label_content\">Trabajo</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">LinkedIn Profile</span>"
msgstr "<span class=\"s_website_form_label_content\">Perfil de LinkedIn</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Resume</span>"
msgstr "<span class=\"s_website_form_label_content\">Currículum</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Short Introduction</span>"
msgstr "<span class=\"s_website_form_label_content\">Presentación corta</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su nombre</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Phone Number</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su número de teléfono</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Department</span>"
msgstr "<span class=\"text-muted small\">Departamento</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Employment Type</span>"
msgstr "<span class=\"text-muted small\">Tipo de empleo </span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Job</span>"
msgstr "<span class=\"text-muted small\">Trabajo</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Location</span>"
msgstr "<span class=\"text-muted small\">Ubicación</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"text-muted\" style=\"font-size: 0.8rem\">Optional if you "
"provided a Linkedin profile</span>"
msgstr ""
"<span class=\"text-muted\" style=\"font-size: 0.8rem\">Es opcional si "
"proporcionó un perfil de LinkedIn</span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"<span>\n"
"                                            We usually reply between one and three days.<br/>\n"
"                                            Feel free to contact him/her if you have further questions.\n"
"                                        </span>"
msgstr ""
"<span>\n"
"                                            Usualmente respondemos entre 1 y 3 días.<br/>\n"
"                                            Siéntase libre de contactarle si tiene alguna otra pregunta.\n"
"                                        </span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "A full-time position <br/>Attractive salary package."
msgstr "Puesto de tiempo completo <br/> Salario atractivo."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "About us"
msgstr "Sobre nosotros"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Achieve monthly sales objectives"
msgstr "Alcanzar los objetivos de ventas mensuales"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Additional languages"
msgstr "Idiomas adicionales"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Administrative Work"
msgstr "Trabajo administrativo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
msgid "All Countries"
msgstr "Todos los países"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "All Departments"
msgstr "Todos los departamentos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "All Employment Types"
msgstr "Todos los tipos de empleo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "All Offices"
msgstr "Todas las oficinas"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr "Candidato"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Applied Job"
msgstr "Trabajo solicitado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr "Solicitar trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply Now!"
msgstr "¡Solicitar ahora!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br/><br/>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"Como empleado de nuestra empresa, <b>usted colaborará con todos los departamentos\n"
"                        para crear e implementar productos revolucionarios.</b> Venga a trabajar en una empresa en crecimiento\n"
"                        que ofrece grandes beneficios con oportunidades para crecer y aprender\n"
"                        junto con líderes consumados. Buscamos un miembro del personal con experiencia\n"
"                        y excepcional.\n"
"                        <br/><br/>\n"
"                        Este puesto es <b>creativo y riguroso</b> por naturaleza, debe pensar\n"
"                        más allá de lo común. Esperamos que el candidato sea proactivo y tenga una mentalidad orientada hacia los\n"
"                        resultados. Para tener éxito, debe tener habilidades sólidas para resolver problemas."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Autonomy"
msgstr "Autonomía"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Bachelor Degree or Higher"
msgstr "Licenciatura o superior"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__job_details
msgid "Complementary information that will appear on the job submission page"
msgstr ""
"Información complementaria que aparecerá en la página de envío de trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Congratulations!"
msgstr "¡Felicidades!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Contact us"
msgstr "Contáctenos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Continue To Our Website"
msgstr "Continuar a nuestro sitio web"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Countries Filter"
msgstr "Filtro de países"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Create content that will help our users on a daily basis"
msgstr "Crear contenido que ayude a nuestros usuarios a diario"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Create new job pages from the <strong><i>+New</i></strong> top-right button."
msgstr ""
"Cree nuevas páginas de empleos mediante el botón "
"<strong><i>+Nuevo</i></strong> en la parte superior derecha."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Customer Relationship"
msgstr "Relación con el cliente"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#: model:ir.model,name:website_hr_recruitment.model_hr_department
#, python-format
msgid "Department"
msgstr "Departamento"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Departments Filter"
msgstr "Filtro de departamentos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Discover our products."
msgstr "Descubra nuestros productos."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                You can make a real contribution to the success of the company.\n"
"                <br/>\n"
"                Several activities are often organized all over the year, such as weekly\n"
"                sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Cada empleado tiene la oportunidad de ver el impacto de su trabajo.\n"
"                Puede hacer una contribución real al éxito de la empresa.\n"
"                <br/>\n"
"                Se organizan varias actividades a lo largo del año, como actividades deportivas\n"
"                semanales, eventos para fomentar el trabajo en equipo, una convivencia al mes, ¡y mucho más!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Eat &amp; Drink"
msgstr "Comida y bebida"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Employment Types Filter"
msgstr "Filtro de tipos de empleo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Expand your knowledge of various business industries"
msgstr "Amplíe su conocimiento de diversas industrias comerciales"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Experience in writing online content"
msgstr "Experiencia en redacción de contenido en línea"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Fruit, coffee and <br/>snacks provided."
msgstr "Ofrecemos fruta, café y <br/>botanas."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Google Adwords experience"
msgstr "Experiencia en Google Adwords"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""
"Forma parte de un equipo de personas muy inteligentes dentro de una cultura "
"amistosa y abierta."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Highly creative and autonomous"
msgstr "Altamente creativo e independiente"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "I'm feeling lucky"
msgstr "Voy a tener suerte"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "In the meantime,"
msgstr "Mientras tanto,"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_published
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_website_published_button
msgid "Is Published"
msgstr "Publicado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr "Formulario de solicitud de empleo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Job Detail"
msgstr "Detalles del trabajo"

#. module: website_hr_recruitment
#: model:ir.actions.act_window,name:website_hr_recruitment.action_job_pages_list
msgid "Job Pages"
msgstr "Páginas de trabajo"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
#, python-format
msgid "Job Title"
msgstr "Título del trabajo"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/website.py:0
#: model:ir.ui.menu,name:website_hr_recruitment.menu_job_pages
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
#, python-format
msgid "Jobs"
msgstr "Empleos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Jobs Page"
msgstr "Página de trabajos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Join us and help disrupt the enterprise market!"
msgstr "¡Únase a nosotros y ayúdenos a revolucionar el mercado empresarial!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Join us, we offer you an extraordinary chance to learn, to\n"
"                                    develop and to be part of an exciting experience and\n"
"                                    team."
msgstr ""
"Únase a nosotros, le ofrecemos una oportunidad extraordinaria para aprender,\n"
"                                    desarrollar y ser parte de una experiencia y equipo\n"
"                                    emocionantes."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Lead the entire sales cycle"
msgstr "Dirigir todo el ciclo de ventas"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "LinkedIn Profile"
msgstr "Perfil de LinkedIn"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Look around on our website:"
msgstr "Eche un vistazo a nuestro sitio web:"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Master demos of our software"
msgstr "Dominar demostraciones de nuestro software"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Must Have"
msgstr "Requisitos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Negotiate and contract"
msgstr "Negociar y contratar"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Nice to have"
msgstr "Deseable"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Sin gerentes incompetentes, sin herramientas complejas para usar, sin "
"horarios de trabajo rígidos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"No se pierde el tiempo en procesos empresariales; se tienen "
"responsabilidades reales y autonomía."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "Not published"
msgstr "Sin publicar"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Offices Filter"
msgstr "Filtro de oficinas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Optional introduction, or any question you might have about the job…"
msgstr ""
"Presentación opcional, o cualquier pregunta que tenga sobre el trabajo..."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Our Job Offers"
msgstr "Nuestras ofertas de trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Our Product"
msgstr "Nuestro producto"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Passion for software products"
msgstr "Pasión por productos de software"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perfect written English"
msgstr "Inglés escrito avanzado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perks"
msgstr "Beneficios"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Personal Evolution"
msgstr "Evolución personal"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Número de teléfono"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Play any sport with colleagues, <br/>the bill is covered."
msgstr ""
"Practique cualquier deporte con sus colegas <br/>y nosotros lo pagamos."

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__job_details
msgid "Process Details"
msgstr "Detalles del proceso"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_search_view_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_tree_inherit_website
msgid "Published"
msgstr "Publicado"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Qualify the customer needs"
msgstr "Evaluar las necesidades del cliente"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"Responsabilidades y retos reales en una empresa que se expande con rapidez."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Remote"
msgstr "Remoto"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Responsibilities"
msgstr "Responsabilidades"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir publicaciones a este sitio web."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "SEO Optimized"
msgstr "Optimizado para SEO"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizado para SEO"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__seo_name
msgid "Seo name"
msgstr "Nombre SEO"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_published
msgid "Set if the application is published on the website of the company."
msgstr "Establezca si la solicitud se publica en el sitio web de la empresa."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Short Introduction"
msgstr "Breve introducción"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Sidebar"
msgstr "Barra lateral"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Origen de los candidatos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Sport Activity"
msgstr "Actividad deportiva"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Strong analytical skills"
msgstr "Habilidades analíticas avanzadas"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Technical Expertise"
msgstr "Conocimientos técnicos"

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "The job offer has been closed."
msgstr "Esta oferta de trabajo está cerrada."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Trainings"
msgstr "Capacitaciones"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source__url
msgid "Url Parameters"
msgstr "Parámetros del URL"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Valid work permit for Belgium"
msgstr "Permiso de trabajo válido para Bélgica"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products.\n"
"                                We build great products to solve your business problems."
msgstr ""
"Somos un equipo de personas apasionadas cuyo objetivo es mejorar la vida de todo el mundo a través de nuestros productos revolucionarios.\n"
"                                Creamos grandes productos para resolver sus problemas empresariales."

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_website
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr "Formulario de reclutamiento del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_description
msgid "Website description"
msgstr "Descripción del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagen del Open Graph del sitio web"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What We Offer"
msgstr "Qué ofrecemos"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What's great in the job?"
msgstr "¿Cuáles son las ventajas del trabajo?"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_inherit_website
msgid ""
"You can write here a short description of your Job Description that will be "
"displayed on the main Jobs' list page."
msgstr ""
"Puede escribir aquí un texto corto de su descripción de trabajo que se "
"mostrará en la página principal de la lista de trabajos."

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Su correo electrónico"

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Su nombre"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your application has been posted successfully."
msgstr "Su solicitud se publicó correctamente."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your application has been sent to:"
msgstr "Su solicitud se envió a:"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "breadcrumb"
msgstr "migas de pan"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "e.g. https://www.linkedin.com/in/fpodoo/"
msgstr "Por ejemplo, https://www.linkedin.com/in/fpodoo/"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "for job opportunities."
msgstr "para obtener información sobre oportunidades de trabajo."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open position"
msgstr "puesto abierto"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr "puestos disponibles"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_recruitment_source_kanban_inherit_website
msgid "share it"
msgstr "compartir"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "unpublished"
msgstr "sin publicar"
