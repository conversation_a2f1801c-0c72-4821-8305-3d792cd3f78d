# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_hr_recruitment
# 
# Translators:
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: ສີສຸວັນ ສັງບົວບຸລົມ <<EMAIL>>, 2023\n"
"Language-Team: Lao (https://app.transifex.com/odoo/teams/41243/lo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lo\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_job.py:0
#, python-format
msgid ""
"\n"
"            <span class=\"text-muted small\">Time to Answer</span>\n"
"            <h6>2 open days</h6>\n"
"            <span class=\"text-muted small\">Process</span>\n"
"            <h6>1 Phone Call</h6>\n"
"            <h6>1 Onsite Interview</h6>\n"
"            <span class=\"text-muted small\">Days to get an Offer</span>\n"
"            <h6>4 Days after Interview</h6>\n"
"        "
msgstr ""

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "%s's Application"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "12 days / year, including <br/>6 of your choice."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<i class=\"fa fa-arrow-left\"/> Job Description"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Publication date\" role=\"img\" "
"aria-label=\"Publication date\"/>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_kanban_referal_extends
msgid ""
"<i class=\"fa fa-fw fa-external-link\" role=\"img\"/>\n"
"                        Job Page"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-suitcase\" title=\"Employment type\" role=\"img\" "
"aria-label=\"Employment type\"/>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "<small><b>READ</b></small>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "<span class=\"bg-odoo\">Published</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"d-none text-muted\" style=\"margin-left: 200px; font-size: "
"0.8rem\">The resume is optional if you have a Linkedin profile</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Department</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Job</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">LinkedIn Profile</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Resume</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Short Introduction</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Phone Number</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Department</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Employment Type</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Job</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"text-muted small\">Location</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"text-muted\" style=\"font-size: 0.8rem\">Optional if you "
"provided a Linkedin profile</span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid ""
"<span>\n"
"                                            We usually reply between one and three days.<br/>\n"
"                                            Feel free to contact him/her if you have further questions.\n"
"                                        </span>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "A full-time position <br/>Attractive salary package."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "About us"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Achieve monthly sales objectives"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Additional languages"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Administrative Work"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
msgid "All Countries"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "All Departments"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_employment_type
msgid "All Employment Types"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "All Offices"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Applied Job"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply Now!"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br/><br/>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Autonomy"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Bachelor Degree or Higher"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__job_details
msgid "Complementary information that will appear on the job submission page"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Congratulations!"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Contact us"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Continue To Our Website"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Countries Filter"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Create content that will help our users on a daily basis"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Create new job pages from the <strong><i>+New</i></strong> top-right button."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Customer Relationship"
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#: model:ir.model,name:website_hr_recruitment.model_hr_department
#, python-format
msgid "Department"
msgstr "ຫ້ອງການ"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Departments Filter"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Discover our products."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                You can make a real contribution to the success of the company.\n"
"                <br/>\n"
"                Several activities are often organized all over the year, such as weekly\n"
"                sports sessions, team building events, monthly drink, and much more"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Eat &amp; Drink"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Employment Types Filter"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Expand your knowledge of various business industries"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Experience in writing online content"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Fruit, coffee and <br/>snacks provided."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Google Adwords experience"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Highly creative and autonomous"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "I'm feeling lucky"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "In the meantime,"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_published
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_website_published_button
msgid "Is Published"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Job Detail"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.actions.act_window,name:website_hr_recruitment.action_job_pages_list
msgid "Job Pages"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "ຕຳແໜ່ງວຽກ"

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/controllers/main.py:0
#, python-format
msgid "Job Title"
msgstr ""

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/website.py:0
#: model:ir.ui.menu,name:website_hr_recruitment.menu_job_pages
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
#, python-format
msgid "Jobs"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Jobs Page"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Join us and help disrupt the enterprise market!"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Join us, we offer you an extraordinary chance to learn, to\n"
"                                    develop and to be part of an exciting experience and\n"
"                                    team."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Lead the entire sales cycle"
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "LinkedIn Profile"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Look around on our website:"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Master demos of our software"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Must Have"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Negotiate and contract"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Nice to have"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "Not published"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Offices Filter"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Optional introduction, or any question you might have about the job…"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Our Job Offers"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Our Product"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Passion for software products"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perfect written English"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perks"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Personal Evolution"
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Phone Number"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Play any sport with colleagues, <br/>the bill is covered."
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__job_details
msgid "Process Details"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_search_view_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_tree_inherit_website
msgid "Published"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Qualify the customer needs"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Remote"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Responsibilities"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_pages_kanban_view
msgid "SEO Optimized"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__seo_name
msgid "Seo name"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_published
msgid "Set if the application is published on the website of the company."
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Short Introduction"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.snippet_options
msgid "Sidebar"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Sport Activity"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Strong analytical skills"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Technical Expertise"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_url
msgid "The full URL to access the document through the website."
msgstr "ທີ່ຢູ່ເຕັມ URL ເພື່ອເຂົ້າເຖິງເອກະສານຜ່ານທາງເວັບໄຊຕ໌."

#. module: website_hr_recruitment
#. odoo-python
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "The job offer has been closed."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Trainings"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source__url
msgid "Url Parameters"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Valid work permit for Belgium"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products.\n"
"                                We build great products to solve your business problems."
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_website
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_id
msgid "Website"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_url
msgid "Website URL"
msgstr "ທີ່ຢູ່ເວັບໄຊຕ໌ URL"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_description
msgid "Website description"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_description
msgid "Website meta description"
msgstr "ລາຍລະອຽດ ເມຕາ ເວັບໄຊຕ໌"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_keywords
msgid "Website meta keywords"
msgstr "ຄຳໄຂລະຫັດ ເມຕາ ເວັບໄຊຕ໌"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_title
msgid "Website meta title"
msgstr "ຫົວເລື່ອງ ເມຕາ ເວັບໄຊຕ໌"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What We Offer"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What's great in the job?"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_inherit_website
msgid ""
"You can write here a short description of your Job Description that will be "
"displayed on the main Jobs' list page."
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Email"
msgstr ""

#. module: website_hr_recruitment
#. odoo-javascript
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Name"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your application has been posted successfully."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
msgid "Your application has been sent to:"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "breadcrumb"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "e.g. https://www.linkedin.com/in/fpodoo/"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "for job opportunities."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open position"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_recruitment_source_kanban_inherit_website
msgid "share it"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "unpublished"
msgstr ""
