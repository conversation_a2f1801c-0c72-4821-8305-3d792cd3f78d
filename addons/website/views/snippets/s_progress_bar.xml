<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template name="Progress Bar" id="s_progress_bar">
    <div class="s_progress_bar" data-display="inline">
        <h4 class="mb-0">We are almost done!</h4>
        <div class="progress">
            <div class="progress-bar progress-bar-striped" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%; min-width: 3%">
                <span class="s_progress_bar_text">80% Development</span>
            </div>
        </div>
    </div>
</template>

<template id="s_progress_bar_options" inherit_id="website.snippet_options">
    <xpath expr="." position="inside">
        <div data-js="progress" data-selector=".s_progress_bar" >
            <we-input string="Value" data-progress-bar-value="" data-unit="%"/>
            <we-select string="Display">
                <we-button data-display="inline">Inline</we-button>
                <we-button data-display="below">Below Each Other</we-button>
            </we-select>
            <we-colorpicker string="Colors" data-apply-to=".progress-bar"
                data-select-style="true"
                data-css-property="background-color"
                data-color-prefix="bg-"/>
            <we-checkbox string="Striped" data-select-class="progress-bar-striped" data-apply-to=".progress-bar"/>
            <we-checkbox string="Animated" data-select-class="progress-bar-animated" data-apply-to=".progress-bar"/>
        </div>
    </xpath>
    <xpath expr="//div[@id='so_content_addition']" position="attributes">
        <attribute name="data-selector" add=".s_progress_bar" separator=","/>
        <attribute name="data-drop-near" add=".s_progress_bar" separator=","/>
    </xpath>
</template>

<!-- No related CSS but there is some in theme overrides -->
</odoo>
