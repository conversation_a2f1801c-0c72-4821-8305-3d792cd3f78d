<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_features_grid" name="Features Grid">
    <section class="s_features_grid pt48 pb24">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 s_col_no_bgcolor pb24">
                    <div class="row">
                        <div class="col-lg-12 pb24" data-name="Box">
                            <h2>First list of Features</h2>
                            <h5>Add a great slogan.</h5>
                        </div>
                        <div class="col-lg-12 pt16 pb16" data-name="Box">
                            <i class="fa fa-2x fa-font-awesome rounded-circle bg-primary s_features_grid_icon"/>
                            <div class="s_features_grid_content">
                                <h4>Change Icons</h4>
                                <p>Double click an icon to replace it with one of your choice.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 pt16 pb16" data-name="Box">
                            <i class="fa fa-2x fa-files-o rounded-circle bg-primary s_features_grid_icon"/>
                            <div class="s_features_grid_content">
                                <h4>Duplicate</h4>
                                <p>Duplicate blocks and columns to add more features.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 pt16 pb16" data-name="Box">
                            <i class="fa fa-2x fa-trash rounded-circle bg-primary s_features_grid_icon"/>
                            <div class="s_features_grid_content">
                                <h4>Delete Blocks</h4>
                                <p>Select and delete blocks to remove features.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 s_col_no_bgcolor pb24">
                    <div class="row">
                        <div class="col-lg-12 pb24" data-name="Box">
                            <h2>Second list of Features</h2>
                            <h5>Add a great slogan.</h5>
                        </div>
                        <div class="col-lg-12 pt16 pb16" data-name="Box">
                            <i class="fa fa-2x fa-magic rounded bg-secondary s_features_grid_icon"/>
                            <div class="s_features_grid_content">
                                <h4>Great Value</h4>
                                <p>Turn every feature into a benefit for your reader.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 pt16 pb16" data-name="Box">
                            <i class="fa fa-2x fa-eyedropper rounded bg-secondary s_features_grid_icon"/>
                            <div class="s_features_grid_content">
                                <h4>Edit Styles</h4>
                                <p>You can edit colors and backgrounds to highlight features.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 pt16 pb16" data-name="Box">
                            <i class="fa fa-2x fa-picture-o rounded bg-secondary s_features_grid_icon"/>
                            <div class="s_features_grid_content">
                                <h4>Sample Icons</h4>
                                <p>All these icons are completely free for commercial use.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<record id="website.s_features_grid_000_scss" model="ir.asset">
    <field name="name">Features grid 000 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_features_grid/000.scss</field>
</record>

</odoo>
