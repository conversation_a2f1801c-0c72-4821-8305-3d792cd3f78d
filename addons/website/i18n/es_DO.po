# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-05-19 06:07+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.sitemap_index_xml
#: model_terms:ir.ui.view,arch_db:website.sitemap_xml
msgid "&lt;?xml version=\"1.0\" encoding=\"UTF-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"UTF-8\"?&gt;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:17
#: code:addons/website/static/src/xml/website.gallery.xml:97
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
",\n"
"                                updated:"
msgstr ""
",\n"
"actualizado:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid ""
",\n"
"                the #1"
msgstr ""
",\n"
"el #1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", autor:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", updated:"
msgstr ", actualizado:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "- The Odoo Team"
msgstr "- El Equipo de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"1. Add your <strong>desired languages</strong><br/>\n"
"                                Use the link in the footer of your website "
"(when logged in) to add\n"
"                                languages from the available list. Also, you "
"can change the\n"
"                                default language in"
msgstr ""
"1. Añadir sus <strong> idiomas deseados </ strong> <br/>\n"
"Utilice el enlace en el pie de página de su sitio web (cuando está "
"conectado) para añadir\n"
"idiomas de la lista disponible. Además, puede cambiar el idioma por defecto "
"en"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:17
#, python-format
msgid "1. Define Keywords"
msgstr "1. Definir las Palabras Clave"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "10s"
msgstr "10s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1s"
msgstr "1s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "2. <strong>Setup Gengo</strong><br/>"
msgstr "2. <strong>Configurar Gengo</strong><br/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:45
#, python-format
msgid "2. Reference Your Page"
msgstr "2. Referenciar Su Página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "Soporte telefónico gratuito 24x7"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2s"
msgstr "2s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:62
#, python-format
msgid "3. Preview"
msgstr "3. Previsualizar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3s"
msgstr "3s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "403: Forbidden"
msgstr "403: Prohibido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid "404: Page not found!"
msgstr "404: Página no encontrada!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5s"
msgstr "5s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "<b>A Small Subtitle</b>"
msgstr "<b>Un Pequeño Subtítulo</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<b>Choose an image...</b>"
msgstr "<b>Escoja una imagen...</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories are for everyone even when only written for\n"
"                        just one person.</b> If you try to write with a wide "
"general\n"
"                        audience in mind, your story will ring false and be "
"bland.\n"
"                        No one will be interested. Write for one person. If "
"it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"<b>Las grandes historias son para todos, incluso cuando sólo se escribe para "
"una sola persona.</b> Si intenta escribir con una amplia audiencia en mente, "
"su historia sonará falsa y suave. A nadie le va a interesar. Escriba para "
"una persona. Si es genuina para una, es genuina para el resto."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
#, fuzzy
msgid ""
"<b>Great stories have personality.</b> Consider telling\n"
"                        a great story that provides personality. Writing a "
"story\n"
"                        with personality for potential clients will asists "
"with\n"
"                        making a relationship connection. This shows up in "
"small\n"
"                        quirks like word choices or phrases. Write from your "
"point\n"
"                        of view, not from someone else's experience."
msgstr ""
"<B> Las grandes historias tienen personalidad. </ B> Considere contar\n"
"una gran historia que muestre personalidad. Escribir una historia con "
"personalidad para los clientes potenciales ayudará a hacer una conexión en "
"la relación. Esto se muestra en pequeños apartes como opciones o frases de "
"palabras. Escriba desde su punto de vista, no desde la experiencia de otra "
"persona."

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_theme
msgid "<b>No theme module found!</b>"
msgstr "<b>¡No se encontró el módulo de tema!</b>"

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_website
msgid "<b>No website module found!</b>"
msgstr "<b>¡No se encontró el módulo de sitio web!</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_sign_in
msgid "<b>Sign in</b>"
msgstr "<b>Iniciar sesión</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Contact Us Now"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"Contáctenos Ahora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
#, fuzzy
msgid "<i class=\"fa fa-check-square\"/> Feature"
msgstr "<i class=\"fa fa-th-large\"/> Sitio Web<b class=\"caret\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, fuzzy
msgid "<i class=\"fa fa-envelope-o\"/> Email Our Website Expert"
msgstr "<span class=\"fa fa-envelope-o\"/> Escríbale a Nuestro Experto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-indent\"/> Content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-magic icon-fix\"/> Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Add a language..."
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"Añadir un idioma..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
#, fuzzy
msgid "<i class=\"fa fa-th-large\"/> Structure"
msgstr "<i class=\"fa fa-th-large\"/> Sitio Web<b class=\"caret\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "<i class=\"fa fa-th-large\"/> WEBSITE <b class=\"caret\"/>"
msgstr "<i class=\"fa fa-th-large\"/> Sitio Web<b class=\"caret\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                        The whole process may take a few hours, some "
"discussions with your colleagues and\n"
"                    several cups of coffee to go through. But don't worry, "
"you can return to this tool at any time.</i>"
msgstr ""
"<i> Todo el proceso puede tardar un par de horas, algunas discusiones con "
"sus colegas y varias tazas de café. Pero no se preocupe, puede volver a esta "
"herramienta en cualquier momento. </i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                        Whether you're a beginner or a pro, we have "
"everything you need to plan, create,\n"
"                    publish and grow your site, blog or online store.</i>"
msgstr ""
"<i>\n"
"Tanto si usted es un principiante o un profesional, nosotros tenemos todo lo "
"que nececita para planear, crear,\n"
"publicar y ampliar su sitio web, blog o tienda en línea.</i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                    This Planner will help you to think about your website "
"and provide tips and ideas to inspire you. There are examples and "
"explanations to guide you through the process of creating a top-notch, high "
"quality site that meets all your needs.</i>"
msgstr ""
"<i>\n"
"                    Este planificador le ayudará a pensar sobre su sitio web "
"y proveer trucos e ideas para  inspirarlo. Hay ejemplos y explicaciones para "
"guiarlo a través del proceso de crear un sitio web de alta calidad que "
"cumpla con todas sus necesidades.</i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>Congratulations on taking the leap and deciding to build your own website!"
"</i>"
msgstr ""
"<i>¡Felicitaciones por dar el salto y decidir construir su propio sitio web! "
"</i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr ""
"<i>Configuración instantánea, quedará satisfecho o será reembolsado. </i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<i>We wish you a fun time!</i>"
msgstr "<i>¡Le deseamos que tenga un momento divertido!</i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_floating
msgid ""
"<small class=\"text-muted\">A great way to catch your reader's attention is "
"to tell a story. Everything you consider writing can be told as a story.</"
"small>"
msgstr ""
"<small class = \"text-muted\">Una gran manera de captar la atención del "
"lector es contar una historia. Todo lo que considere escribir puede ser "
"contado como una historia.</ small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
#: model_terms:ir.ui.view,arch_db:website.s_quote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "<small>Author of this quote</small>"
msgstr "<small>Autor de esta cita</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "<small>John Doe, CEO</small>"
msgstr "<small>Juan Fulano, Presidente Ejecutivo</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Instalar ahora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<span class=\"fa fa-comment-o\"/>Chat del Sitio Web en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                <strong>Tips for a good domain:</strong>"
msgstr ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                <strong>Consejos para un buen dominio:</"
"strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        <span>Try to limit yourself to 1 testing variable, "
"otherwise you won't be able to clearly interpret the results of your "
"modifications.</span>"
msgstr ""
"<span class = \"fa fa-lightbulb-o fa-2x\" />\n"
"<span>Trate de limitarse a 1 variable de prueba, de lo contrario no será "
"capaz de interpretar con claridad los resultados de sus modificaciones. </ "
"span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "<span class=\"fa fa-pencil\"/> EDIT PAGE"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Instalar ahora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "<span class=\"fa fa-plus\"/> NEW PAGE"
msgstr "<span class=\"fa fa-envelope-o\"/> Escríbale a Nuestro Experto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-thumbs-o-down\"/> <strong> Bad practices</strong>"
msgstr ""
"<span class=\"fa fa-thumbs-o-down\"/> <strong> Malas prácticas</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-thumbs-o-up\"/> <strong> Good practices</strong>"
msgstr "<span class=\"fa fa-thumbs-o-up\"/> <strong> Buenas prácticas</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-cogs\"/>\n"
"                                        <strong>Machine translation</"
"strong><br/>Free, but quality may vary\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-cogs\"/>\n"
"                                        <strong>Traducción automática</"
"strong><br/>Gratuita,pero la calidad puede variar\n"
"                                    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-user\"/>\n"
"                                        <strong>Human translation</"
"strong><br/>Pay for professionnal quality\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-user\"/>\n"
"                                        <strong>Traducción humana</"
"strong><br/>Pague por una calidad profesional\n"
"                                    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-code\"/><strong> 2. "
"Customize its appearance</strong></span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-code\"/><strong> 2. "
"Personalizar su apariencia </ strong> </ span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-magic\"/><strong> 1. Choose "
"your theme</strong></span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-magic\"/><strong> 1. Escoja "
"su tema</strong></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid ""
"<span class=\"sr-only\">Toggle navigation</span>\n"
"                                    <span class=\"icon-bar\"/>\n"
"                                    <span class=\"icon-bar\"/>\n"
"                                    <span class=\"icon-bar\"/>"
msgstr ""
"<span class=\"sr-only\">Alternar navegación</span>\n"
"                                    <span class=\"icon-bar\"/>\n"
"                                    <span class=\"icon-bar\"/>\n"
"                                    <span class=\"icon-bar\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">125</b><small>.00</small>"
msgstr "<span>$</span><b style=\"font-size: 60px\">125</b><small>.00</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">35</b><small>.00</small>"
msgstr "<span>$</span><b style=\"font-size: 60px\">35</b><small>.00</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">65</b><small>.00</small>"
msgstr "<span>$</span><b style=\"font-size: 60px\">65</b><small>.00</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span>3. Select a <strong>translation method</strong></span>"
msgstr "<span>3. Seleccione un <strong>método de traducción<strong></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>... and your location too!</strong><br/>\n"
"                                Millions of people use Google Maps everyday. "
"Whether you are a restaurant\n"
"                                or a huge business, you have no excuse not "
"to register your location in"
msgstr ""
"<strong>... y su ubicación también!</strong><br/>\n"
"                                Millones de personas usan Google Maps todos "
"los días. Así usted tenga un restaurante o un gran negocio, no tiene excusa "
"para no registrar su ubicación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>1. Define what to test</strong><br/>\n"
"                                Your choice of what to test will obviously "
"depend on your goals, but should\n"
"                                always be linked to one of your business "
"objectives (don't spend energy\n"
"                                improving something that brings you no ROI). "
"Then, find the best page to\n"
"                                test and the variation you think will bring "
"the best results.Here are a\n"
"                                few examples of good testing variables:"
msgstr ""
"<strong> 1. Defina que poner a prueba </ strong> <br/>\n"
"Su elección de qué evaluar, obviamente, dependerá de sus metas, pero debe "
"siempre estar vinculada a uno de sus objetivos de negocio (no gaste energía "
"mejorando algo que no le traiga un retorno de la inversión). A continuación, "
"encuentre la mejor página para evaluar y la variación que usted crea traerá "
"los mejores resultados. Aquí hay algunos ejemplos de buenas variables de "
"prueba:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>1. Your main menu items</strong> and 2. Your secondary menu items "
"(optional):"
msgstr ""
"<strong>1. Sus principales opciones de menú</strong> y 2. Sus opciones de "
"menú secundarias (opcional):"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>2. Create the page variation</strong><br/>\n"
"                                Once you're clear on the measurable "
"objective, the page and the variable\n"
"                                you want to test, duplicate the page in Odoo "
"using the <strong>'Versions'</strong>\n"
"                                menu and apply the modification you decided."
msgstr ""
"<strong> 2. Crear la variación de la página </ strong> <br/> Una vez que "
"tenga claro el objetivo medible, la página y la variable que desea probar, "
"duplique la página en Odoo utilizando el menú <strong>'Versiones'</strong> y "
"aplique la modificación que decidió."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>3. Configure a Google Analytics account</strong><br/>\n"
"                                This is necessary in order to record the "
"statistics of your test."
msgstr ""
"<strong>3. Configure una cuenta Google Analytics account</strong><br/>\n"
"                                Esto es necesario para registrar las "
"estadísticas de su sitio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>3. Your footer titles</strong> and 4. Your footer links:"
msgstr ""
"<strong> 3. Sus títulos de pie de página </ strong> y 4. Sus enlaces de pie "
"de página:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>4. Create your campaign in Google Analytics</strong><br/>\n"
"                                Simply follow the wizard by creating a new "
"experiment in the <strong>Behavior &gt;\n"
"                                Experiments</strong> menu of"
msgstr ""
"<strong>4. Crear su campaña en Google Analytics</strong><br/>\n"
"                                Simplemente siga el asistente creando un "
"nuevo experimento en el menú de <strong>Comportamiento &gt;\n"
"                                Experimentos</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid "<strong>50,000+ companies run Odoo to grow their businesses.</strong>"
msgstr ""
"<strong>50.000+ empresas usan Odoo para hacer crecer sus negocios. </ strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Activate now</strong> the optional modules you'll need:"
msgstr ""
"<strong> Activar ahora </ strong> los módulos opcionales que necesitará:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Add features and content</strong><br/>\n"
"                                You can put more content in the intermediate "
"section of your homepage (one\n"
"                                or two scrolls down from the visible "
"section), but try to keep your\n"
"                                paragraphs easy to read and avoid 'walls of "
"text'."
msgstr ""
"<strong> Añada características y contenido </ strong> <br/> Se puede poner "
"más contenido en la sección intermedia de su página principal (uno o dos "
"desplazamientos hacia abajo desde la sección visible), pero trate de "
"mantener sus párrafos fáciles de leer y evite 'paredes de texto'."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Add relevant keywords</strong><br/>\n"
"                                Adding relevant keywords to a web page's "
"meta data, including the title tag\n"
"                                and meta description, will tend to improve "
"the relevancy of a site's search\n"
"                                listings, also increasing your traffic."
msgstr ""
"<Strong> Añada palabras clave relevantes </ strong> <br/> Añadir palabras "
"clave relevantes a los metadatos de una página web, incluyendo la etiqueta "
"del título y meta descripción, tenderá a mejorar la relevancia de las listas "
"de búsqueda de un sitio, incrementando también su tráfico."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Advertise to get the first visitors</strong><br/>\n"
"                                If you have a small budget to allocate to "
"advertising (around $100), you\n"
"                                should start a Google Adwords campaign."
msgstr ""
"<strong>Haga publicidad para obtener los primeros visitantes</strong><br/>\n"
"                                Si usted tiene un presupuesto pequeño para "
"dedicar a la publicidad (alrededor de $100.000), usted debería comenzar una "
"campaña en Google Adwords."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Be announced on the Odoo Twitter account</strong><br/>\n"
"                                We like to hear from people using Odoo to "
"build their website. Tweet us"
msgstr ""
"<strong> Sea anunciado en la cuenta de Twitter de Odoo </ strong> <br/> Nos "
"gustaría escuchar de personas que utilizan Odoo para construir su sitio web. "
"Envíenos un Tweet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Be linked by trusted websites</strong><br/>\n"
"                                The more sites that link to your own site, "
"the more Google trusts your site\n"
"                                to be worthwhile:"
msgstr ""
"<strong>Sea enlazado por sitios web confiables</strong><br/>\n"
"                                Entre más sitios enlacen a su sitio, Google "
"confía más que su sitio es valioso:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Blogs</strong>"
msgstr "<strong>Blogs</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Community builder</strong>"
msgstr "<strong>Constructor comunitario</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Contact form</strong>"
msgstr "<strong>Formulario de contacto</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Contact us for a custom-made theme:</strong>"
msgstr "<strong>Contáctenos para un tema hecho a la medida:</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Contact us now:</strong><br/>"
msgstr "<strong>Contáctenos ahora:</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Create an online Q&amp;A</strong>"
msgstr "<strong>Crear una PF en línea</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Discuss live</strong>"
msgstr "<strong>Charlar en vivo</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Display social proof</strong><br/>\n"
"                                Below your main banner or call-to-action, "
"it's a good practice to put\n"
"                                social proofs about your products or "
"services; customer quotes, videos,\n"
"                                photos of your products in actions, twitter "
"quotes, etc."
msgstr ""
"<strong>Presentar una prueba social</strong><br/>\n"
"Bajo su banner principal o llamado a la acción, es una buena práctica poner "
"pruebas sociales acerca de sus productos o servicios: testimonios de "
"clientes, videos, fotos de sus productos en acción, menciones en twitter, "
"etc."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Do many A/B tests.</strong><br/>\n"
"                                Chances are, your first A/B test will turn "
"out a lemon. But don’t despair. An A/B test can have only three outcomes: no "
"result, a negative result or a\n"
"                                positive result. The key to optimizing "
"conversion rates is to do a ton of A/B\n"
"                                tests, so that all positive results add up "
"to a huge boost to your sales and\n"
"                                achieved goals."
msgstr ""
"<strong>Haga varias pruebas A/B.</strong><br/>\n"
"Lo más probable, es que su primer prueba A/B no sea contundente. Pero no "
"desista. Una prueba A/B puede tener sólo tres conclusiones: sin resultados, "
"un resultado negativo o uno positivo. La clave para optimizar la tasa de "
"conversión es hacer muchísimas pruebas A/B, para que los resultados "
"positivos impulsen sus ventas y el logro de sus objetivos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Don't test versions at different time periods.</strong><br/>If you\n"
"                                test one version one week and the second the "
"next, your results won't be\n"
"                                accurate, or worse, wrong!"
msgstr ""
"<strong> No haga pruebas de versiones en diferentes períodos de tiempo. </ "
"strong> <br/> Si usted prueba una versión una semana y la segunda la "
"siguiente, sus resultados no serán precisos, o peor, serán incorrectos!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Don’t conclude too early.</strong><br/>\n"
"                                Wait for your test results to be "
"significant. Take a look at the statistical\n"
"                                confidence in Google Analytics, it should be "
"at least 95%."
msgstr ""
"<strong>No concluya prematuramente.</strong><br/>\n"
"                                Espere a que los resultados de sus pruebas "
"sean significantes. Mire la confianza estadística en Google Analytics, "
"debería ser por lo menos del 95%."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Don’t surprise regular visitors.</strong><br/>\n"
"                                If you are testing a core part of your "
"website, include only new visitors in\n"
"                                the test. You want to avoid shocking regular "
"visitors, especially because the\n"
"                                variations may not ultimately be implemented."
msgstr ""
"<strong> No sorprenda a los visitantes regulares. </ strong> <br/> Si está "
"probando una parte fundamental de su sitio web, incluya sólo nuevos "
"visitantes en la prueba. Usted quiere evitar impactar a los visitantes "
"regulares, sobre todo debido a que las variaciones pueden finalmente no ser "
"implementadas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Encourage to action</strong><br/>\n"
"                                Add  one, and only one, call-to-action on "
"your homepage. Your homepage is\n"
"                                the page that gets the most visitors.<br/>\n"
"                                It's good practice to try to engage with "
"your visitors on the homepage:\n"
"                                Contact Us, Subscribe to Get More "
"Information, Check the Catalog, etc."
msgstr ""
"<strong> Anime a la acción </ strong> <br/> Añada una, y sólo una, llamada a "
"la acción en su página principal. Su página principal es la página que "
"obtiene la mayoría de los visitantes <br/> Es una buena práctica el tratar "
"de enganchar a sus visitantes en la página principal: Contáctenos, "
"Suscribirse para Obtener Más Información, Consultar el Catálogo, etc."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr "<strong>Mensaje de error:</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Find 3 websites that you like</strong> and write down what you like "
"about them."
msgstr ""
"<strong>Encuentre 3 sitios web que le gusten</strong> y escriba lo que le "
"gusta de ellos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Find a catchy headline</strong><br/>\n"
"                                Make sure anyone who visits your site "
"understands what it’s about without having to do a lot of reading.<br/>\n"
"                                To do that, the visible section of your "
"homepage (above the fold) needs to be simple and straightforward. For "
"example, use pictures of your work, screenshots of your app, a short and "
"catchy hook."
msgstr ""
"<strong>Escriba un título llamativo</strong><br/>\n"
"                                Asegúrese que cualquiera que visite su "
"sitio, comprenda de lo que se trata sin tener que leer mucho.<br/>\n"
"                                Para lograrlo, su sección visible de su "
"página de inicio (encima de la cubierta) necesita ser simple y directa. Por "
"ejemplo, use imágenes de su trabajo, capturas de pantalla de sus "
"aplicaciones, un anzuelo corto y atrapante."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>How to get a custom domain name ?</strong><br/>"
msgstr "<strong>Cómo obtener un nombre de dominio personalizado?</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Initial Teaser</strong><br/>\n"
"                                Before launching to the masses, it's good "
"practice to tease around 100 of\n"
"                                your contacts. Start with your friends, "
"colleagues and family. Whether or\n"
"                                not they're interested in your area of "
"business, they will be ready to help\n"
"                                you with feedback if you engage in a "
"personal discussion with them."
msgstr ""
"<Strong> Teaser inicial </ strong> <br/> \n"
"Antes de realizar el lanzamiento al publico, es una buena práctica  tantear "
"alrededor de 100 de\n"
"sus contactos. Comience con sus amigos, colegas y familiares. Esten o \n"
"No esten interesados en su área de negocio, que estaran listos para ayudar\n"
"con retroalimentación si se involucra en una discusión personal con ellos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Jobs</strong>"
msgstr "<strong>Trabajos</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Know how long to run a test before giving up.</strong><br/>\n"
"                                Giving up too early can cost you because you "
"may have gotten meaningful\n"
"                                results had you waited a little longer. "
"Giving up too late isn’t good either,\n"
"                                because poorly performing variations could "
"cost you conversions and sales."
msgstr ""
"<strong> Para saber en cuánto tiempo se ejecuta una prueba antes de "
"abandonar. </ strong> <br/> Renunciar demasiado pronto puede costar ya que "
"para haber conseguido resultados significativos tuvieron que esperar un poco "
"más. Renunciar demasiado tarde tampoco es bueno, ya que las variaciones de "
"bajo rendimiento  podría costar conversiones y ventas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Now try to write down you footer structure:</strong>"
msgstr ""
"<strong> Ahora trate de escribir la estructura de pie de página: </ strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Optimize your content</strong><br/>\n"
"                                Writing content that includes frequently "
"searched-for keywords and phrases\n"
"                                tends to increase  traffic to your website."
msgstr ""
"<strong> Optimice su contenido </ strong> <br/> Escribir contenido que "
"incluye la frecuencia buscada por palabras clave y frases tiende a aumentar "
"el tráfico a su sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Organize physical events</strong>"
msgstr "<strong> Organizar eventos físicos </ strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Reference your images...</strong><br/>\n"
"                                If you are an artist or a photographer, you "
"can potentially bring in a lot\n"
"                                of  visitors by referencing your images so "
"they are displayed in image\n"
"                                search engines, like Google Images. To do "
"that, simply add an 'Alt text'\n"
"                                containing descriptions and keywords for "
"relevant pictures."
msgstr ""
"<strong>Referencie sus imágenes...</strong><br/>\n"
"                                Si usted es un artista o un fotógrafo, usted "
"potencialmente puede atraer gran cantidad de visitantes referenciando sus "
"imágenes, así son mostradas en los motores de búsqueda, como Google Images. "
"Para hacer eso, simplemente añada un 'Texto Alternativo' conteniendo "
"descripciones y palabras clave para las imágenes relevantes."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Start a blog</strong><br/>\n"
"                                Google loves websites which are updated "
"often or ones where new content is\n"
"                                added regularly. This is why starting a blog "
"is an excellent way of\n"
"                                improving your search ranking. If you have "
"the time and dedication to write\n"
"                                good articles,"
msgstr ""
"<strong>Comience un blog</strong><br/>\n"
"Google ama los sitios web que son actualizados frecuentemente o donde nuevo "
"contenido\n"
"es añadido regularmente. Esta es la razón por la que iniciar un blog es una "
"excelente manera\n"
"de mejorar su ranking de búsqueda. Si usted tiene tiempo y dedicación para "
"escribir\n"
"buenos artículos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>The big launch</strong><br/>"
msgstr "<strong>El gran lanzamiento</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Use Google Webmaster</strong><br/>\n"
"                                This tool will help you understand your "
"search traffic and learn how users\n"
"                                are finding your site and make optimizations "
"to help Google better\n"
"                                understand and represent your site."
msgstr ""
"<strong>Use Google Webmaster</strong><br/>\n"
"                                Esta herramiento le ayuda a entender su "
"tráfico y aprender cómo los visitantes están encontrando su sitio para hacer "
"optimizaciones y que Google entienda y represente mejor su sitio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Use contact forms:</strong>"
msgstr "<strong>Use formularios de contacto:</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>With that in mind, try to define the structure of your main menu:</"
"strong>"
msgstr ""
"<strong>Con eso en mente, trate de definir la estructura de su menú "
"principal:</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>You have your domain name ?</strong><br/>"
msgstr "<strong>Usted tiene su nombre de dominio?</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Your website objectives</strong> by priority:"
msgstr "<strong>Los objetivos de su sitio web</strong> por prioridad:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>e-Commerce</strong>"
msgstr "<strong>Comercio Electrónico</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "@odoo"
msgstr "@odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid "A Great Headline"
msgstr "Un Gran Encabezado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "A Punchy Headline"
msgstr "Un Título Impactante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "Un Subtítulo de Sección"

#. module: website
#: model:ir.model.fields,help:website.field_ir_act_server_website_published
#, fuzzy
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"Se puede ejecutar desde el sitio web una acción de código en el servidor, "
"usando un controlador dedicado. La dirección es <base>/website/action/"
"<ruta_sitio_web>. Marque esta casilla para permitir a los usuarios ejecutar "
"esta acción. Si está desmarcada, la acción no se podrá ejecutar a través del "
"sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"A footer, as its name implies, is located at the very bottom of your "
"website.\n"
"                        Because of that, your visitors won't see it "
"immediately so it's very useful for linking\n"
"                        pages you don't want in your main menu e.g. social "
"media, terms and conditions, privacy\n"
"                        terms, etc."
msgstr ""
"Un pie de página, como su nombre lo indica, está situado en la parte "
"inferior de su sitio web. Debido a eso, los usuarios no lo verán de "
"inmediato por lo que es muy útil para vincular las páginas que no desea en "
"su menú principal, por ejemplo, medios de comunicación social, los términos "
"y condiciones, términos de privacidad, etc."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid "A good subtitle"
msgstr "Un buen subtítulo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"A good way to start is to use machine translation then apply corrections "
"yourself."
msgstr ""
"Una buena manera de comenzar es usar una traduccion automática y luego "
"corregir usted mismo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"A great way to catch your reader's attention is to tell a story.\n"
"                        Everything you consider writing can be told as a "
"story."
msgstr ""
"Una gran manera de captar la atención del lector es contar una historia. "
"Todo se tiene en cuenta la escritura puede ser contada como una historia."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great<br/>feature, in clear words."
msgstr ""
"Una pequeña explicación de esta gran función <br/>, en palabras claras."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "A/B Testing"
msgstr "Pruebas A/B"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_module_website_version
msgid "A/B testing and versioning"
msgstr "Pruebas A/B y control de versiones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"A/B testing is the process of testing two versions of a page to see which "
"one performs\n"
"                        better in reaching your business objectives. For "
"example, imagine you have an existing\n"
"                        page (A) where you ask your customers to subscribe "
"to your mailing list.\n"
"                        You create a variation (B) of this page with a "
"slightly different subscription message.\n"
"                        Then your randomly display this page to half the "
"visitors of your website, and after a\n"
"                        while, you analyze the subscription statistics and "
"see that page B performs 10% better\n"
"                        than page A. Of course, you now decide to make B "
"your new current page!"
msgstr ""
"Las pruebas A/B es el proceso de probar dos versiones de una páginas para "
"ver cual rinde mejor alcanzando los objetivos de su negocio. Por ejemplo, "
"imagine que tiene una página (A) donde invita a sus clientes a suscribirse a "
"su lista de correo.\n"
"Usted crea una variación (B) de esta página con un mensaje de invitación "
"ligeramente diferente. Luego aleatoriamente muestra esta página a la mitad "
"de los visitantes de su sitio web, y luego de un tiempo, analiza las "
"estadísticas de suscripción y ve que la página B rinde 10% mejor que la "
"página A. Desde luego, usted decide entonces hacer que B sea su página "
"oficial!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "About us"
msgstr "Sobre Nosotros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "Administración de Cuentas y Ventas"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_activated
msgid "Activate CDN for assets"
msgstr "Activar CDN para los recursos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit you design need.\n"
"                        To duplicate, delete or move columns, select the\n"
"                        column and use the top icons to perform your action."
msgstr ""
"Adapte esas tres columnas para adaptar su diseño. Para duplicar, eliminar o "
"mover columnas, seleccione la columna y utilice los iconos superiores para "
"realizar su acción."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:37
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.snippets.gallery.js:248
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#, python-format
msgid "Add Images from the 'Customize' menu"
msgstr "Añadir imágenes desde el menú 'Personalizar'"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:55
#, python-format
msgid "Add Menu Entry"
msgstr "Añadir Opción de Menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "Añadir Diapositiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Add a great slogan"
msgstr "Añadir un gran eslogan"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:18
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Add images"
msgstr "Añadir imágenes"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:20
#, python-format
msgid "Add keyword:"
msgstr "Añadir palabra clave:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:111
#, fuzzy, python-format
msgid "Add new pages"
msgstr "Añadir nuevas páginas y menús"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:68
#, python-format
msgid "Add page in menu"
msgstr "Añadir página en el menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Advanced"
msgstr "Avanzada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"After working on this step, you may want to go back and refine your "
"objectives or modify your visitors interests. Those 3 first steps are "
"essentially linked, and the more time your spend on polishing them, the "
"better your website will be!"
msgstr ""
"Después de trabajar en este paso, es posible que desee volver atrás y "
"refinar sus objetivos o modificar sus visitantes interesados. Esos 3 "
"primeros pasos están esencialmente vinculados, y cuanto más tiempo su "
"inversión en pulirlos, el mejor sitio web será!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Aim for a .com and/or your country extension"
msgstr "Apunte a un dominio .com o .co"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"All these icons are licensed under creative commons so that you can use them."
msgstr ""
"Todos estos iconos están licenciados bajo creative commons para que usted "
"pueda utilizarlos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Allows your customers and the whole community to ask and answer questions "
"about their interests."
msgstr ""
"Le permite a sus clientes y a toda la comunidad preguntar y responder "
"preguntas acerca de sus intereses."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Also, don't hesitate to post on other people blogs, forums, websites, but "
"don't go overboard and post everywhere without considering whether it's a "
"good place for you to be."
msgstr ""
"También, no dude en publicar en blogs de otras personas, foros, sitios web, "
"pero no exagere y publique en todas partes sin considerar si se trata de un "
"buen lugar para que usted esté."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Alternatively, you can write a good article about your business and publish "
"it on an article database websites (ex: squidoo.com) or send it to a press "
"website and your local newspapers."
msgstr ""
"Como alternativa, puede escribir un buen artículo sobre su negocio y  "
"publicarlo en sitios web  de base de datos del articulo (por ejemplo: "
"squidoo.com) o enviarlo a un sitio web de prensa y los periódicos locales."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Amount of text on the page (short vs. long)."
msgstr "El monto del texto en la página (largo vs. corto)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "An error occured while rendering the template"
msgstr "Ocurrió un error dibujando la plantilla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "And a great subtitle too"
msgstr "Y un gran subtítulo también"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Announce it on social media (and update your profiles): Twitter,Facebook, "
"LinkedIn, Youtube, etc."
msgstr ""
"Anuncialo en las redes sociales (y actualizar su perfil): Twitter, Facebook, "
"LinkedIn, Youtube, etc."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Apply"
msgstr "Aplicar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Aqua"
msgstr "Aguamarina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, fuzzy
msgid ""
"As you add more pages to your website, your main menu will begin to feel "
"overcrowded.\n"
"                            You can regroup similar pages into a sub-menu, "
"so they will appear under a unique drop-down menu."
msgstr ""
"A medida que agrega más páginas a su sitio web, el menú principal comenzará "
"a sentirse saturado.\n"
"Podría agrupar páginas similares en un submenú, por lo que va a aparecer en "
"un menú desplegable único."

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment_website_url
msgid "Attachment URL"
msgstr "URL Adjunto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Attract more leads with a winning content marketing strategy."
msgstr "Atrae más iniciativas con una estrategia de marketing de contenidos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Attract new leads"
msgstr "Atraer nuevas iniciativas"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_published
msgid "Available on the Website"
msgstr "Disponible en el Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Avoid special characters"
msgstr "Evitar caracteres especiales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Baby Blue"
msgstr "Azul Bebé"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Back"
msgstr "Atrás"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, fuzzy
msgid "Background Color"
msgstr "Fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, fuzzy
msgid "Background Image"
msgstr "Imagen de fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, fuzzy
msgid "Background Image Options"
msgstr "Imagen de fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Background image"
msgstr "Imagen de fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Banner Odoo Image"
msgstr "Imagen Banner de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "Ventas básicas y marketing de hasta 2 usuarios"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:70
#, python-format
msgid "Be careful !"
msgstr "¡Tenga cuidado!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Be mobile"
msgstr "Se móvil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Before starting to work on your website, make sure you have all the right "
"applications installed.\n"
"                        It will be much easier for you to organize and "
"create your content if you have all the options activated from the start."
msgstr ""
"Antes de comenzar a trabajar en su sitio web, asegúrese de que tiene todas "
"las aplicaciones adecuadas instaladas.\n"
"Será mucho más fácil para usted organizar y crear su contenido si usted "
"tiene todas las opciones activadas desde el principio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Before starting,"
msgstr "Antes de comenzar,"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Principiante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Being clear on why you're creating your site and your goals is an essential "
"first step to ensure your content and structure meet business objectives."
msgstr ""
"Siendo claros en el por qué está creando su sitio web y sus metas, es un "
"primer paso esencial para asegurar que su contenido y estructura cumplan los "
"objetivos del negocio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr "Grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bigger Text"
msgstr "￼Texto más grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Black"
msgstr "Negro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Boost your online sales with sleek product pages."
msgstr ""
"Increment sus ventas en líneas con páginas elegantes para los productos."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:14
#, python-format
msgid "Build a page"
msgstr "Crear una página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Build and engage with communities with forums, Q&amp;A, mailing lists."
msgstr ""
"Construir y comprometerse con la comunidad con foros, Q&amp;A, listas de "
"correo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Build awareness and credibility"
msgstr "Construya conciencia y credibilidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Business Guy"
msgstr "Hombre de Negocios"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_url
#: model:ir.model.fields,field_description:website.field_website_config_settings_cdn_url
msgid "CDN Base URL"
msgstr "URL Base CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_filters
#: model:ir.model.fields,field_description:website.field_website_config_settings_cdn_filters
msgid "CDN Filters"
msgstr "Filtros CDN"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Can I afford to advertise on the keyword?"
msgstr "Pedir la palabra clave para publicar?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Can I use it to manage projects based on agile methodologies?"
msgstr ""
"¿Puedo usarlo para gestionar proyectos basados en una metodología ágil?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:63
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Change Background"
msgstr "Cambiar Fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Change Icons"
msgstr "Cambiar Iconos"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:96
#, python-format
msgid "Check Mobile Preview"
msgstr "Ver la Previsualización Móvil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Check its availability"
msgstr "Verificar su disponibilidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Check your references before deciding to work with you"
msgstr "Compruebe sus referencias antes de decidirse a trabajar"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_child_id
msgid "Child Menus"
msgstr "Sub Menús"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph\n"
"                        about it. It does not have to be long, but it "
"should\n"
"                        reinforce your image."
msgstr ""
"Elija una imagen vibrante y escriba un párrafo inspirador. No tiene por qué "
"ser largo, pero debe reforzar su imagen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr "Círculo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:43
#, python-format
msgid "Click in the text and start editing it."
msgstr "Clic en el texto y comience a editarlo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Click on 'Translate' on the menu bar"
msgstr "Clic en 'Traducir' en la barra de menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Click on the icon to adapt it to your feature"
msgstr "Clic en el icono para adaptarlo a su característica"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:49
#: model_terms:ir.ui.view,arch_db:website.s_banner
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#, python-format
msgid "Click to customize this text"
msgstr "Clic para personalizar este texto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.ace.xml:14
#: code:addons/website/static/src/xml/website.gallery.xml:97
#: code:addons/website/static/src/xml/website.translator.xml:13
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:113
#, python-format
msgid "Close Tutorial"
msgstr "Cerrar Tutorial"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color Splash"
msgstr "Splash de Color"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Colors palette"
msgstr "Paleta de colores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr "Columnas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Communicate"
msgstr "Comunicar"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_company_id
msgid "Company"
msgstr "Compañía"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Company name"
msgstr "Nombre de la compañía"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "CRM completo para cualquier tamaño de equipo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_compress_html
msgid "Compress HTML"
msgstr "Comprimir HTML"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_compress_html
msgid "Compress rendered HTML for a better Google PageSpeed result"
msgstr "Comprimir HTML generado para un mejor resultado de Google PageSpeed"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Configure Website"
msgstr "Configurar el Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Configure all your social media (Twitter, Facebook, LinkedIn) so that the "
"address of your website appears on them."
msgstr ""
"Configurar todos las redes sociales (Twitter, Facebook, LinkedIn) para que "
"aparezca en ellos la dirección de su sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Configure website menus"
msgstr "Configurar menús del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Configure your Gengo API key in"
msgstr "Configure su clave de API Gengo en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Congratulations, you're done !"
msgstr "¡Enhorabuena, ha Terminado!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "Connect with us"
msgstr "Conéctate con nosotros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
msgid "Contact Us"
msgstr "Contáctenos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Contact a Domain Manager in your country to buy it"
msgstr ""
"Póngase en contacto con un administrador de dominio en su país para comprarlo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model_terms:ir.ui.view,arch_db:website.s_banner
#: model_terms:ir.ui.view,arch_db:website.s_big_message
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#: model_terms:ir.ui.view,arch_db:website.website_planner
#: model:website.menu,name:website.menu_contactus
#: model:website.menu,name:website.website2_menu_contactus
msgid "Contact us"
msgstr "Contáctenos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "Contact us about anything related to our company or services."
msgstr ""
"Contáctenos acerca de cualquier cosa relacionada a nuestra compañía o "
"servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "Contact us »"
msgstr "Contáctenos »"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "Content"
msgstr "Continuar"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:24
#, python-format
msgid "Content to translate"
msgstr "Contenido a traducir"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:61
#: code:addons/website/static/src/js/website.tour.banner.js:83
#: code:addons/website/static/src/js/website.tour.banner.js:105
#: code:addons/website/static/src/xml/website.contentMenu.xml:28
#: code:addons/website/static/src/xml/website.xml:62
#, python-format
msgid "Continue"
msgstr "Continuar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Copyright &amp;copy;"
msgstr "Copyright &amp;copy;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Crear Página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Create a community"
msgstr "Crear una comunidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Create a new project (this may take some time)"
msgstr "Crear un nuevo proyecto (esto puede tomar algo de tiempo)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.editor.js:133
#, python-format
msgid "Create page '%s'"
msgstr "Crear página '%s'"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_create_uid
#: model:ir.model.fields,field_description:website.field_website_create_uid
#: model:ir.model.fields,field_description:website.field_website_menu_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_create_date
#: model:ir.model.fields,field_description:website.field_website_create_date
#: model:ir.model.fields,field_description:website.field_website_menu_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Creating your main menu is an important and difficult stage in building your "
"website.<br/>\n"
"                        It needs to be organized, so that your visitors can "
"see all the main content you offer at a glance.<br/>\n"
"                        It needs to be clear, so that when they click on an "
"item, they get the content they expected.<br/>\n"
"                        In fact, you need to build your navigation menu for "
"your visitors and not for your own purposes or objectives."
msgstr ""
"La creación del menú principal es una etapa importante y difícil en la "
"construcción de su sitio web.<br/>\n"
"Debe ser organizado, para que sus visitantes puedan ver todo lo que usted "
"ofrece de un vistazo.<br/>\n"
"Tiene que ser clara, de manera que al hacer clic en un elemento, se reciba "
"el contenido esperado.<br/>\n"
"En realidad, usted necesita para construir un menú de navegación para sus "
"visitantes y no para sus propios fines u objetivos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "Customize"
msgstr "Personalizar Tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize Theme"
msgstr "Personalizar Tema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:60
#, python-format
msgid ""
"Customize any block through this menu. Try to change the background of the "
"banner."
msgstr ""
"Personalice cualquier bloque a través de este menú. Intente cambiar el fondo "
"del banner."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:42
#, python-format
msgid "Customize banner's text"
msgstr "Personalice el texto del banner"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:59
#, python-format
msgid "Customize the banner"
msgstr "Personalice el banner"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_default_lang_id
#: model:ir.model.fields,field_description:website.field_website_default_lang_id
msgid "Default language"
msgstr "Idioma predeterminado"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_default_lang_code
#: model:ir.model.fields,field_description:website.field_website_default_lang_code
msgid "Default language code"
msgstr "Código del idioma predeterminado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Delete Blocks"
msgstr "Borrar Bloques"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:129
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Delete Page"
msgstr "Borrar Página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture\n"
"                        that illustrates your message. Click on the picture "
"to\n"
"                        change it's <em>rounded corner</em> style."
msgstr ""
"Elimine la imagen de arriba o reemplacela por una imagen que ilustre su "
"mensaje. Haga clic en la imagen para cambiar su estilo de <em>esquinas "
"redondeadas</em>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Deploy new stores with just an internet connection: no\n"
"                                installation, no specific hardware required. "
"It works with any\n"
"                                iPad, Tablet PC, laptop or industrial POS "
"machine."
msgstr ""
"Implementar nuevas tiendas con sólo una conexión a Internet: sin\n"
"instalación, no requiere hardware específico. Funciona con cualquier\n"
"iPad, Tablet PC, portátil o máquina POS industrial."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:54
#, python-format
msgid "Description"
msgstr "Descripción"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:109
#, python-format
msgid "Description..."
msgstr "Descripción..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Design"
msgstr "Diseño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disable autoplay"
msgstr "Sin reproducción automática"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:364
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Discuss and Comments"
msgstr "Charlas y Comentarios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Discuss with propects for a face-to-face sales pitch."
msgstr "Charle con prospectos para hacerles un pitch de ventas cara-a-cara."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_display_name
#: model:ir.model.fields,field_description:website.field_website_display_name
#: model:ir.model.fields,field_description:website.field_website_menu_display_name
#: model:ir.model.fields,field_description:website.field_website_published_mixin_display_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:43
#, python-format
msgid "Do not show this dialog later."
msgstr "No mostrar este cuadro de diálogo después."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Does it works offline?"
msgstr "¿Funciona fuera de línea?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Domain"
msgstr "Dominio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Don't hesitate to"
msgstr "No dude en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Download the technical documentation of a product"
msgstr "Descargar la documentación técnica de un producto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:67
#, python-format
msgid "Drag & Drop This Block"
msgstr "Arrastrar y Soltar Este Bloque"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:34
#, python-format
msgid "Drag & Drop a Banner"
msgstr "Arrastrar y Soltar un Banner"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:50
#, python-format
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Arrastre un menú a la derecha para crear un sub-menú"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:68
#, python-format
msgid "Drag the <em>'Features'</em> block and drop it below the banner."
msgstr ""
"Arrastre el bloque <em>'Características'</em> y suéltelo  por debajo del "
"banner."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:35
#, python-format
msgid "Drag the Banner block and drop it in your page."
msgstr "Arrastre el bloque del banner y suéltelo en su página."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Duplicate"
msgstr "Duplicar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Duplicate blocks to add more features."
msgstr "Duplicar bloque para añadir más características."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Easy to remember and spell"
msgstr "Fácil de recordar y deletrear"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit"
msgstr "Editar"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:38
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Editar Menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "Edit Top Menu"
msgstr "Editar Menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit in backend"
msgstr "Editar en el backend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"Edit the content below this line to adapt the default \"page not found\" "
"page."
msgstr ""
"Edite el contenido a continuación de esta línea para adaptar la página por "
"defecto de \"Página no encontrada\"."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:27
#, python-format
msgid "Edit this page"
msgstr "Editar esta página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "Soporte por Correo Electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "End"
msgstr "Fin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Enterprise package"
msgstr "Paquete enterprise"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Error"
msgstr "Error"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:28
#, python-format
msgid ""
"Every page of your website can be modified through the <i>Edit</i> button."
msgstr ""
"Cada página de su sitio web puede ser modificada a través del botón "
"<i>Editar</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Example of Good Footer"
msgstr "Ejemplo de un Buen Pie de Página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Example of Good Homepage"
msgstr "Ejemplo de una Buena Página Inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Example of Good Menu"
msgstr "Ejemplo de un Buen Menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Examples"
msgstr "Ejemplos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Experto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_well
msgid ""
"Explain the benefits you offer. Don't write about products or\n"
"            services here, write about solutions."
msgstr ""
"Explique los beneficios que ofrece. No escriba sobre sus productos o "
"servicios aquí, escriba sobre soluciones."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Explain to them why you launched your new website and ask them for their\n"
"                                    views on it. Avoid using a mass survey, "
"it's a lot more efficient to have\n"
"                                    personal discussions with everyone. If "
"you efficiently engage them in the\n"
"                                    reviewing process, they will even help "
"you promote your website when it's\n"
"                                    ready for the big launch."
msgstr ""
"Explique por qué lanzó su nuevo sitio web y pregunte sus puntos de vista "
"sobre\n"
"ella. Evita el uso de una encuesta masiva y es mucho más eficiente que "
"tener\n"
"discusiones personales con cada uno de ellos. Si les involucra "
"eficientemente en el\n"
"proceso de revisión, incluso le ayudará a promover su sitio web cuando este\n"
"listo para el lanzamiento."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Extra Features"
msgstr "Características Extra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "Extra-Grande"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_facebook
#: model:ir.model.fields,field_description:website.field_website_social_facebook
msgid "Facebook Account"
msgstr "Cuenta Facebook"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fast"
msgstr "Rápido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Característica Uno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Característica Tres"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_panel
msgid "Feature Title"
msgstr "Título de la Característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Característica Dos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Finally don't try to make all your content accessible from the main menu or "
"the footer.\n"
"                            Indeed, a page can also be accessible through a "
"direct link or button, from any other page of your website."
msgstr ""
"Finalmente, cohibirse de crear todos los contenidos accesibles desde el menú "
"principal o el pie de página.\n"
"También, una página puede ser también accesible a través de un enlace o "
"botón directo, desde cualquier otra página de su sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Find Inspiration"
msgstr "Encuentra Inspiración"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Find a good domain name (see tips on the right)"
msgstr "Encontrar un buen nombre de dominio (vea los consejos a la derecha)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Primera Característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fixed"
msgstr "Fijo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Float"
msgstr "Número flotante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flowers Field"
msgstr "Campo de Flores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Folded list"
msgstr "Lista recogida"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Fonts"
msgstr "Fuentes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For example, use photos to help break up large areas of text by altering\n"
"                                    Text-Image and Image-Text blocks. Or if "
"you prefer to use icons, use the Features or Feature Grid blocks."
msgstr ""
"Por ejemplo, utilice fotos para ayudar a romper grandes áreas de texto "
"mediante la alternación de bloques de Texto-Imagen e Imagen-Texto. O si "
"prefiere usar iconos, use los bloques de Características o Tablilla de "
"Características."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For example, you'll want your company name, your country/city and a few\n"
"                                    words describing your business in your "
"homepage title so that potential\n"
"                                    customers near your location will find "
"your website easily.\n"
"                                    WIth Odoo, this can be done easily by "
"using the Promote menu."
msgstr ""
"Por ejemplo, usted quiere que aparezca en el título de su página web el "
"nombre de su empresa, país/ciudad y algunas palabras \n"
"que describen su empresa para que los clientes potenciales cercanos a la "
"ubicación encontren su sitio web fácilmente.\n"
"Con Odoo, esto se puede hacer fácilmente usando el menú Promover."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"De parte del equipo Odoo,<br/>\n"
"Fabien Pinckaers, Fundador"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_module_website_form_editor
msgid "Form builder: create and customize forms"
msgstr "Constructor de formularios: crear y personalizar formularios"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.ace.xml:13
#, python-format
msgid "Format"
msgstr "Formato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Form’s length and types of fields,"
msgstr "Tamaño del formulario y tipos de campos,"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"From the main container, you can change the background to highlight features."
msgstr ""
"Desde el contenedor principal, puede cambiar el fondo para destacar "
"características."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Gengo module"
msgstr "Módulo Gengo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Obtener acceso a todos los módulos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Obtener acceso a todos los módulos y características"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Get an understanding of your methodology"
msgstr "Obtener alcance de su metodología "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:52
#, python-format
msgid "Get banner properties"
msgstr "Obtener propiedades del banner"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:361
#, python-format
msgid ""
"Get this page efficiently referenced in Google to attract more visitors."
msgstr ""
"Consiga referenciar eficientemente esta página en Google para atraer más "
"visitantes."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_github
#: model:ir.model.fields,field_description:website.field_website_social_github
msgid "GitHub Account"
msgstr "Cuenta GitHub"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Go to the page you want to translate"
msgstr "Vaya a la página que quiere traducir"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:81
#, python-format
msgid "Good Job!"
msgstr "¡Buen trabajo!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_google_analytics_key
#: model:ir.model.fields,field_description:website.field_website_google_analytics_key
msgid "Google Analytics Key"
msgstr "Llave de Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Analytics."
msgstr "Google Analytics."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Business"
msgstr "Google Business"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google's developer console"
msgstr "Consola de desarrollador de Google"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_googleplus
#: model:ir.model.fields,field_description:website.field_website_social_googleplus
msgid "Google+ Account"
msgstr "Cuenta Google+"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Great Value"
msgstr "Gran Valor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid "Great products for great people"
msgstr "Grandes productos para una gran gente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Greenfields"
msgstr "Campos verdes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "Cuadrícula"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Grow"
msgstr "Crecimiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HELP &amp; TUTORIALS"
msgstr "AYUDA &amp; TUTORIALES"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML Editor"
msgstr "Editor HTML"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP routing"
msgstr "Enrutado HTTP"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Headline or product description,"
msgstr "Encabezado o la descripción del producto,"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Help"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:21
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr ""
"Aquí están los visuales utilizados para ayudarle a traducir eficientemente:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Here is a basic guide for your first A/B testing:"
msgstr "Aquí una guía básica para su primera prueba A/B:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model:website.menu,name:website.menu_homepage
#: model:website.menu,name:website.website2_menu_homepage
msgid "Home"
msgstr "Inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Homepage"
msgstr "Página de inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
msgid "Homepage 0.0.0.0"
msgstr "Página de inicio 0.0.0.0"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:110
#, python-format
msgid "I'm sure, I want to delete this page definitively"
msgstr "Estoy seguro, quiero eliminar definitivamente esta página"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_id
#: model:ir.model.fields,field_description:website.field_website_id
#: model:ir.model.fields,field_description:website.field_website_menu_id_2055
#: model:ir.model.fields,field_description:website.field_website_published_mixin_id
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_id
msgid "ID"
msgstr "ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset one or more templates to their <strong>factory "
"settings</strong>."
msgstr ""
"Si este error es causado por un cambio de sus plantillas, usted tiene la "
"posibilidad de restablecer una o más de sus plantillas a los <strong>valores "
"predeterminados</strong>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"If you answer yes to these 3 questions, then go ahead and position your "
"website on these keywords."
msgstr ""
"Si su respuesta es sí a estas 3 preguntas, continuar y posicionar su sitio "
"web en estas palabras clave."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"If you have a lot of pages in your website, you can create a small top-menu "
"to focus\n"
"                            on the main six pages and make all secondary "
"pages accessible from a footer. You can\n"
"                            have a look at the Odoo.com website, it's a "
"great example of this type of structure."
msgstr ""
"Si usted tiene muchas páginas web en su sitio, usted puede crear un pequeño "
"menú superior para enfocarse en las seis secciones principales y hacer "
"accesibles todas las páginas secundarias desde el pie de página. Dele una "
"mirada al sitio web de Odoo.com, es un buen ejemplo de este tipo de "
"estructura."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Images on landing and product pages,"
msgstr "Imágenes en páginas de productos y de inicio,"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images spacing"
msgstr "Espaciado de imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Improve your conversion from visitor to customer."
msgstr "Mejore su conversión de visitante a cliente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"In the left-side menu of the project, go to <strong>APIs and auth &gt; APIs</"
"strong> and activate <strong>Analytics API</strong> by clicking on the"
msgstr ""
"En el menú de la izquierda del proyecto, vaya a <strong>APIs y autenticación "
"&gt; APIs</strong> y activar <strong>Analytics API</strong> haciendo clic en "
"el"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"In the pop-up window, simply enter your domain name in the first textbox "
"(the second will complete automatically)"
msgstr ""
"En la ventana emergente, sólo tiene que introducir su nombre de dominio en "
"el primer cuadro de texto (el segundo, se completará automáticamente)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:32
#, python-format
msgid ""
"In this mode, you can only translate texts.  To\n"
"                            change the structure of the page, you must edit "
"the\n"
"                            master page. Each modification on the master "
"page\n"
"                            is automatically applied to all translated\n"
"                            versions."
msgstr ""
"En este modo, sólo puede traducir textos. Para cambiar la estructura de la "
"página,\n"
"debe editar la página maestra. Cada modificación en la página maestra se "
"aplica\n"
"automáticamente aplicada a todas las versiones traducidas."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.ace.xml:10
#, python-format
msgid "Include Asset Bundles"
msgstr "Paquetes de Activos Incluidos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Información sobre el"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Install Apps"
msgstr "Instalar Apps"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Instalar idioma"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Install the"
msgstr "Instalar el"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Aplicaciones Instalados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Modules"
msgstr "Módulos Instalados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Integrate a contact form that automatically creates leads."
msgstr ""
"Integrar un formulario de contacto que automáticamente cree iniciativas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Internal Server Error"
msgstr "Error Interno del Servidor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Is the keyword searched for in Google? If there is no search volume,\n"
"                                    then that tells you no one is typing "
"that phrase into Google. There is no\n"
"                                    point in advertising on keywords no one "
"is searching for."
msgstr ""
"Es la palabra clave buscada en Google? Si no hay volumen de búsqueda, "
"entonces eso indica que nadie está buscando esa frase en Google. No tiene "
"sentido hacer publicidad en palabras clave que nadie busca."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Is the person searching with this keyword likely to buy my product or\n"
"                                    service? Or is the person more likely to "
"be just carrying out research\n"
"                                    with no intention of making a purchase? "
"In other words, what is the\n"
"                                    intent of the keyword? When starting "
"out, you’ll want to advertise on\n"
"                                    what we call “buying intent” keywords - "
"that is, ones where the person\n"
"                                    is clearly looking to buy."
msgstr ""
"¿La persona que busca con esta palabra clave es propensa a comprar mi "
"producto \n"
"o servicio? O es más probable que sea simplemente investigando sin la "
"intención de \n"
"hacer una compra? En otras palabras, ¿cuál es la intención de la palabra "
"clave? Al comenzar, usted \n"
"querrá hacer publicidad en lo que llamamos \"intención de compra\" - es "
"decir, aquellos en los que la \n"
"persona está buscando claridad para comprar."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:19
#, fuzzy, python-format
msgid "It might be possible to edit the relevant items or fix the issue in"
msgstr ""
"Podría ser posible editar los elementos relevantes o arreglar el problema en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"It's difficult to change your theme after creating your website, so take the "
"time to look at all the themes, and be sure that you're happy with your "
"choice!"
msgstr ""
"Es difícil cambiar su tema después de crear su sitio web, así que tome un "
"tiempo para mirar todos los temas disponibles y tener la seguridad que está "
"feliz con su elección!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"It's often much easier not to start with a blank page. Take a look at your "
"competitors\n"
"                        and similar companies around the world. They "
"probably have the same objectives and\n"
"                        visitors that you do. How did they transform that "
"into a website?<br/>\n"
"                        Try to find 3 websites that have remarkable "
"characteristics:"
msgstr ""
"Muchas veces no es facil empezar con una pagina en blanco. Echale un vistazo "
"a tus competidores \n"
"y empresas similares en todo el mundo. Es probable que tengan los mismos "
"objetivos y \n"
"visitantes que tú. ¿Cómo transformar eso en una página web?<br/>\n"
"Trate de encontrar 3 sitios web que tengan características notables:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid "Join us and make your company a better place."
msgstr "Únase a nosotros y hagade su compañía un lugar mejor."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Landscape"
msgstr "Paisaje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Language"
msgstr "Idioma"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:28
#, python-format
msgid "Language:"
msgstr "Idioma:"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_language_ids
#: model:ir.model.fields,field_description:website.field_website_language_ids
msgid "Languages"
msgstr "Idiomas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Large"
msgstr "Grande"

#. module: website
#: model:ir.model.fields,field_description:website.field_website___last_update
#: model:ir.model.fields,field_description:website.field_website_config_settings___last_update
#: model:ir.model.fields,field_description:website.field_website_menu___last_update
#: model:ir.model.fields,field_description:website.field_website_published_mixin___last_update
#: model:ir.model.fields,field_description:website.field_website_seo_metadata___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_write_uid
#: model:ir.model.fields,field_description:website.field_website_menu_write_uid
#: model:ir.model.fields,field_description:website.field_website_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_write_date
#: model:ir.model.fields,field_description:website.field_website_menu_write_date
#: model:ir.model.fields,field_description:website.field_website_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Launch"
msgstr "Lanzar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Launching your website is an important step.<br/>\n"
"                        Here is a checklist of actions to help you launch a "
"new website efficiently:"
msgstr ""
"El lanzamiento de su sitio web es un paso importante.<br/>\n"
"He aquí una lista de acciones para ayudar a lanzar un nuevo sitio web de "
"manera eficiente:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Learn what your visitors are thinking, what question they have, what "
"problems they encounter."
msgstr ""
"Aprende de lo que sus visitantes están pensando, ¿qué preguntas  tienen, "
"cuáles son los problemas que encuentran."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Izquierda"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:90
#, python-format
msgid "Let's check how your homepage looks like on mobile devices."
msgstr "Veamos cómo se ve su página de inicio en dispositivos móviles."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Personalización limitada"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_linkedin
#: model:ir.model.fields,field_description:website.field_website_social_linkedin
msgid "LinkedIn Account"
msgstr "Cuenta LinkedIn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "List of Features"
msgstr "Lista de Características"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:83
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Logo"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Logout"
msgstr "Cerrar Sesión"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_id
msgid "Main Menu"
msgstr "Menú Principal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mango"
msgstr "Mango"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Margin"
msgstr "Margen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Masonry"
msgstr "Mampostería"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
msgid "Maybe you were looking for one of these popular pages ?"
msgstr "¿Tal vez buscaba una de estas página populares ?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Media"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Meet your existing customers and encourage them to 'repeat buy'."
msgstr ""
"Satisfacer a sus clientes actuales e incitar a \"la repetición de compra '."

#. module: website
#: code:addons/website/models/website.py:320
#: model:ir.model.fields,field_description:website.field_website_menu_name
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#, python-format
msgid "Menu"
msgstr "Menú"

#. module: website
#: code:addons/website/models/website.py:324
#, python-format
msgid "Menu <b>%s</b> seems to have a link to this page !"
msgstr "El menú <b>%s</b> parece tener un enlace a esta página!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Message"
msgstr "Mensaje"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.mobile.js:42
#, python-format
msgid "Mobile preview"
msgstr "Previsualización móvil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mode"
msgstr "Modo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "More info on pricing:"
msgstr "Más información de precios:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "More than 500 happy customers."
msgstr "Más de 500 clientes satisfechos."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:41
#, python-format
msgid "Most searched topics related to your keywords, ordered by importance:"
msgstr ""
"Temas más buscados relacionados con sus palabras clave, ordenadas por "
"importancia:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mountains"
msgstr "￼Montañas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr "Mover al inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr "Movel al final"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr "Mover al siguiente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr "Mover al anterior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "My Website"
msgstr "Mi Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Estrecho"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:125
#, python-format
msgid "New Name"
msgstr "Nuevo Nombre"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:60
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "New Page"
msgstr "Nueva Página"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_new_window
msgid "New Window"
msgstr "Nueva ventana"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:123
#, python-format
msgid "New name"
msgstr "Nuevo nombre"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.editor.js:118
#, python-format
msgid "New or existing page"
msgstr "Página nueva o existente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
#: model_terms:ir.ui.view,arch_db:website.pager
msgid "Next"
msgstr "Siguiente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Sin personalización"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Sin soporte"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "None"
msgstr "Ninguno"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.backend.js:22
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Not Published"
msgstr "No Publicado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr "Nota: Para ocultar esta página, desmáquela desde Personalizar menú."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Note: To use those features, you need to install the"
msgstr "Nota: Para usar esas características, necesita instalar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Odoo"
msgstr "Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 1 for three columns"
msgstr "Odoo - Ejemplo 1 para tres columnas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 2 for three columns"
msgstr "Odoo - Ejemplo 2 para tres columnas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 3 for three columns"
msgstr "Odoo - Ejemplo 3 para tres columnas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "Odoo CMS - a big picture"
msgstr "CMS Odoo - Una gran imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_floating
msgid "Odoo CMS- Sample image floating"
msgstr "CMS Odoo - Ejemplo de imagen flotante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Versión de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Odoo image and text block"
msgstr "Bloque de imagen y texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid ""
"Odoo provides essential platform for our project management.\n"
"                                                Things are better organized "
"and more visible with it."
msgstr ""
"Odoo provee una plataforma esencial para nuestra gestión de proyectos.\n"
"Las cosas están mejor organizadas y más visibles con Odoo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
msgid ""
"Odoo provides essential platform for our project management.\n"
"                                        Things are better organized and more "
"visible with it."
msgstr ""
"Odoo provee una plataforma esencial para nuestra gestión de proyectos.\n"
"Las cosas están mejor organizadas y más visibles con Odoo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid ""
"Odoo provides essential platform for our project management.\n"
"                            Things are better organized and more visible "
"with it."
msgstr ""
"Odoo provee una plataforma esencial para nuestra gestión de proyectos.\n"
"Las cosas están mejor organizadas y más visibles con Odoo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Odoo text and image block"
msgstr "Bloque de texto e imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Odoo's Google Analytics settings"
msgstr "Ajustes de Google Analytics en Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Odoo's POS is a web application that can run on any device that\n"
"                                can display websites with little to no setup "
"required."
msgstr ""
"Odoo POS es una aplicación web que puede funcionar con cualquier "
"dispositivo \n"
"que pueda mostrar sitios web con poca o ninguna configuración requerida."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Off"
msgstr "Apagado"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:46
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:5
#, python-format
msgid "On Website"
msgstr "En el Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Once you’ve launched your website, it’s time to grow your traffic."
msgstr ""
"Una vez haya lanzado su sitio web, es tiempo de incrementar su tráfico."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "ERP de Código Abierto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Open Source eCommerce"
msgstr "Tienda Virtual de Código Abierto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Optimization"
msgstr "Mejoramiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize AdWords Campaign"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize SEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize your AdWords account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Orange Red"
msgstr "￼Rojo anaranjado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Pedir ahora!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Organize your job board and promote your job announces easily."
msgstr ""
"Organice su bolsa de trabajo y promueva sus anuncios de empleo fácilmente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Other Info"
msgstr "Otra información"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Our Offers"
msgstr "Nuestras Ofertas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
#, fuzzy
msgid "Our Products &amp; Services"
msgstr "Nuestros productos y Servicios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "Nuestras Referencias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid "Our Team"
msgstr "Nuestro Equipo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid ""
"Our products are designed for small to medium size companies willing to "
"optimize\n"
"                                      their performance."
msgstr ""
"Nuestros productos están diseñados para empresas pequeñas y medianas que "
"quieren optimizar su rendimiento."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid ""
"Our products are designed for small to medium size companies willing to "
"optimize\n"
"                            their performance."
msgstr ""
"Nuestros productos están diseñados para empresas pequeñas y medianas que "
"quieren optimizar su rendimiento."

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:298
#: code:addons/website/static/src/xml/website.editor.xml:11
#, python-format
msgid "Page"
msgstr "Página"

#. module: website
#: code:addons/website/models/website.py:303
#, python-format
msgid "Page <b>%s</b> seems to have a link to this page !"
msgstr "Página <b>%s</b> parece tener un enlace a esta página !"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:61
#, python-format
msgid "Page Title"
msgstr "Título de la Página"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:103
#, fuzzy, python-format
msgid "Pages and menus"
msgstr "Añadir nuevas páginas y menús"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_panel
msgid ""
"Panels are a great tool to compare offers or to emphasize on\n"
"                key features. To compare products, use the inside columns."
msgstr ""
"Los paneles son una gran herramienta para comparar ofertas o enfatizar en "
"características clave. Para comparar productos, utilice las columnas de "
"dentro."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_left
msgid "Parent Left"
msgstr "Padre izquierdo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_id
msgid "Parent Menu"
msgstr "Menú padre"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_right
msgid "Parent Right"
msgstr "Padre derecho"

#. module: website
#: model:ir.model,name:website.model_res_partner
msgid "Partner"
msgstr "Empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_post
msgid "Partner Detail"
msgstr "Detalles del Contacto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Partners"
msgstr "Empresas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Plan"
msgstr "Plan"

#. module: website
#: model:web.planner,tooltip_planner:website.planner_website
msgid ""
"Plan your website strategy, design your pages, sell your products and grow "
"your online business!"
msgstr ""
"Planée su estrategia web, diseñe sus páginas, venda sus productos y haga "
"crecer su negocio en línea!"

#. module: website
#: model:ir.model,name:website.model_web_planner
msgid "Planner"
msgstr "Planificador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Please install a theme in order to customize your website."
msgstr "Por favor instale un tema para poder personalizar su sitio web."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.theme.js:277
#, python-format
msgid "Please install or update node-less"
msgstr "Por favor, instalar o actualizar el nodo menor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Please note that you'll need an Odoo Standard or Business subscriptions for "
"that"
msgstr "Por favor, debe suscribirse a Odoo Standar o Business para esto "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Point of Sale Questions <small>v7</small>"
msgstr "Preguntas del Punto de Venta <small>v7</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Powered by"
msgstr "Desarrollado por"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
#: model_terms:ir.ui.view,arch_db:website.pager
msgid "Prev"
msgstr "Anterior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Product pricing and promotional offers,"
msgstr "Precios de los productos y promociones,"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Profesional"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:5
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, python-format
msgid "Promote"
msgstr "Promover"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:360
#, python-format
msgid "Promote This Page"
msgstr "Promover Esta Página"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:5
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Promote page on the web"
msgstr "Promover página en la web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Promote your catalog of services"
msgstr "Promover su catálogo de servicios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Provide fast, professional and accurate information to your visitors and "
"customers."
msgstr ""
"Provea información rápida, profesional y precisa a sus visitantes y clientes."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_partner_id
msgid "Public Partner"
msgstr "Contacto Público"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_user_id
msgid "Public User"
msgstr "Usuario Público"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:76
#, python-format
msgid "Publish your page by clicking on the <em>'Save'</em> button."
msgstr "Publique su página haciendo clic en el botón <em>'Guardar'</em>."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.backend.js:18
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Published"
msgstr "Publicado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Purple"
msgstr "Morado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "QWeb"
msgstr "QWeb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Quote"
msgstr "Cita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Re-order"
msgstr "Re-organizar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Ready For Launch!"
msgstr "Listo Para Lanzar!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Real professional translators will translate all your contents"
msgstr "Todos sus contenidos se traducirán con traductores profesionales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Recipient"
msgstr "Destinatario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Reduce the time and resources spent on support."
msgstr "Reduce el tiempo y recursos gastados en soporte."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:96
#, python-format
msgid "Reference(s) found:"
msgstr "Referencia(s) encontradas:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "Eliminar Diapositiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove all images"
msgstr "Remover todas las imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove from gallery"
msgstr "Eliminar de galeria"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:95
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Rename Page"
msgstr "Renombrar Página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset selected templates"
msgstr "Restablecer las plantillas seleccionadas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset templates"
msgstr "Restablecer las plantillas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Derecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded corners"
msgstr "Esquinas redondeadas"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "Metadatos SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Sample images"
msgstr "Imágenes de ejemplo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:363
#: code:addons/website/static/src/xml/website.ace.xml:12
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:75
#, python-format
msgid "Save your modifications"
msgstr "Guardar sus cambios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Speed"
msgstr "Velocidad de Desplazamiento"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:97
#, python-format
msgid "Scroll to check rendering and then close the mobile preview."
msgstr ""
"Desplace para comprobar el dibujado y cierre entonces la previsualización "
"móvil."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Buscar Menús"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Buscar..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Segunda Característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Second List"
msgstr "Segunda Lista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "See and buy your products"
msgstr "Vea y compre sus productos"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:27
#, python-format
msgid "Select a Menu"
msgstr "Seleccione un Menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Select and delete blocks to remove some features."
msgstr "Seleccione y elimine bloques para eliminar algunas características."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:53
#, python-format
msgid "Select the parent container to get the global options of the banner."
msgstr ""
"Seleccione el contenedor padre para obtener las opciones globales del banner."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Select the untranslated language"
msgstr "Seleccione el idioma sin traducir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_message
msgid "Sell Online. Easily."
msgstr "Venda en línea. Fácilmente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Sell more online"
msgstr "Venda más en línea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Send <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Enviar <span class=\"fa fa-long-arrow-right\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Send a Message to our Partners"
msgstr "Enviar un Mensaje a nuestros Contactos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Send an email to all your contacts"
msgstr "Enviar un correo a todo sus contactos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "Send us an email"
msgstr "Envíenos un correo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Settings &gt; Website"
msgstr "Ajustes &gt; Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadows"
msgstr "Sombras"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "Share"
msgstr "Compartir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Share experience on similar projects"
msgstr "Comparta experiencia en proyectos similares"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_customize_show
msgid "Show As Optional Inherit"
msgstr "Mostrar como herencia opcional"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Simple and obvious"
msgstr "Simple y obvio"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:21
#, python-format
msgid "Skip It"
msgstr "Omitirlo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow"
msgstr "Presentación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow speed"
msgstr "Velocidad de la presentación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slow"
msgstr "Lenta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small"
msgstr "Pequeño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Social Media"
msgstr "Medios Sociales"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:70
#, python-format
msgid "Some dependencies can exist ..."
msgstr "￼Algunas dependencias pueden existir..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Special effects and animations"
msgstr "Efectos especiales y animaciones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr "Cuadrada"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:21
#, python-format
msgid "Start Tutorial"
msgstr "Comenzar Tutorial"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Start by looking how often people search phrases related to your business,\n"
"                                    how competitive the keywords are in "
"AdWords, and how much it’ll cost to\n"
"                                    advertise on each keyword. All of this "
"information will  help you determine\n"
"                                    which keywords you want to use in your "
"first campaign."
msgstr ""
"Comience buscando con qué frecuencia la gente busca frases relacionadas con "
"su negocio, lo competitivo que las palabras clave están en las palabras del "
"anuncio, y cuánto le va a costar hacer publicidad en cada palabra clave. "
"Toda esta información le ayudará a determinar las palabras clave que desea "
"utilizar en su primera campaña."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Start with the customer – find out what they want\n"
"                        and give it to them."
msgstr ""
"Comience con el cliente - averigüe lo que quiere\n"
"y véndalo. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Starter package"
msgstr "Paquete de inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Static"
msgstr "Estático"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Still in the left-side menu, go to <strong>APIs and auth &gt; Credentials</"
"strong> and click on <strong>'Create New Client ID'</strong>"
msgstr ""
"Al lado izquiero del menú, ir a <strong>APIs y autenticación &gt; "
"Credenciales</strong> y haga clic en <strong>'Crear Nuevo ID de Cliente'</"
"strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "Estilo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Styling"
msgstr "Estilizando"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle"
msgstr "Subtítulo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle 2"
msgstr "Subtítulo 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle 3"
msgstr "Subtítulo 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sunflower"
msgstr "Girasol"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Nombre técnico:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"Tell features the visitor would like to know, not what you'd like to say."
msgstr ""
"￼Cuente características que el visitante quiera conocer, no lo que le "
"gustaría decir."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the<br/>customer for this feature."
msgstr "Indicar cual es el valor del<br/>cliente para esta función"

#. module: website
#: code:addons/website/models/website.py:308
#, python-format
msgid "Template <b>%s (id:%s)</b> seems to have a link to this page !"
msgstr "Plantilla <b>%s (id:%s)</b> parece tener un enlace a esta página !"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.ace.js:246
#, python-format
msgid "Template ID: %s"
msgstr "ID Plantilla: %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Template fallback"
msgstr "Alternativa a la plantilla"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:89
#, python-format
msgid "Test Your Mobile Version"
msgstr "Probar Su Versión Móvil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_post
msgid "Thank you for posting a message !"
msgstr "Gracias por publicar un mensaje!"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:104
#, fuzzy, python-format
msgid ""
"The 'Content' menu allows you to rename and delete pages or add them to the "
"top menu."
msgstr "El menú 'Contenido' le permite añadir páginas o el menú superior."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The Banner or Big Picture building blocks are good choices for that."
msgstr ""
"Un Banner o un bloque con una imagen grande son buenas opciones para ello."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"The Point of Sale works perfectly on any kind of touch enabled\n"
"                                device, whether it's multi-touch tablets "
"like an iPad or\n"
"                                keyboardless resistive touchscreen terminals."
msgstr ""
"El Punto de Venta trabaja perfectamente en cualquier tipo de\n"
"dispositivo touch, sea una tableta multi-touch como un iPad o\n"
"terminales de pantalla táctil resistente sin teclado."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The best and simplest way to start is to create a few pages and link them "
"directly from the main menu.\n"
"                            Just try not to go over 6 items, otherwise your "
"main menu will be difficult to use."
msgstr ""
"La mejor forma y más simple para empezar es la creación de unas pocas "
"páginas y vincularlas directamente desde el menú principal.\n"
"Sólo trate de no realizar más de 6 elementos, de lo contrario su menú "
"principal será difícil de usar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The building blocks"
msgstr "Los bloques de construcción"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The call to action’s (i.e. the button’s) wording, size, color and placement,"
msgstr ""
"Para llamar acciones (ejemplo: los botones)  de frases, tamaño, color y "
"ubicación,"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The colors palettes"
msgstr "Las paletas de colores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The cost is from $0.05 to $0.15 per word, depending on the translator's "
"level of expertise"
msgstr ""
"El costo va desde $150 a $400 pesos por palabra, dependiendo de la "
"experiencia del traductor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The error occured while rendering the template"
msgstr "Error ocurrido durante el dibujado de la plantilla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The following error was raised in the website controller"
msgstr "Se lanzó el siguiente error en el controlador del sitio web"

#. module: website
#: model:ir.model.fields,help:website.field_blog_post_website_url
#: model:ir.model.fields,help:website.field_delivery_carrier_website_url
#: model:ir.model.fields,help:website.field_event_event_website_url
#: model:ir.model.fields,help:website.field_event_track_website_url
#: model:ir.model.fields,help:website.field_hr_employee_website_url
#: model:ir.model.fields,help:website.field_hr_job_website_url
#: model:ir.model.fields,help:website.field_im_livechat_channel_website_url
#: model:ir.model.fields,help:website.field_payment_acquirer_website_url
#: model:ir.model.fields,help:website.field_product_template_website_url
#: model:ir.model.fields,help:website.field_project_project_website_url
#: model:ir.model.fields,help:website.field_res_partner_grade_website_url
#: model:ir.model.fields,help:website.field_res_partner_tag_website_url
#: model:ir.model.fields,help:website.field_res_partner_website_url
#: model:ir.model.fields,help:website.field_slide_channel_website_url
#: model:ir.model.fields,help:website.field_slide_slide_website_url
#: model:ir.model.fields,help:website.field_website_published_mixin_website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website
#: model:ir.model.fields,help:website.field_ir_act_server_website_url
msgid "The full URL to access the server action through the website."
msgstr ""
"La URL completa para acceder a la acción de servidor a través del sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "The page you were looking for could not be authorized."
msgstr "No está autorizado a ver la página que está buscando."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid ""
"The page you were looking for could not be found; it is possible you have\n"
"                        typed the address incorrectly, but it has most "
"probably been removed due\n"
"                        to the recent website reorganisation."
msgstr ""
"No se puede encontrar la página que está buscando; es posible que haya \n"
"Ingresado la dirección de manera incorrecta, pero probablemente puede haber "
"sido \n"
"eliminada debido a la reciente reorganización del sitio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The pre-loaded images"
msgstr "Las imágenes pre-cargadas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "The selected templates will be reset to their factory settings."
msgstr ""
"Las plantillas seleccionadas serán restablecidas a su configuración "
"predeterminada."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The shorter the better"
msgstr "Entre más corto mejor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The theme you choose sets the tone for the overall look and feel of your "
"site design."
msgstr ""
"El tema que escogió fija el tono para la apariencia y sensación general del "
"diseño de su sitio."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:18
#, python-format
msgid "The web site has encountered an error."
msgstr "El sitio web ha encontrado un error."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.theme.js:280
#, python-format
msgid "Theme Error"
msgstr "Error del Tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Theme Selection"
msgstr "Selección de Temas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Then simply"
msgstr "Luego simplemente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Then, copy the <strong>Client ID</strong> and <strong>Client Secret</strong> "
"codes and paste them in"
msgstr ""
"A continuación, vaya a <strong>ID de Cliente</strong> y <strong> Cliente "
"Secreto</strong> copiar los códigos y pegarlos en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Then, go to"
msgstr "Entonces, vaya a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"There are three questions you should ask before deciding whether or not to "
"advertise on a particular keyword:"
msgstr ""
"Hay tres preguntas que debería hacerse antes de decidir si publicita en una "
"palabra clave particular o no:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"They allow a detailed qualification of the visitor, which is perfect to link "
"them later to marketing campaigns."
msgstr ""
"Permiten una calificación detallada del visitante, lo cual es perfecto para "
"vincularlos más adelante en campañas de marketing."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Tercera Característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exists, but you can create it as you are administrator of "
"this site."
msgstr ""
"Esta página no existe, pero puede crearla, ya que es administrador de este "
"sitio."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:20
#, python-format
msgid ""
"This tutorial will guide you to build your home page. We will start by "
"adding a banner."
msgstr ""
"Este tutorial le guiará para construir su página de inicio. Empezaremos "
"añadiendo un banner."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Miniaturas"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:48
#, python-format
msgid "Title"
msgstr "Título"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these\n"
"                        three columns using the right icon of each block.\n"
"                        Then, duplicate one of the column to create a new\n"
"                        one as a copy."
msgstr ""
"Para añadir una cuarta columna, reduzca el tamaño de esas tres columnas "
"usando el icono derecho de cada bloque. Luego, duplique una de las columnas "
"para crear una nueva como una copia."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "To do that, simply use the Promote menu on each page of your website."
msgstr ""
"Para hacer eso, simplemente use el menú de Promover en cada página de su "
"sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"To get an external assessment of your website, you can also submit it to"
msgstr ""
"Para obtener una evaluación externa de su sitio web, también puede enviarlo a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "To learn more, take a look at their"
msgstr "Para obtener más información, eche un vistazo a su"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:175
#: model:website.menu,name:website.main_menu
#: model:website.menu,name:website.website2_main_menu
#, python-format
msgid "Top Menu"
msgstr "Menú Superior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Traceback"
msgstr "Traza"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.snippets.editor.js:771
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Translate"
msgstr "Traducir"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:14
#, python-format
msgid "Translate this page"
msgstr "Traducir esta página"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:27
#, python-format
msgid "Translated content"
msgstr "Contenido traducido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Translated versions are updated automatically after 32 hours on average."
msgstr ""
"Las versiones traducidas se actualizan automáticamente después de 32 horas "
"en promedio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Translating your website into other languages is the best way to broaden its "
"audience.\n"
"                        In Odoo, you can either manually translate your "
"pages as they are displayed, use an\n"
"                        automatic machine translation service for free or "
"pay professional translators to do\n"
"                        it for you."
msgstr ""
"La traducción de su sitio web a otros idiomas es la mejor manera de ampliar "
"su audiencia. En Odoo, usted puede traducir manualmente sus páginas a medida "
"que se muestran utilizando un servicio de traducción automática de forma "
"gratuita, o pagando un traductor profesional que lo haga por usted."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_twitter
#: model:ir.model.fields,field_description:website.field_website_social_twitter
msgid "Twitter Account"
msgstr "Cuenta Twitter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"Escriba '<i class=\"confirm_word\">si</i>' a continuación si quiere "
"confirmar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "UA-XXXXXXXX-Y"
msgstr "UA-XXXXXXXX-Y"

#. module: website
#: model:ir.model.fields,help:website.field_website_cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""
"URL que coincida con esos filtros se escribirá a través de la URL CDN Base"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Ultimate package"
msgstr "Ultimo Extremo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "Poder y soporte ilimitado de CRM"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Personalización ilimitada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Update your internal documents: footer of sales order, contracts,invoices, "
"business cards, etc."
msgstr ""
"Actualización de sus documentos internos: pie de página de pedido de ventas, "
"contratos, facturas, tarjetas de visita, etc."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:32
#, python-format
msgid ""
"Upload failed, some images might not have been uploaded. Check your network "
"connectivity."
msgstr ""
"Carga fallida, algunas imágenes podrían no haber sido subidas. Verifique su "
"conexión de red."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:31
#, python-format
msgid "Upload successful."
msgstr "Subida exitosa."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_url
msgid "Url"
msgstr "Url"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, fuzzy
msgid "Use Google Adwords"
msgstr "Usar Google Adword"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_cdn_activated
msgid "Use a Content Delivery Network (CDN)"
msgstr "Utilice una red de entrega de contenidos (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Use a free, automatic machine translation (quality will vary depending on "
"languages)"
msgstr ""
"Usar una máquina de traducción gratuita y automática (la calidad puede "
"variar dependiendo del idioma)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Use the <i>Customize</i> menu to change the look of your theme:"
msgstr ""
"Use el menú de <i>Personalizar</i> para cambiar la apariencia de su tema:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Use the Newsletter subscription (from mass mailing application) or the Big "
"button building block for that."
msgstr ""
"Utilice la suscripción Newsletter (desde la aplicación de correo masivo) o "
"el Gran botón construido para eso."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:112
#, python-format
msgid "Use this button to add pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid ""
"User-agent: *\n"
"Sitemap:"
msgstr ""
"Agente-usuario: *\n"
"Mapa del sitio:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Velour"
msgstr "Terciopelo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Fast"
msgstr "Muy rápido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Slow"
msgstr "Muy Lento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "View our themes selection"
msgstr "Vea nuestra selección de temas"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_post_website_published
#: model:ir.model.fields,field_description:website.field_delivery_carrier_website_published
#: model:ir.model.fields,field_description:website.field_event_track_website_published
#: model:ir.model.fields,field_description:website.field_hr_employee_website_published
#: model:ir.model.fields,field_description:website.field_hr_job_website_published
#: model:ir.model.fields,field_description:website.field_im_livechat_channel_website_published
#: model:ir.model.fields,field_description:website.field_product_template_website_published
#: model:ir.model.fields,field_description:website.field_project_project_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_grade_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_tag_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_website_published
#: model:ir.model.fields,field_description:website.field_slide_channel_website_published
#: model:ir.model.fields,field_description:website.field_slide_slide_website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin_website_published
msgid "Visible in Website"
msgstr "Visible en el Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Visitors might not be interested in your products or services when they come "
"to your site. They could want to learn something, improve their life, grow "
"their business, find out more about you, etc. What great content can you "
"offer your visitors? Why should they stay on your website?"
msgstr ""
"Los visitantes pueden no estar interesados en sus productos o servicios "
"cuando llegan a su sitio. Ellos podrían querer aprender algo, mejorar su "
"vida, crecer sus negocios, obtener más información acerca de usted, etc. "
"¿Qué gran contenido puede ofrecer a sus visitantes? ¿Por qué deberían "
"permanecer en su sitio web?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                      life through disruptive products. We "
"build great products to solve your\n"
"                                      business problems."
msgstr ""
"Somos un equipo de personas apasionadas cuya meta es mejorar la vida de "
"todos a través de productos disruptivos. Nosotros construimos productos "
"geniales para resolver sus problemas empresariales."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                            life through disruptive products. We build great "
"products to solve your\n"
"                            business problems."
msgstr ""
"Somos un equipo de personas apasionadas cuya meta es mejorar la vida de "
"todos a través de productos disruptivos. Nosotros construimos productos "
"geniales para resolver sus problemas empresariales."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"We can create a customized theme for you with your company colors, logo and "
"images from your library."
msgstr ""
"Nosotros podemos crear un tema personalizado para usted con los colores de "
"su compañía, logo e imágenes desde su librería."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "We hope this planner helped you to create your website."
msgstr "Esperamos que este planificador le haya ayudado a crear su sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "We'll do our best to get back to you as soon as possible."
msgstr "Haremos lo posible por responderle a la mayor brevedad."

#. module: website
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_id
#: model:ir.model.fields,field_description:website.field_website_menu_website_id
#: model:ir.ui.menu,name:website.menu_website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
msgid "Website"
msgstr "Sitio web"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_configuration
msgid "Website Admin"
msgstr "Admin Sitio"

#. module: website
#: model:ir.actions.act_window,name:website.action_module_website
msgid "Website Apps"
msgstr "Aplicaciones del Sitio Web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_domain
msgid "Website Domain"
msgstr "Dominio del Sitio Web"

#. module: website
#: model:ir.actions.act_url,name:website.action_website_homepage
msgid "Website Homepage"
msgstr "Página de Inicio del Sitio Web"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Menú del Sitio Web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_website_name
#: model:ir.model.fields,field_description:website.field_website_name
msgid "Website Name"
msgstr "Nombre del Sitio Web"

#. module: website
#: model:ir.actions.server,name:website.action_partner_post
msgid "Website Partner Post and Thanks Demo"
msgstr "Demo de entrada de un cliente del sitio web y agradecimiento"

#. module: website
#: model:ir.actions.server,name:website.action_partner_comment
msgid "Website Partners Comment Form"
msgstr "Formulario de Comentarios del Cliente del Sitio Web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_path
msgid "Website Path"
msgstr "Ruta del Sitio Web"

#. module: website
#: model:crm.team,name:website.salesteam_website_sales
msgid "Website Sales"
msgstr "Ventas del Sitio Web"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr "Configuración del Sitio Web"

#. module: website
#: model:ir.actions.act_window,name:website.action_module_theme
msgid "Website Theme"
msgstr "Tema del Sitio Web"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_post_website_url
#: model:ir.model.fields,field_description:website.field_delivery_carrier_website_url
#: model:ir.model.fields,field_description:website.field_event_event_website_url
#: model:ir.model.fields,field_description:website.field_event_track_website_url
#: model:ir.model.fields,field_description:website.field_hr_employee_website_url
#: model:ir.model.fields,field_description:website.field_hr_job_website_url
#: model:ir.model.fields,field_description:website.field_im_livechat_channel_website_url
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_url
#: model:ir.model.fields,field_description:website.field_payment_acquirer_website_url
#: model:ir.model.fields,field_description:website.field_product_template_website_url
#: model:ir.model.fields,field_description:website.field_project_project_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_grade_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_tag_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_website_url
#: model:ir.model.fields,field_description:website.field_slide_channel_website_url
#: model:ir.model.fields,field_description:website.field_slide_slide_website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin_website_url
msgid "Website URL"
msgstr "URL del Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Website Versioning."
msgstr "Versionado del Sitio Web."

#. module: website
#: model:ir.actions.act_url,name:website.action_website_tutorial
msgid "Website With Tutorial"
msgstr "Sitio Web con Tutorial"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Menú del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_description
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_description
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_description
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_description
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_description
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_description
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_description
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_description
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_description
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_description
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_description
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_description
msgid "Website meta description"
msgstr "Meta descripción del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta palabras clave del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_title
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_title
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_title
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_title
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_title
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_title
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_title
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_title
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_title
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_title
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_title
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_title
msgid "Website meta title"
msgstr "Meta título del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Sitios Web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install_website_ids
msgid "Websites to translate"
msgstr "Sitios web a traducir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Welcome"
msgstr "Bienvenido/a"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:19
#, python-format
msgid "Welcome to your website!"
msgstr "¡Bienvenido a su sitio web!"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:82
#, python-format
msgid "Well done, you created your homepage."
msgstr "Bien hecho, usted creó su página de inicio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "What may <strong>interest your visitors?</strong>"
msgstr "¿Que puede <strong>interesarle a sus visitantes?</strong>"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_page
msgid "Whether this view is a web page template (complete)"
msgstr "Si esta plantilla es una plantilla de página web (completa)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Which hardware does Odoo POS support?"
msgstr "Qué hardware soporta el PdV de Odoo?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"While an internet connection is required to start the Point of\n"
"                                Sale, it will stay operational even after a "
"complete disconnection."
msgstr ""
"Mientras que una conexión de internet es necesaria para\n"
"comenzar el Punto de Venta, este permanecerá operacional\n"
"aún después de una desconexión completa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"With Odoo, you can automatically check which keywords are ranked best for a\n"
"                                    specific query, then add them in the "
"content of your page."
msgstr ""
"Con Odoo, usted puede comprobar automáticamente que palabras clave clasifica "
"mejor para una \n"
"consulta específica, adicionelas en el contenido de su página."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Wood"
msgstr "Madera"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                                                great way to build "
"confidence in your products or services."
msgstr ""
"Escriba aquí una cita de uno de sus clientes. Los testimonios son una buena "
"manera de construir confianza en sus productos o servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                                        great way to build confidence in "
"your products or services."
msgstr ""
"Escriba aquí una cita de uno de sus clientes. Los testimonios son una buena "
"manera de construir confianza en sus productos o servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                            great way to build confidence in your products "
"or services."
msgstr ""
"Escriba aquí una cita de uno de sus clientes. Los testimonios son una buena "
"manera de construir confianza en sus productos o servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quote
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"            great way to build confidence in your products or services."
msgstr ""
"Escriba aquí una cita de uno de sus clientes. Los testimonios son una buena "
"manera de construir confianza en sus productos o servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or\n"
"                        services. To be successful your content needs to be\n"
"                        useful to your readers."
msgstr ""
"Escribe uno o dos párrafos que describan sus productos o \n"
"servicios. Para tener éxito su contenido debe ser útil y llamativo para el "
"público."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid ""
"Write one or two paragraphs describing your product,\n"
"                        services or a specific feature. To be successful\n"
"                        your content needs to be useful to your readers."
msgstr ""
"Escribe uno o dos párrafos que describen su producto,\n"
"servicios o una característica específica. Para tener éxito\n"
"su contenido debe ser útil para el público."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_message
msgid "Write one sentence to convince visitor about your message."
msgstr "Escriba una frase para convencer al visitante sobre su mensaje."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know,<br/>not what you want to show."
msgstr ""
"Escribe lo que el cliente le gustaría saber,<br/> no lo que se quiere "
"mostrar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Yellow Green"
msgstr "Amarillo Verdoso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Yes"
msgstr "Sí"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:18
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Está a punto de entrar en el modo de traducción."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can also place online advertisements on web pages that show results from "
"search\n"
"                        engine queries: this is called SEA."
msgstr ""
"También puede colocar anuncios en línea en las páginas web que muestran los "
"resultados de búsqueda\n"
"del motor de consultas: esto se llama MAR."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can also use the <i>integrated HTML Editor(from the Customize menu)</i> "
"to modify the code directly."
msgstr ""
"También puede utilizar el <i>Editor HTML integrado (en el menú "
"Personalizar)</i> para modificar el código directamente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can do this by optimizing your referencing by search engines like "
"Google: it's\n"
"                        called SEO. This  is the process of increasing the "
"visibility of a website or a web\n"
"                        page in a search engine's natural and unpaid "
"(\"organic\") search results. In general,\n"
"                        the earlier (or higher ranked on the search results "
"page), and more frequently a site\n"
"                        appears in the search results list, the more "
"visitors it will receive from the search\n"
"                        engine."
msgstr ""
"Usted puede hacer esto optimizando su referenciamiento por los motores de "
"búsqueda como Google: se llama SEO. Este es el proceso de aumentar la "
"visibilidad de un sitio web o una página web en resultados de búsqueda "
"naturales y no pagados (\"orgánicos\"). En general, cuanto antes (o más alto "
"en la página de resultados de búsqueda), y cuanto más frecuentemente "
"aparezca un sitio en la lista de resultados, más visitantes recibirá del "
"motor de búsqueda."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "You can retrieve a 75$ coupon code to start your campaign here:"
msgstr "Puede obtener un código de cupón de 75$ para comenzar su campaña aquí:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can set Odoo on a custom domain name (e.g. yourcompany.com) for both the "
"website\n"
"                        and your emails. Because your website address is as "
"important to your branding as the\n"
"                        name of your business or organization, you’ll want "
"to put some thought into changing it\n"
"                        for a proper domain, or change it to one you already "
"own."
msgstr ""
"Puede establecer Odoo en un nombre de dominio personalizado (por ejemplo, "
"sucompañia.com) tanto para el sitio web \n"
"y sus correos electrónicos. Debido a que su dirección del sitio web es tan "
"importante para su marca como el \n"
"nombre de su empresa u organización, tendrá que poner algún pensamiento en "
"cambiándolo \n"
"para un dominio adecuado, o cambiar a uno que ya posee."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You have a lot of choices for that: the References, Quotes Slider, Twitter "
"Scroller,..."
msgstr ""
"Usted tiene muchas opciones para eso: los Testimonios, Slider de Citas, "
"Scroller de Twitter,..."

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_theme
#: model_terms:ir.actions.act_window,help:website.action_module_website
#, fuzzy
msgid "You should try other search criteria."
msgstr "Debería tratar con otros criterios de búsqueda."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You will get your leads filled up automatically in our integrated CRM "
"application."
msgstr ""
"Obtendrá sus clientes potenciales que se dieron de alta de forma automática "
"en nuestra aplicación CRM."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:48
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Your Banner Title"
msgstr "Título de su Banner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Domain Name"
msgstr "Su Nombre de Dominio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Footer"
msgstr "Su Pie de Página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Homepage"
msgstr "Su Página de Inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Main Menu"
msgstr "Su Menú Principal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Objectives"
msgstr "Sus Objetivos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "Your Website Title"
msgstr "El Título de su Sitio Web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your homepage is the most important page of your website. This is where "
"visitors get their first impressions of you. An excellent homepage will "
"encourage them to stay on your site, guide them towards your content, "
"reinforce your company's branding and more.<br/>\n"
"                        Here are some pointers to help you get started."
msgstr ""
"Su página principal es la página más importante de su sitio web. Aquí es "
"donde los visitantes tienen sus primeras impresiones de usted. Una excelente "
"página de inicio les animará a permanecer en su sitio, guiarlos hacia su "
"contenido, reforzará la marca de su empresa y muchas cosas más. <br/>\n"
"Éstos son algunos consejos para ayudarle a empezar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your theme selection will define:"
msgstr "Su selección de tema definirá:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your website is an important part of your online business strategy, but it "
"shouldn't be\n"
"                        the only one. There are so many ways to communicate "
"with your customers, visitors and\n"
"                        prospects that it's sometimes difficult to know what "
"to put your energy into. Here is\n"
"                        some advice on what to focus on next."
msgstr ""
"Su sitio web es una parte importante de su estrategia de negocio en línea, "
"pero no debe ser\n"
"el único. Hay tantas maneras de comunicarse con sus clientes, visitantes y "
"las perspectivas que a veces es difícil saber qué poner su energía. He "
"aquí \n"
"algunos consejos sobre lo que debe centrarse para continuar."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_youtube
#: model:ir.model.fields,field_description:website.field_website_social_youtube
msgid "Youtube Account"
msgstr "Cuenta Youtube"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"a reference to your new website. We will retweet it (we have 30,000 "
"followers) and feature it in our different communications."
msgstr ""
"una referencia a su nuevo sitio web. Lo vamos a retuitear (tenemos 30.000 "
"seguidores) y recomendarlo en nuestras diferentes medios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "and connect with the Google account you created"
msgstr "y conecte la cuenta Google que usted creó"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "and evaluating the following expression:"
msgstr "y evaluando la siguiente expresión:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "and we'll configure your website and/or email for you."
msgstr "y configuraremos su sitio web y/o correo electrónico para usted."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "button."
msgstr "botón."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "check out our blog application"
msgstr "échale un vistazo a nuestra aplicación de blog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "create a Google Analytics account"
msgstr "crear una cuenta Google Analytics"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:17
#, python-format
msgid "describing your page content"
msgstr "describiendo el contenido de su página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: About us"
msgstr "ej. Sobre Nosotros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: Blog, Success stories, References, Events, Jobs"
msgstr "ej: Blog, Historias de éxito, Referencias, Eventos, Trabajos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, fuzzy
msgid "ex: Contact us, Our Customers, Privacy Policy, Events, Blog, Jobs"
msgstr ""
"ej: Contáctenos, Clientes, Política de Privacidad, Eventos, Blog, Trabajos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: interesting contents, texts and articles"
msgstr "por ejemplo: contenidos interesante, textos y artículos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "for a free evaluation of the usability of your homepage."
msgstr "para una evaluación gratuito de la usabilidad de su página de inicio."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:62
#, python-format
msgid "how your page will be listed on Google"
msgstr "cómo será listada su página en Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "http://gengo.com/pricing-languages/"
msgstr "http://gengo.com/es/pricing-languages/"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "http://www.google.com/ads/adwords-coupon.html"
msgstr "http://www.google.com/ads/adwords-coupon.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "http://www.google.com/webmasters/tools/"
msgstr "http://www.google.com/webmasters/tools/"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://plus.google.com/+Odooapps"
msgstr "https://plus.google.com/+Odooapps"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://twitter.com/Odoo"
msgstr "https://twitter.com/Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "https://www.facebook.com/Odoo"
msgstr "https://www.facebook.com/Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.facebook.com/odoo"
msgstr "https://www.facebook.com/odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.linkedin.com/company/odoo"
msgstr "https://www.linkedin.com/company/odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.youtube.com/user/OpenERPonline"
msgstr "https://www.youtube.com/user/OpenERPonline"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://youraccount.github.io"
msgstr "https://usuario.github.io"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "if you don't have one yet."
msgstr "si no tiene una todavía."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "in Odoo"
msgstr "en Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "instancia de Odoo, el"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
msgid "ir.actions.server"
msgstr "ir.acciones.server"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "ir.attachment"
msgstr "ir.attachment"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "ir.qweb"
msgstr "ir.qweb"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "name"
msgstr "nombre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "online help"
msgstr "ayuda en línea"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:5
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "or Edit Master"
msgstr "o Editar el Maestro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "or see who currently owns it"
msgstr "o ver quien lo posee actualmente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "peek.usertesting.com"
msgstr "peek.usertesting.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "send us an email"
msgstr "envíenos un correo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid "sitemap.xml"
msgstr "mapadelsitio.xml"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:19
#, python-format
msgid "the classic Odoo interface"
msgstr "el interfaz clásico de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"to describe\n"
"                    <br/> your experience or to suggest improvements !"
msgstr ""
"para describir\n"
"<br/> su experiencia o sugerir mejoras !"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"to do that (make sure the Advanced Options are set to your country and\n"
"                                    language)."
msgstr ""
"para hacer eso (asegúrese que las Opciones Avanzadas tienen establecidos su "
"país \n"
"e idioma)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "to get started."
msgstr "para empezar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "url"
msgstr "url"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "user / month (billed annually)"
msgstr "usuario / mes (facturado anualmente)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:45
#, python-format
msgid "using above suggested keywords"
msgstr "usando las palabras clave sugeridas a continuación"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_website_id
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "website"
msgstr "sitio web"

#. module: website
#: model:ir.model,name:website.model_website_config_settings
msgid "website.config.settings"
msgstr "website.config.settings"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "website.published.mixin"
msgstr "website.published.mixin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "yes"
msgstr "Sí"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "your Company details form"
msgstr "formulario de detalles de su Compañía"

#~ msgid "&lt;!DOCTYPE html&gt;"
#~ msgstr "&lt;!DOCTYPE html&gt;"

#~ msgid ".xml"
#~ msgstr ".xml"

#~ msgid ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-align-justify fa-stack-1x\"/>\n"
#~ "        </span> Content"
#~ msgstr ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-align-justify fa-stack-1x\"/>\n"
#~ "        </span> Contenido"

#~ msgid ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-paper-plane fa-stack-1x\"/>\n"
#~ "        </span> Effect"
#~ msgstr ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-paper-plane fa-stack-1x\"/>\n"
#~ "        </span> Efecto"

#~ msgid ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-pie-chart fa-stack-1x\"/>\n"
#~ "        </span> Feature"
#~ msgstr ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-pie-chart fa-stack-1x\"/>\n"
#~ "        </span> Característica"

#~ msgid ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-th-large fa-stack-1x\"/>\n"
#~ "        </span> Structure"
#~ msgstr ""
#~ "<span class=\"fa-stack\">\n"
#~ "            <i class=\"fa fa-th-large fa-stack-1x\"/>\n"
#~ "        </span> Estructura"

#~ msgid "<span title=\"Edit Top Menu\">Edit Menu</span>"
#~ msgstr "<span title=\"Edit Top Menu\">Editar Menú</span>"

#~ msgid ""
#~ "<span title=\"Optimize your AdWords account\">Optimize AdWords Campaign</"
#~ "span>"
#~ msgstr ""
#~ "<span title=\"Optimice su cuenta AdWords\">Optimice su Campaña AdWords</"
#~ "span>"

#~ msgid "<span title=\"Promote page on the web\">Optimize SEO</span>"
#~ msgstr "<span title=\"Promover página en la web\">Optimizar SEO</span>"

#~ msgid "<strong>Click Here</strong>"
#~ msgstr "<strong>Clic Aquí</strong>"

#~ msgid "Check now and discover more today!"
#~ msgstr "Compruébelo ahora y descubra más hoy!"

#~ msgid "Color"
#~ msgstr "Color"

#~ msgid "Content <span class=\"caret\"/>"
#~ msgstr "Contenido <span class=\"caret\"/>"

#~ msgid "Customize <span class=\"caret\"/>"
#~ msgstr "Personalizar <span class=\"caret\"/>"

#~ msgid ""
#~ "Deploy new stores with just an internet connection: no\n"
#~ "                installation, no specific hardware required. It works "
#~ "with any\n"
#~ "                iPad, Tablet PC, laptop or industrial POS machine."
#~ msgstr ""
#~ "Implementar nuevas tiendas con sólo una conexión a Internet: sin\n"
#~ "instalación, no requiere hardware específico. Funciona con cualquier\n"
#~ "iPad, Tablet PC, portátil o máquina POS industrial."

#~ msgid "Discover more about Odoo"
#~ msgstr "Descubra más sobre Odoo"

#~ msgid "Edit Menu Entry"
#~ msgstr "Editar Entrada del Menú"

#~ msgid "Help <span class=\"caret\"/>"
#~ msgstr "Ayuda <span class=\"caret\"/>"

#~ msgid "Hide link"
#~ msgstr "Ocultar enlace"

#~ msgid ""
#~ "In case you still feel a bit puzzled about all\n"
#~ "                        of our yesterday's announcements, here is a "
#~ "little\n"
#~ "                        summary for you. <br/>\n"
#~ "                        We have decided to change the\n"
#~ "                        name because <b>\"OpenERP\"</b> didn't reflect "
#~ "the offering\n"
#~ "                        of the company anymore. With our newest apps, "
#~ "such\n"
#~ "                        as Website Builder, PoS or eCommerce, we have\n"
#~ "                        moved beyond the ERP territory. <br/>\n"
#~ "                        But <u>don't worry</u>, <b>Odoo</b> is and always "
#~ "will be\n"
#~ "                        fully open source. You can read more about the "
#~ "new name here.\n"
#~ "                        We have also prepared a short FAQ to explain all "
#~ "these changes\n"
#~ "                        to all of you. <br/>"
#~ msgstr ""
#~ "En caso de que todavía se sienta un poco confundido acerca \n"
#~ "de todos nuestros anuncios de ayer, aquí encuentra un pequeño \n"
#~ "resumen para usted. <br/>\n"
#~ "Hemos decidido cambiar el \n"
#~ "nombre <b>\"OpenERP\"</b> porque no refleja la oferta \n"
#~ "de la empresa. Con nuestras nuevas aplicaciones, como \n"
#~ "constructor de sitios, puntos de venta o comercio electrónico, \n"
#~ "hemos ido más allá del territorio de ERP. <br/>\n"
#~ "Pero <u>no se preocupe</u>, <b>Odoo</b> es y será siempre \n"
#~ "completamente código abierto. Puede leer más sobre el nuevo nombre aquí.\n"
#~ "También hemos preparado un breve FAQ para explicar todos estos \n"
#~ "cambios a todos ustedes.<br/>"

#~ msgid "Menu Label"
#~ msgstr "Etiqueta del Menú"

#~ msgid "Odoo Members"
#~ msgstr "Miembros Odoo"

#~ msgid ""
#~ "Odoo's POS is a web application that can run on any device that\n"
#~ "                can display websites with little to no setup required."
#~ msgstr ""
#~ "Odoo POS es una aplicación web que puede funcionar con cualquier "
#~ "dispositivo \n"
#~ "que pueda mostrar sitios web con poca o ninguna configuración requerida."

#~ msgid "Press The Top-Left Edit Button"
#~ msgstr "Presione el Boton Editar de la parte Superior Izquierda"

#~ msgid "Project Management Questions <small>v7</small>"
#~ msgstr "Preguntas Gestión de Proyectos <small>v7</small>"

#~ msgid "Promote <span class=\"caret\"/>"
#~ msgstr "Promover <span class=\"caret\"/>"

#~ msgid "Reset Transformation"
#~ msgstr "Restablecer Transformación"

#~ msgid ""
#~ "The Point of Sale works perfectly on any kind of touch enabled\n"
#~ "                device, whether it's multi-touch tablets like an iPad or\n"
#~ "                keyboardless resistive touchscreen terminals."
#~ msgstr ""
#~ "El Punto de Venta trabaja perfectamente en cualquier tipo de\n"
#~ "dispositivo touch, sea una tableta multi-touch como un iPad o\n"
#~ "terminales de pantalla táctil resistente sin teclado."

#~ msgid "Transform"
#~ msgstr "Transformación"

#~ msgid "What a day it was yesterday - such a big day for us!"
#~ msgstr "Qué día el que fue ayer - un gran día para nosotros!"

#~ msgid ""
#~ "While an internet connection is required to start the Point of\n"
#~ "                Sale, it will stay operational even after a complete "
#~ "disconnection."
#~ msgstr ""
#~ "Mientras que una conexión de internet es necesaria para\n"
#~ "comenzar el Punto de Venta, este permanecerá operacional\n"
#~ "aún después de una desconexión completa."

#~ msgid ""
#~ "With Odoo's fully integrated software, you can easily manage your\n"
#~ "                        meetings, schedule business calls, create "
#~ "recurring meetings,\n"
#~ "                        synchronize your agenda and easily keep in touch "
#~ "with your colleagues,\n"
#~ "                        partners and other people involved in projects or "
#~ "business discussions.\n"
#~ "                        <br/><br/>"
#~ msgstr ""
#~ "Con el software completamente integrado de Odoo, usted puede administrar "
#~ "fácilmente sus reuniones, agendar llamadas de negocios, crear reuniones "
#~ "recurrentes, sincronizar su agenda y mantener el contacto con sus "
#~ "colegas, contactos y otras personas involucradas en proyectos o charlas "
#~ "de negocios.\n"
#~ "<br/><br/>"

#~ msgid "Yes."
#~ msgstr "Sí."

#~ msgid "or"
#~ msgstr "o"

#~ msgid "sitemap-"
#~ msgstr "mapadelsitio-"
