<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="45.918%" y2="54.082%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <linearGradient id="linearGradient-2" x1="0%" x2="100%" y1="42.969%" y2="57.031%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-3" d="M12 9v2H3V9h9zm37 0v2h-9V9h9z"/>
    <filter id="filter-4" width="102.2%" height="200%" x="-1.1%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-5" d="M8 22v1H3v-1h5zm4-2v1H3v-1h9zm-3-3v1H3v-1h6zm2-3v1H3v-1h8z"/>
    <filter id="filter-6" width="111.1%" height="122.2%" x="-5.6%" y="-5.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-7" d="M27 20v1h-7v-1h7zm5-3v1H20v-1h12zm-4-3v1h-8v-1h8zm3-3v1H20v-1h11z"/>
    <filter id="filter-8" width="108.3%" height="120%" x="-4.2%" y="-5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-9" d="M49 22v1h-9v-1h9zm-5-2v1h-4v-1h4zm4-3v1h-8v-1h8zm-2-3v1h-6v-1h6z"/>
    <filter id="filter-10" width="111.1%" height="122.2%" x="-5.6%" y="-5.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-11" width="12" height="2" x="20" y="6"/>
    <filter id="filter-12" width="108.3%" height="200%" x="-4.2%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_comparisons">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 12)">
        <path fill="#D8D8D8" d="M14 3a1 1 0 0 1 1 1v29a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h13zm0 4H1v26h13V7zm37-4a1 1 0 0 1 1 1v29a1 1 0 0 1-1 1H38a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h13zm0 4H38v26h13V7zM34 0a1 1 0 0 1 1 1v34a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1V1a1 1 0 0 1 1-1h16zm0 4H18v31h16V4z" class="combined_shape"/>
        <rect width="7" height="2" x="4" y="28" fill="url(#linearGradient-1)" class="rectangle"/>
        <rect width="7" height="2" x="41" y="28" fill="url(#linearGradient-1)" class="rectangle"/>
        <rect width="8" height="3" x="22" y="29" fill="url(#linearGradient-2)" class="rectangle"/>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-3"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-5"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-7"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-10)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-9"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-11"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-11"/>
        </g>
      </g>
    </g>
  </g>
</svg>
