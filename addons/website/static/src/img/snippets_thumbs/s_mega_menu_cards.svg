<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="14" height="2" x="0" y="0"/>
    <filter id="filter-2" width="107.1%" height="200%" x="-3.6%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-3" d="M13 6zm-1-3v1H2V3h10zm2-3v1H0V0h14z"/>
    <filter id="filter-4" width="107.7%" height="128.6%" x="-3.8%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
     <rect id="path-5" width="13" height="6" x="0" y="0"/>
    <linearGradient id="linearGradient-6" x1="72.875%" x2="40.332%" y1="47.367%" y2="38.122%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-7" x1="88.517%" x2="50%" y1="46.899%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_mega_menu_cards">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 9)">
        <g class="group">
          <g class="image_1_border">
            <rect width="14" height="7" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.472 .47)">
              <mask id="mask-2" fill="#fff">
                <use xlink:href="#path-5"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
              <ellipse cx="10.519" cy="1.394" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.594" ry="2.394"/>
              <ellipse cx="10.821" cy="8.706" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-2)" rx="11.085" ry="5.882"/>
              <ellipse cx="-10.982" cy="10" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-2)" rx="26.018" ry="8.471"/>
            </g>
            <path fill="#FFF" d="M14 0V7H0V0H14Zm0 1H1v5h12V1z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(0 10)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
          </g>
          <g class="combined_shape" transform="translate(0 14)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(19)">
          <g class="image_1_border">
            <rect width="14" height="7" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.472 .47)">
              <mask id="mask-2" fill="#fff">
                <use xlink:href="#path-5"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
              <ellipse cx="10.519" cy="1.394" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.594" ry="2.394"/>
              <ellipse cx="10.821" cy="8.706" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-2)" rx="11.085" ry="5.882"/>
              <ellipse cx="-10.982" cy="10" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-2)" rx="26.018" ry="8.471"/>
            </g>
            <path fill="#FFF" d="M14 0V7H0V0H14Zm0 1H1v5h12V1z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(0 10)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
          </g>
          <g class="combined_shape" transform="translate(0 14)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(39)">
          <g class="image_1_border">
            <rect width="14" height="7" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.472 .47)">
              <mask id="mask-2" fill="#fff">
                <use xlink:href="#path-5"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
              <ellipse cx="10.519" cy="1.394" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.594" ry="2.394"/>
              <ellipse cx="10.821" cy="8.706" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-2)" rx="11.085" ry="5.882"/>
              <ellipse cx="-10.982" cy="10" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-2)" rx="26.018" ry="8.471"/>
            </g>
            <path fill="#FFF" d="M14 0V7H0V0H14Zm0 1H1v5h12V1z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(0 10)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
          </g>
          <g class="combined_shape" transform="translate(0 14)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(0 23)">
          <g class="image_1_border">
            <rect width="14" height="7" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.472 .47)">
              <mask id="mask-2" fill="#fff">
                <use xlink:href="#path-5"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
              <ellipse cx="10.519" cy="1.394" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.594" ry="2.394"/>
              <ellipse cx="10.821" cy="8.706" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-2)" rx="11.085" ry="5.882"/>
              <ellipse cx="-10.982" cy="10" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-2)" rx="26.018" ry="8.471"/>
            </g>
            <path fill="#FFF" d="M14 0V7H0V0H14Zm0 1H1v5h12V1z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(0 10)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
          </g>
          <g class="combined_shape" transform="translate(0 14)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(19 23)">
          <g class="image_1_border">
            <rect width="14" height="7" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.472 .47)">
              <mask id="mask-2" fill="#fff">
                <use xlink:href="#path-5"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
              <ellipse cx="10.519" cy="1.394" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.594" ry="2.394"/>
              <ellipse cx="10.821" cy="8.706" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-2)" rx="11.085" ry="5.882"/>
              <ellipse cx="-10.982" cy="10" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-2)" rx="26.018" ry="8.471"/>
            </g>
            <path fill="#FFF" d="M14 0V7H0V0H14Zm0 1H1v5h12V1z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(0 10)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
          </g>
          <g class="combined_shape" transform="translate(0 14)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(39 23)">
          <g class="image_1_border">
            <rect width="14" height="7" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.472 .47)">
              <mask id="mask-2" fill="#fff">
                <use xlink:href="#path-5"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
              <ellipse cx="10.519" cy="1.394" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.594" ry="2.394"/>
              <ellipse cx="10.821" cy="8.706" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-2)" rx="11.085" ry="5.882"/>
              <ellipse cx="-10.982" cy="10" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-2)" rx="26.018" ry="8.471"/>
            </g>
            <path fill="#FFF" d="M14 0V7H0V0H14Zm0 1H1v5h12V1z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(0 10)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
          </g>
          <g class="combined_shape" transform="translate(0 14)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
