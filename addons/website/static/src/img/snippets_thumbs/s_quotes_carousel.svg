<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="44.17%" y2="55.83%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-2" d="M11.193 7c.449 0 .839-.16 1.169-.479.33-.319.495-.691.495-1.116 0-.207-.03-.406-.091-.599a1.391 1.391 0 0 0-.313-.528 1.541 1.541 0 0 0-.606-.39c-.25-.092-.574-.139-.971-.139h-.539c.064-.691.301-1.28.712-1.765.41-.485.978-.91 1.702-1.276L12.328 0a6.386 6.386 0 0 0-2.352 1.845C9.326 2.655 9 3.46 9 4.258c0 .857.19 1.529.568 2.014.378.485.92.728 1.625.728zm5.143 0c.449 0 .838-.16 1.169-.479.33-.319.495-.691.495-1.116 0-.207-.03-.406-.091-.599a1.391 1.391 0 0 0-.313-.528 1.541 1.541 0 0 0-.606-.39c-.25-.092-.574-.139-.971-.139h-.54c.065-.691.302-1.28.713-1.765.41-.485.978-.91 1.702-1.276L17.471 0a6.386 6.386 0 0 0-2.352 1.845c-.65.81-.976 1.615-.976 2.413 0 .857.189 1.529.567 2.014.379.485.92.728 1.626.728z"/>
    <filter id="filter-3" width="111.1%" height="128.6%" x="-5.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-4" width="19" height="3" x="22" y="6"/>
    <filter id="filter-5" width="105.3%" height="166.7%" x="-2.6%" y="-16.7%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-6" d="M47.549 14a6.593 6.593 0 0 0 2.439-1.845C50.663 11.345 51 10.54 51 9.742c0-.857-.196-1.529-.589-2.014C50.02 7.243 49.457 7 48.726 7c-.466 0-.87.16-1.212.479-.343.319-.514.691-.514 1.116 0 .207.032.406.095.599.063.193.171.369.324.528.16.166.369.296.628.39.26.092.596.139 1.008.139h.558c-.066.691-.312 1.28-.738 1.765-.425.485-1.014.91-1.765 1.276l.439.708zm5 0a6.593 6.593 0 0 0 2.439-1.845C55.663 11.345 56 10.54 56 9.742c0-.857-.196-1.529-.589-2.014C55.02 7.243 54.457 7 53.726 7c-.466 0-.87.16-1.212.479-.343.319-.514.691-.514 1.116 0 .207.032.406.095.599.063.193.171.369.324.528.16.166.369.296.628.39.26.092.596.139 1.008.139h.558c-.066.691-.312 1.28-.738 1.765-.425.485-1.014.91-1.765 1.276l.439.708z"/>
    <filter id="filter-7" width="111.1%" height="128.6%" x="-5.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_quotes_carousel">
      <rect width="82" height="60" class="bg"/>
      <g fill="url(#linearGradient-1)" class="group" opacity=".4" transform="translate(0 17)">
        <g class="image_1">
          <rect width="82" height="28" class="rectangle"/>
        </g>
      </g>
      <g class="group" transform="translate(8 22)">
        <path fill="#FFF" stroke="#FFF" d="M62.5 3.648L66.659 8.5 62.5 13.352V3.648zm-59 0v9.704L-.659 8.5 3.5 3.648z" class="combined_shape"/>
        <use fill="#000" filter="url(#filter-3)" xlink:href="#path-2"/>
        <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-5)" xlink:href="#path-4"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-4"/>
        </g>
        <use fill="#000" filter="url(#filter-7)" xlink:href="#path-6"/>
        <use fill="#FFF" fill-opacity=".78" xlink:href="#path-6"/>
      </g>
    </g>
  </g>
</svg>
