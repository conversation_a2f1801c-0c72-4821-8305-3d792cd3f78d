<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="82" height="60" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="44.674%" y2="25.975%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="38.481%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_image_gallery">
      <rect width="82" height="60" class="bg"/>
      <g class="group" opacity=".5">
        <g class="oval___oval_mask">
          <mask id="mask-2" fill="#fff">
            <use xlink:href="#path-1"/>
          </mask>
          <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
          <circle cx="65.5" cy="11.5" r="7.5" fill="#F3EC60" class="oval" mask="url(#mask-2)"/>
          <ellipse cx="68.5" cy="62" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="26.5" ry="20"/>
          <ellipse cx="18" cy="67" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="51" ry="32"/>
        </g>
      </g>
      <g fill="#FFF" stroke="#FFF" class="center_group" transform="translate(9 27)">
        <path d="M61.5-1.352L65.659 3.5 61.5 8.352v-9.704zm-58 0v9.704L-.659 3.5 3.5-1.352z" class="combined_shape"/>
      </g>
    </g>
  </g>
</svg>
