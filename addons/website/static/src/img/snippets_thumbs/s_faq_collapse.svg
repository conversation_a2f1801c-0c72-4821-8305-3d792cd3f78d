<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-2" d="M38 8v1H8V8h30zm-7-3v1H8V5h23zm12-3v1H8V2h35z"/>
    <filter id="filter-3" width="102.9%" height="128.6%" x="-1.4%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-4" width="26" height="1" x="8" y="17"/>
    <filter id="filter-5" width="103.8%" height="300%" x="-1.9%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-6" width="33" height="1" x="8" y="24"/>
    <filter id="filter-7" width="103%" height="300%" x="-1.5%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-8" width="28" height="1" x="8" y="31"/>
    <filter id="filter-9" width="103.6%" height="300%" x="-1.8%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_faq_collapse">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 13)">
        <rect width="53" height="13" fill="#D8D8D8" class="rectangle" opacity=".219"/>
        <rect width="53" height="5" y="16" fill="#D8D8D8" class="rectangle" opacity=".219"/>
        <rect width="53" height="5" y="23" fill="#D8D8D8" class="rectangle" opacity=".219"/>
        <rect width="53" height="5" y="30" fill="#D8D8D8" class="rectangle" opacity=".219"/>
        <rect width="3" height="3" x="2" y="31" fill="url(#linearGradient-1)" class="rectangle" rx="1"/>
        <rect width="3" height="3" x="2" y="24" fill="url(#linearGradient-1)" class="rectangle" rx="1"/>
        <rect width="3" height="3" x="2" y="17" fill="url(#linearGradient-1)" class="rectangle" rx="1"/>
        <rect width="3" height="3" x="2" y="2" fill="url(#linearGradient-1)" class="rectangle" rx="1"/>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-3)" xlink:href="#path-2"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-5)" xlink:href="#path-4"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-4"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-7)" xlink:href="#path-6"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-6"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-9)" xlink:href="#path-8"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-8"/>
        </g>
      </g>
    </g>
  </g>
</svg>
