<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M39 23v1H20v-1h19zm6-3v1H20v-1h25z"/>
    <filter id="filter-2" width="104%" height="150%" x="-2%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-3" width="15" height="2" x="20" y="15"/>
    <filter id="filter-4" width="106.7%" height="200%" x="-3.3%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-5" width="12.5" height="10.488" x="0" y="0"/>
    <linearGradient id="linearGradient-7" x1="72.875%" x2="40.332%" y1="46.011%" y2="32.006%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-8" x1="88.517%" x2="50%" y1="37.969%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <path id="path-9" d="M53 7v26H0V7h53zm-1 1H1v24h51V8z"/>
    <filter id="filter-10" width="101.9%" height="107.7%" x="-.9%" y="-1.9%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-11" width="10" height="5" x="12" y="0"/>
    <filter id="filter-12" width="110%" height="140%" x="-5%" y="-10%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-13" width="10" height="5" x="0" y="0"/>
    <filter id="filter-14" width="110%" height="140%" x="-5%" y="-10%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-15" width="10" height="5" x="24" y="0"/>
    <filter id="filter-16" width="110%" height="140%" x="-5%" y="-10%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_tabs">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 14)">
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-1"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-3"/>
        </g>
        <g class="image_1_border" transform="translate(4 14)">
          <rect width="13" height="11" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .256)">
            <mask id="mask-6" fill="#fff">
              <use xlink:href="#path-5"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
            <ellipse cx="9.625" cy="2.686" fill="#F3EC60" class="oval" mask="url(#mask-6)" rx="1.875" ry="1.919"/>
            <ellipse cx="12.625" cy="11.512" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-6)" rx="5.875" ry="3.837"/>
            <ellipse cx=".125" cy="11.64" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-6)" rx="9.375" ry="6.012"/>
          </g>
          <path fill="#FFF" d="M13 0v11H0V0h13zm-1 1H1v9h11V1z" class="rectangle_2"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-10)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-11"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-11"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-14)" xlink:href="#path-13"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-13"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-16)" xlink:href="#path-15"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-15"/>
        </g>
      </g>
    </g>
  </g>
</svg>
