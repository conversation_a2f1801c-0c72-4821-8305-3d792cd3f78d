<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="1" height="19" x="26" y="0"/>
    <filter id="filter-2" width="200%" height="110.5%" x="-50%" y="-2.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-3" d="M13 16v1H2v-1h11zm0-11v1H5V5h8z"/>
    <filter id="filter-4" width="109.1%" height="116.7%" x="-4.5%" y="-4.2%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-5" d="M13 12v2H0v-2h13zm0-11v2H0V1h13z"/>
    <filter id="filter-6" width="107.7%" height="115.4%" x="-3.8%" y="-3.8%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-7" width="13" height="5" x="20" y="22"/>
    <filter id="filter-8" width="107.7%" height="140%" x="-3.8%" y="-10%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-9" width="11" height="3" x="21" y="23"/>
    <filter id="filter-10" width="109.1%" height="166.7%" x="-4.5%" y="-16.7%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-11" d="M51 16v1H40v-1h11zm0-11v1H40V5h11z"/>
    <filter id="filter-12" width="109.1%" height="116.7%" x="-4.5%" y="-4.2%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-13" d="M54 12v2H40v-2h14zM50 1v2H40V1h10z"/>
    <filter id="filter-14" width="107.1%" height="115.4%" x="-3.6%" y="-3.8%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <linearGradient id="linearGradient-15" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_showcase">
      <rect width="82" height="60" class="bg"/>
      <g class="group_2" transform="translate(14 17)">
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-1"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-5"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-7"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-10)" xlink:href="#path-9"/>
          <use fill="#000" fill-opacity=".348" xlink:href="#path-9"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-11"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-11"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-14)" xlink:href="#path-13"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-13"/>
        </g>
        <path fill="url(#linearGradient-15)" d="M18 12a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0-11a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm17 11a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0-11a3 3 0 1 1 0 6 3 3 0 0 1 0-6z" class="combined_shape"/>
      </g>
    </g>
  </g>
</svg>
