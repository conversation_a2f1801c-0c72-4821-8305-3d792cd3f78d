<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="82" height="60" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="44.674%" y2="25.975%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="38.481%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-5" width="15" height="2" x="13" y="2"/>
    <filter id="filter-6" width="106.7%" height="200%" x="-3.3%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0.259587944   0 0 0 0 0.259629577   0 0 0 0 0.259574831  0 0 0 0.525895979 0"/>
    </filter>
    <path id="path-7" d="M25 10v1H13v-1h12zm5-3v1H13V7h17z"/>
    <filter id="filter-8" width="105.9%" height="150%" x="-2.9%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-9" d="M28 30v2h-5v-2h5zm14 0v2h-5v-2h5zm7 0v2h-5v-2h5zm-14 0v2h-5v-2h5z"/>
    <filter id="filter-10" width="103.8%" height="200%" x="-1.9%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_carousel">
      <rect width="82" height="60" class="bg"/>
      <g class="group">
        <g class="oval___oval_mask" opacity=".5">
          <mask id="mask-2" fill="#fff">
            <use xlink:href="#path-1"/>
          </mask>
          <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
          <circle cx="65.5" cy="11.5" r="7.5" fill="#F3EC60" class="oval" mask="url(#mask-2)"/>
          <ellipse cx="68.5" cy="62" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="26.5" ry="20"/>
          <ellipse cx="18" cy="67" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="51" ry="32"/>
        </g>
        <g class="center_group" transform="translate(6 20)">
          <rect width="21" height="14" x="11" fill="#FFF" class="rectangle"/>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
            <use fill="#000" fill-opacity=".697" xlink:href="#path-5"/>
          </g>
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
            <use fill="#000" fill-opacity=".348" xlink:href="#path-7"/>
          </g>
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-10)" xlink:href="#path-9"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
          </g>
          <path fill="#FFF" stroke="#FFF" d="M3.5 2.648v9.704L-.659 7.5 3.5 2.648zm64-1L71.659 6.5 67.5 11.352V1.648z" class="combined_shape"/>
        </g>
      </g>
    </g>
  </g>
</svg>
