<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M0 17.71V4.29C0 3.577.597 3 1.336 3h27.162l-1.91 1.843H1.909v12.313h32.184v-2.475L36 12.837v4.873c-.001.712-.598 1.29-1.336 1.29H1.336C.598 19 0 18.423 0 17.71z"/>
    <filter id="filter-2" width="102.8%" height="112.5%" x="-1.4%" y="-3.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <linearGradient id="linearGradient-3" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_share">
      <rect width="82" height="60" class="bg"/>
      <g class="noun_share_3132" transform="translate(21 21)">
        <g class="path">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
        </g>
        <path fill="url(#linearGradient-3)" d="M33.852 2.409V0L40 6.234l-6.174 6.262V9.99s-3.621-.254-6.218 1.377C25.883 12.45 24.854 13.535 24 15c0 0 .233-3.89 2.44-7.433 2.76-4.43 7.412-5.158 7.412-5.158z" class="path"/>
      </g>
    </g>
  </g>
</svg>
