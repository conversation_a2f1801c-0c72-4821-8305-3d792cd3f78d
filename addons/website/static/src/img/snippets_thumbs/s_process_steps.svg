<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="31" height="1" x="11" y="5"/>
    <filter id="filter-2" width="103.2%" height="300%" x="-1.6%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <linearGradient id="linearGradient-3" x1="0%" x2="100%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_process_steps">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 25)">
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-1"/>
        </g>
        <circle cx="47.5" cy="5.5" r="5.5" fill="url(#linearGradient-3)" class="oval" opacity=".426"/>
        <circle cx="5.5" cy="5.5" r="5.5" fill="#FFF" fill-opacity=".78" class="oval"/>
        <circle cx="26.5" cy="5.5" r="5.5" fill="url(#linearGradient-3)" class="oval"/>
      </g>
    </g>
  </g>
</svg>
