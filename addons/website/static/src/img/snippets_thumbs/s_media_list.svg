<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M32 15v1H21v-1h11zm-4-3v1h-7v-1h7zm4-3v1H21V9h11z"/>
    <filter id="filter-2" width="109.1%" height="128.6%" x="-4.5%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-3" width="23" height="2" x="21" y="5"/>
    <filter id="filter-4" width="104.3%" height="200%" x="-2.2%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0.259587944   0 0 0 0 0.259629577   0 0 0 0 0.259574831  0 0 0 0.525895979 0"/>
    </filter>
    <path id="path-5" d="M42 33v1H21v-1h21zm-7-3v1H21v-1h14zm7-3v1H21v-1h21z"/>
    <filter id="filter-6" width="104.8%" height="128.6%" x="-2.4%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-7" width="13" height="2" x="21" y="23"/>
    <filter id="filter-8" width="107.7%" height="200%" x="-3.8%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0.259587944   0 0 0 0 0.259629577   0 0 0 0 0.259574831  0 0 0 0.525895979 0"/>
    </filter>
    <rect id="path-9" width="17.308" height="14.302" x="0" y="0"/>
    <linearGradient id="linearGradient-11" x1="72.875%" x2="40.332%" y1="46.131%" y2="32.548%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-12" x1="88.517%" x2="50%" y1="38.331%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-13" width="17.308" height="14.302" x="0" y="0"/>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_media_list">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 11)">
        <rect width="53" height="39" class="rectangle"/>
        <rect width="36" height="15" x="17" y="21" fill="#FFF" class="rectangle"/>
        <rect width="36" height="15" x="17" y="3" fill="#FFF" class="rectangle"/>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#000" fill-opacity=".348" xlink:href="#path-1"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#000" fill-opacity=".697" xlink:href="#path-3"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#000" fill-opacity=".348" xlink:href="#path-5"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
          <use fill="#000" fill-opacity=".697" xlink:href="#path-7"/>
        </g>
        <g class="image_1_border" transform="translate(0 3)">
          <rect width="18" height="15" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.346 .349)">
            <mask id="mask-10" fill="#fff">
              <use xlink:href="#path-9"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-9"/>
            <ellipse cx="13.327" cy="3.663" fill="#F3EC60" class="oval" mask="url(#mask-10)" rx="2.596" ry="2.616"/>
            <ellipse cx="17.481" cy="15.698" fill="url(#linearGradient-11)" class="oval" mask="url(#mask-10)" rx="8.135" ry="5.233"/>
            <ellipse cx=".173" cy="15.872" fill="url(#linearGradient-12)" class="oval" mask="url(#mask-10)" rx="12.981" ry="8.198"/>
          </g>
          <path fill="#FFF" d="M18 0v15H0V0h18zm-1 1H1v13h16V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(0 21)">
          <rect width="18" height="15" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.346 .349)">
            <mask id="mask-14" fill="#fff">
              <use xlink:href="#path-13"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-13"/>
            <ellipse cx="13.327" cy="3.663" fill="#F3EC60" class="oval" mask="url(#mask-14)" rx="2.596" ry="2.616"/>
            <ellipse cx="17.481" cy="15.698" fill="url(#linearGradient-11)" class="oval" mask="url(#mask-14)" rx="8.135" ry="5.233"/>
            <ellipse cx=".173" cy="15.872" fill="url(#linearGradient-12)" class="oval" mask="url(#mask-14)" rx="12.981" ry="8.198"/>
          </g>
          <path fill="#FFF" d="M18 0v15H0V0h18zm-1 1H1v13h16V1z" class="rectangle_2"/>
        </g>
      </g>
    </g>
  </g>
</svg>
