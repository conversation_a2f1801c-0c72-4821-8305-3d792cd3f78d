<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M19 21v4h-5v-4h5zm7 0v4h-5v-4h5zm-8 1h-3v2h3v-2zm7 0h-3v2h3v-2zm23-8v4H14v-4h34zm-1 1H15v2h32v-2zm1-8v4H14V7h34zm-1 1H15v2h32V8zm1-8v4H14V0h34zm-1 1H15v2h32V1z"/>
    <filter id="filter-2" width="102.9%" height="108%" x="-1.5%" y="-2%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-3" d="M10 21v2H2v-2h8zm0-7v2H2v-2h8zm0-7v2H3V7h7zm0-7v2H0V0h10z"/>
    <filter id="filter-4" width="110%" height="108.7%" x="-5%" y="-2.2%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <linearGradient id="linearGradient-5" x1="0%" x2="100%" y1="45.675%" y2="54.325%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_website_form">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(17 13)">
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
        </g>
        <rect width="17" height="5" x="14" y="30" fill="url(#linearGradient-5)" class="rectangle"/>
      </g>
    </g>
  </g>
</svg>
