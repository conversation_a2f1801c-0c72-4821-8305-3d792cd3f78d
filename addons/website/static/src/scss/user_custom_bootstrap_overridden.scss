//
// /!\
// This file is meant to regroup your bootstrap customizations. In that file,
// you must define variables *ONLY*. If you want to introduce new CSS rules
// for your website, check the 'user_custom_rules.scss' file you can also edit.
// /!\
//
// You can change the value of a variable you can find in the bootstrap 5
// documentation (or in the file /web/static/lib/bootstrap/scss/_variables.scss)
// and Odoo will automatically adapt its design to your new bootstrap. For
// example, doing this will make some shadows and gradients appear, especially
// for your buttons design:
//
// $enable-shadows: true;
// $enable-gradients: true;
//
// Notice that Odoo already overrides bootstrap variables according to your
// choices via the website builder (especially 3rd tab of the editor panel). You
// should first take a look at it and do customizations this way. Indeed, if you
// override the same variables yourself, Odoo will either have to ignore them or
// not be able to make the website builder work properly for these variables
// anymore.
//
