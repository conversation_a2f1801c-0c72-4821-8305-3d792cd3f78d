@keyframes fadeInDownSmall {
    0% {
        opacity: 0;
        transform: translate(0, -5px);
    }
    100% {
        opacity: 1;
        transform: translate(0, 0);
    }
}

#o_new_content_menu_choices {
    @include o-position-absolute(0, 0, 0, 0);
    position: fixed;
    display: flex;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    font-family: $o-we-font-family;
    z-index: $zindex-fixed - 1;

    &::before {
        content: " ";
        @include o-position-absolute(0, 0, 0, 0);
        z-index: -1;
        pointer-events: none;
    }

    .container {
        max-width: 720px;
        margin: auto;
    }

    .o_new_content_element {
        opacity: 0;
        animation: fadeInDownSmall 1s forwards;

        .o_uninstalled_module {
            filter: brightness(70%) contrast(60%);

        }

        .module_icon {
            display: block;
            margin: auto;
        }

        a {
            display: block;
            font-size: 34px;
            text-align: center;
            text-decoration: none;

            i {
                width: 110px;
                height: 110px;
                border: 3px solid lighten(#2C2C36, 10%);
                border-radius: 100%;
                line-height: 104px;
                background-color: #2C2C36;
                color: white;

                transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0s;
            }
            p {
                color: white;
                margin-top: 0.7em;
                font-size: 0.5em;
            }

            &:hover, &:focus {
                text-decoration: none;
                outline: none; // remove ugly dotted border on Firefox
                i {
                    border-color: #1cc1a9;
                    box-shadow: 0 0 10px rgba(28, 193, 169, 0.46);
                }
            }
        }
    }
}
