
.s_alert {
    margin: $grid-gutter-width/2 0;
    border: $alert-border-width solid;
    border-radius: $alert-border-radius;
    p, ul, ol {
        &:last-child {
            margin-bottom: 0;
        }
    }
    &.s_alert_sm {
        padding: $grid-gutter-width/3;
        font-size: $font-size-sm;
    }
    &.s_alert_md {
        padding: $grid-gutter-width/2;
        font-size: $font-size-base;
    }
    &.s_alert_lg {
        padding: $grid-gutter-width;
        font-size: $font-size-lg;
    }
    .s_alert_icon {
        float: left;
        margin-right: 10px;
    }
    .s_alert_content {
        overflow: hidden;
    }
}
