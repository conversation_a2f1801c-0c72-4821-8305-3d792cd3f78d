
#wrapwrap .s_showcase:not([data-vcss]) {
    @include media-breakpoint-up(lg) {
        .container, .container-fluid {
            position: relative;

            &:before {
                content: " ";
                display: block;
                @include o-position-absolute($left: 50%);
                height: 100%;
                border-right: 1px solid map-get($grays, '200');
            }
        }
    }

    .fa {
        opacity: 0.5;
    }

    .text-end{
        .fa {
            float: right;
            margin-left: .5em;
        }
        p {
            float: right;
            display: block;
        }
    }
    .text-start{
        .fa {
            float: left;
            margin-right: .5em;
        }
        p {
            float: left;
        }
    }
    .row {
        margin-top: 1em;
    }
    .feature p {
        max-width: 300px;
        margin-top: 0.6em;
        clear: both;
    }
}

@include media-breakpoint-down(lg) {
    #wrapwrap .s_showcase:not([data-vcss]) {
        .text-end, .text-start {
            text-align: center;

            .fa {
                font-size: 2em;
                opacity: 0.5;
                float: none;
                display: block;
                position: relative;
                margin-left: auto;
                margin-right: auto;
            }
        }
        .feature {
            margin-bottom: 3em;

            p {
                float: none;
                display: block;
                position: relative;
                margin-left: auto;
                margin-right: auto;
            }
        }
    }
}
