.s_table_of_content:not([data-vcss]) {
    .s_table_of_content_navbar_wrap {
        &.s_table_of_content_navbar_sticky {
            &.s_table_of_content_horizontal_navbar, &.s_table_of_content_vertical_navbar .s_table_of_content_navbar {
                @include o-position-sticky($top: 0px);
            }
            &.s_table_of_content_vertical_navbar .s_table_of_content_navbar {
                overflow: auto;
            }
        }
        &:not(.s_table_of_content_navbar_sticky) {
            &, .s_table_of_content_navbar {
                top: 0px !important;
            }
        }
        &.s_table_of_content_vertical_navbar .s_table_of_content_navbar {
            overflow-wrap: break-word;

            > a.list-group-item-action {
                background: none;
                color: inherit;
                opacity: 0.7;
                font-weight: $font-weight-normal + 100;
                padding-left: 3px;
                transition: padding 0.1s;

                &:before {
                    @include o-position-absolute(10px, auto, 10px, 0);
                    width: 2px;
                    content: "";
                }
                &:hover {
                    opacity: 1;
                }
                &:focus {
                    background: none;
                }
                &.active {
                    background: none;
                    padding-left: 8px;
                    opacity: 1;

                    &:before {
                        background-color: map-get($theme-colors, 'primary');
                    }
                }
            }
        }
        &.s_table_of_content_horizontal_navbar {
            z-index: 1;
            padding-top: $navbar-padding-y;
            padding-bottom: $navbar-padding-y;
            margin-bottom: $spacer * 2;

            .s_table_of_content_navbar {
                display: inline;
    
                > a {
                    &.list-group-item-action {
                        width: auto;
                    }
                    &.list-group-item {
                        display: inline-block;
                        margin-bottom: 2px;
                    }
                }
            }
        }
    }
}
