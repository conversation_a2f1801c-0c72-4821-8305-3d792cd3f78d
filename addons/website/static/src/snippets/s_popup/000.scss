// s_popup
.s_popup_main:not([data-vcss]) {
    .s_popup_content {
        // keep lower than <p> height (cookies bar)
        min-height: $o-font-size-base * $o-line-height-base;
        box-shadow: $modal-content-box-shadow-sm-up;
        .container {
            // keep margin when fixed bottom
            @include make-container();
        }
    }

    &.modal:not(.d-none) {
        display: block !important;
    }

    $popup-close-size: 1.5rem;
    .s_popup_close {
        position: absolute;
        background: white;
        height: $popup-close-size;
        width: $popup-close-size;
        line-height: $popup-close-size;
        margin-top: -1 * $popup-close-size / 2;
        margin-right: -1 * $popup-close-size / 2;
        border-radius: $popup-close-size / 2;
        right: 0px;
        top: 0px;
        box-shadow: rgba(0,0,0,0.8) 0 0 5px;
        cursor: pointer;
        text-align: center;
        z-index: 1;
        font-size: $popup-close-size;
    }

    &.s_popup_center {
        .s_popup_full {
            @include o-position-absolute(0, 0, 0, 0);
            &.modal-dialog {
                max-width: 100%;
                padding: 0 !important;
                margin: 0 !important;

                .modal-content {
                    height: 100%;
                    width: 100%;
                    justify-content: center;
                }
            }
            .s_popup_close {
                font-size: 60px;
                margin: 10px;
                background: none;
                box-shadow: none;
            }
        }
    }

    &.s_popup_fixed {
        &.s_popup_fixed_top {
            .s_popup_content {
                top: $o-navbar-height;
            }
        }
        &:not(.s_popup_fixed_top) {
            .s_popup_content {
                bottom: 0;
            }
        }
        .s_popup_content {
            z-index: $zindex-modal;
            position: fixed;
            right: 20px;
        }
        .modal-sm .s_popup_content {
            width: $modal-sm;
        }
        .modal-md .s_popup_content {
            width: $o-modal-md;
        }
        .modal-lg .s_popup_content {
            width: $o-modal-lg;
        }
        .modal-xl .s_popup_content {
            width: $modal-xl;
        }
        .s_popup_full .s_popup_content {
            right: 0;
            left: 0;
            .s_popup_close {
                box-shadow: none;
                font-size: 60px;
                margin: 10px;
                background: none;
            }

        }
    }
}
