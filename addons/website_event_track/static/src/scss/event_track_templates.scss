/*
 * EVENT TOOL: REMINDER WIDGET
 */
.o_wevent_event .o_wetrack_js_reminder {
    // Icon only
    &.btn-link {
        padding: 0;
    }

    // Ensure width for size coherency
    &:not(.btn-link) {
        min-width: 100px;
    }

    i {
        &.fa-bell {
            color: gold;
            -webkit-text-stroke-width: 0.5px;
            -webkit-text-stroke-color: black;
        }
        &.fa-bell-o {
            color: black;
        }
    }
}

/*
 * AGENDA
 */

 .o_weagenda_index {
    hr {
        border-color: lighten($o-wevent-bg-color-base-contrast, 50%);
    }
}

.o_we_online_agenda {
    overflow-x: auto;
    table {
        border-collapse: separate;
        border-spacing: 0em 0em;
        tr {
            height: 15px;
            line-height: 1em;
            &.active {
                td.active {
                    padding: 0em 0.5em;
                    font-size: smaller;
                    border-top: 1px solid lighten($o-wevent-bg-color-base-contrast, 50%);
                }
            }
        }
        th.active, td:not(.active) {
            border: 0px;
            border-right: 1em solid $body-bg;
            vertical-align: middle;
            span {
                word-break: break-word;
            }
        }
        th:not(.active), td.active {
            width: 100px;
        }
        th.position-sticky {
            left: 0;
        }
        td {
            border: 0px;

            @for $size from 1 through 20 {
                @if #{$size} != 1 {
                    &.o_location_size_#{$size} {
                        width: calc(100% / (#{$size} - 1));
                        min-width: 150px;
                    }
                } @else {
                    width: calc(100%);
                }
            }

            &.active {
                z-index: 1;
                position: sticky;
                left: 0;
                min-width: 100px;
            }
            div.o_we_agenda_card_content {
                height: 100%;
                .o_we_agenda_card_title, small {
                    word-break: break-word;
                }
                .badge:hover {
                    cursor: pointer;
                }
            }
            .badge {
                height: fit-content;
                max-width: 100%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                font-size: xx-small;
                padding: 0.2em 0.5em;
                border-radius: 1em;
                line-height: 10px;
            }
            &.invisible {
                visibility: visible !important;
                opacity: 0.3;
            }
            &.o_we_agenda_time_slot_main, &.o_we_agenda_time_slot_half {
                padding: 0;
                position: relative;
                > div {
                    position: absolute;
                    top: 0;
                    width: 100%;
                }
            }
            &.o_we_agenda_time_slot_main > div {
                padding: 0.3em;
                border-top: 1px solid lighten($o-wevent-bg-color-base-contrast, 50%);
            }
            &.o_we_agenda_time_slot_half > div {
                padding: 0.3em;
                border-top: 1px dashed lighten($o-wevent-bg-color-base-contrast, 50%);
            }
            &.event_track {
                position: relative;
                padding: 0;
                &::before {
                    content: "";
                    display: block;
                    width: 100%;
                    position: absolute;
                    top: 0;
                    border-top: 1px solid lighten($o-wevent-bg-color-base-contrast, 50%);
                }
                &::after {
                    content: "";
                    display: block;
                    width: 100%;
                    position: absolute;
                    bottom: 0;
                    border-bottom: 1px solid lighten($o-wevent-bg-color-base-contrast, 50%);
                    margin-bottom: -1px;
                    z-index: 1;
                }
                > div {
                    padding: 0.3em;
                }
                // For unpublished tracks, opacity is already reduced
                [data-publish='off'] .o_weagenda_track_badges > .o_wevent_online_badge_unpublished {
                    opacity: unset;
                }


            }
        }
    }
}

/*
 * EVENT TOOL: DATE
 */
.o_wevent_event {
    .o_we_track_day_header {
        & > div > span.h1 {
            font-size: xx-large;
        }
        a.collapsed {
            transform: rotate(-90deg);
        }
    }
}
