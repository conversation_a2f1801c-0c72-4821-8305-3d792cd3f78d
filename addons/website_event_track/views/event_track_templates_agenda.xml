<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Revamped agenda : Will need to replace agenda_online from website_event_track in master -->
<template id="agenda_online" name="Track Online: Agenda">
    <t t-call="website_event.layout">
        <!-- No tracks -->
        <div t-if="not tracks_by_days" class="container">
            <div class="h2 mb-3">No track found.</div>
            <div t-if="search_key" class="alert alert-info text-center">
                <p class="m-0">We did not find any track matching your <strong t-out="search_key"/> search.</p>
            </div>
            <div t-else="" class="alert alert-info text-center" groups="event.group_event_user">
                <a target="_blank" t-att-href="'/web?#action=website_event_track.action_event_track_from_event&amp;active_id=%s' % event.id">
                    <p class="m-0">Schedule some tracks to get started</p>
                </a>
            </div>
        </div>

        <div t-else="" class="o_wevent_online o_weagenda_index">
            <!-- Options -->
            <t t-set="option_track_wishlist" t-value="not event.is_done and is_view_active('website_event_track.agenda_topbar_wishlist')"/>
            <!-- Topbar -->
            <t t-call="website_event_track.agenda_topbar"/>
            <!-- Drag/Drop Area -->
            <div class="oe_structure" id="oe_structure_website_event_track_agenda_1"/>
            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-5">
                    <t t-call="website_event_track.agenda_main"/>
                </div>
            </div>
            <!-- Drag/Drop Area -->
            <div class="oe_structure" id="oe_structure_website_event_track_agenda_2"/>
        </div>
    </t>
</template>

<!-- ============================================================ -->
<!-- TOPBAR: BASE NAVIGATION -->
<!-- ============================================================ -->

<!-- Main topbar -->
<template id="agenda_topbar" name="Agenda Tools">
    <nav class="navbar navbar-light border-top shadow-sm d-print-none">
        <div class="container-fluid">
            <div class="d-flex flex-column flex-sm-row justify-content-between w-100">
                <ul class="o_weagenda_topbar_filters o_wevent_index_topbar_filters nav">
                </ul>
                <div class="d-flex ps-sm-3 pe-0">
                    <label class="invisible text-muted me-2" id="search_summary"><span id="search_number" class="me-1">0</span>Results</label>
                    <input type="text" class="form-control" placeholder="Filter Tracks..." id="event_track_search"/>
                </div>
            </div>
        </div>
    </nav>
</template>

<!-- Option: Tracks display: optional favorites -->
<template id="agenda_topbar_wishlist"
    inherit_id="website_event_track.agenda_topbar"
    name="Allow Wishlists"
    active="True">
    <xpath expr="//ul[hasclass('o_weagenda_topbar_filters')]" position="inside">
    </xpath>
</template>

<!-- ============================================================ -->
<!-- CONTENT: MAIN TEMPLATES -->
<!-- ============================================================ -->

<!-- Agenda Main Display -->
<template id="agenda_main" name="Tracks: Main Display">
    <section t-foreach="days" t-as="day" class="col-12">
        <!-- DAY HEADER -->
        <div class="o_we_track_day_header mt-3 w-100 d-flex justify-content-between align-items-center">
            <div class="d-flex">
                <span class="h1 m-0 fw-bold" t-out="day"
                    t-options="{'widget': 'date', 'format': 'EEEE dd'}"/>
                <div class="d-flex flex-column ms-2">
                    <span class="fw-bold" t-out="day"
                        t-options="{'widget': 'date', 'format': 'MMMM'}"/>
                    <span class="fw-bold" t-out="day"
                        t-options="{'widget': 'date', 'format': 'YYYY'}"/>
                </div>
                <div class="flex-column align-self-center ms-2">
                    <span class="small fw-light">(<t t-out="event.date_tz"/>)</span>
                </div>
            </div>
            <small class="float-end text-muted align-self-end"><t t-out="tracks_by_days[day]"/> tracks</small>
        </div>
        <hr class="mt-2 mb-2"/>

        <t t-set="locations" t-value="locations_by_days[day]"/>
        <!-- Day Agenda -->
        <div class="o_we_online_agenda">
        <table id="table_search" class="table table-sm border-0 h-100">
            <!--Header-->
            <tr>
                <th class="border-0 position-sticky"/>
                <t t-foreach="locations" t-as="location">
                    <th t-if="location" class="active text-center">
                        <span t-out="location and location.name or 'Unknown'"/>
                    </th>
                </t>
            </tr>

            <!-- Time Slots -->
            <t t-set="used_cells" t-value="[]"/>
            <t t-foreach="time_slots[day]" t-as="time_slot">
                <t t-set="is_round_hour" t-value="time_slot == time_slot.replace(minute=0)"/>
                <t t-set="is_half_hour" t-value="time_slot == time_slot.replace(minute=30)"/>

                <tr t-att-class="'%s' % ('active' if is_round_hour else '')">
                    <td class="active">
                        <b t-if="is_round_hour" t-out="time_slots[day][time_slot]['formatted_time']"/>
                    </td>

                    <t t-foreach="locations" t-as="location">
                        <t t-set="tracks" t-value="time_slots[day][time_slot].get(location, {})"/>
                        <t t-if="tracks">
                            <t t-foreach="tracks" t-as="track">
                                <t t-set="_classes"
                                    t-value="'text-center %s %s %s' % (
                                        'event_color_%s' % (track.color or 0),
                                        'event_track h-100' if track else '',
                                        'o_location_size_%d' % len(locations),
                                    )"/>
                                <t t-if="track.location_id and track.location_id == location">
                                    <td t-att-rowspan="tracks[track]['rowspan']"
                                        t-att-class="_classes">
                                        <t t-call="website_event_track.agenda_main_track"/>
                                    </td>
                                </t>
                                <t t-else="">
                                    <td t-att-colspan="len(locations)-1"
                                        t-att-rowspan="tracks[track]['rowspan']"
                                        t-att-class="_classes">
                                        <t t-call="website_event_track.agenda_main_track"/>
                                    </td>
                                </t>
                                <t t-set="used_cells" t-value="used_cells + tracks[track]['occupied_cells']"/>
                            </t>
                        </t>
                        <t t-elif="location and (time_slot, location) not in used_cells">
                            <td t-att-rowspan="1"
                                t-att-class="'o_location_size_%s %s' % (len(locations),
                                    'o_we_agenda_time_slot_half' if is_half_hour else
                                    'o_we_agenda_time_slot_main' if is_round_hour else
                                    ''
                                )"><div/></td>
                        </t>
                    </t>
                </tr>
            </t>
        </table>
        </div>
    </section>
</template>

<template id="agenda_main_track" name="Track Agenda: Track">
    <div class="d-flex flex-column h-100" t-att-data-publish="track.website_published and 'on' or 'off'">
        <div class="d-flex justify-content-end flex-wrap-reverse align-items-center o_weagenda_track_badges">
            <small t-if="track.is_track_live and not track.is_track_done and track.website_published"
                class="mx-1 badge text-bg-danger rounded-1">Live
            </small>
            <small t-if="not track.website_published and is_event_user"
                   title="Unpublished"
                   class="ms-1 mt-1 mt-md-0 badge text-bg-danger o_wevent_online_badge_unpublished">Unpublished</small>
            <span t-if="option_track_wishlist">
                <t t-call="website_event_track.track_widget_reminder">
                    <t t-set="reminder_light" t-value="True"/>
                    <t t-set="reminder_small" t-value="True"/>
                    <t t-set="light_theme" t-value="False"/>
                </t>
            </span>
        </div>

        <div class="o_we_agenda_card_content d-flex flex-column justify-content-center my-1">
            <div class="o_we_agenda_card_title">
                <t t-if="track.website_published or is_event_user">
                    <a t-att-href="'/event/%s/track/%s' % (slug(event), slug(track))" class="text-black text-bold">
                        <t t-out="track.name"/>
                    </a>
                </t>
                <t t-else="">
                    <span class="text-muted text-bold">
                        <t t-out="track.name"/>
                    </span>
                </t>
            </div>
            <div class="text-muted text-center" t-if="track.partner_tag_line">
                <small t-out="track.partner_tag_line"/>
            </div>
            <div class="d-flex justify-content-center flex-wrap">
                <t t-foreach="track.tag_ids" t-as="tag">
                    <span t-if="tag.color" t-att-title="tag.name"
                          t-attf-class="me-1 mt-1 badge #{'o_tag_color_'+str(tag.color)}" t-out="tag.name"
                          onclick="
                            var value = this.title;
                            var target = $('#event_track_search');
                            if (target.val() == value) { target.val(''); } else { target.val(value); }
                            target.trigger('input');
                          "
                    />
                </t>
            </div>
        </div>
    </div>
</template>

</odoo>
