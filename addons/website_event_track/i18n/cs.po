# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# <PERSON> <brencic<PERSON><EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# ka<PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>o<PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>e<PERSON> <<EMAIL>>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON> Wacławek, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "\"Events App Name\" field is required."
msgstr "Pole \"Events App Name\" je povinné."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr "# Seznam přání"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s from %(company)s"
msgstr "%(name)s z %(company)s"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s, %(function)s at %(company)s"
msgstr "%(name)s, %(function)s v %(company)s"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "%s Events"
msgstr "%s Akce"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/controllers/webmanifest.py:0
#, python-format
msgid "%s Online Events Application"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "&amp;bull;"
msgstr "&amp;bull;"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr "10 nápadů na DIY nábytek pro úplné začátečníky"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr "6 tipů a triků pro začátečníky při práci se dřevem"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "<b>Contact Information</b>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>E-mail</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>Telefon</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>Navrhl</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speaker Biography</b>:"
msgstr "<b>Životopis řečníka:</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Speaker Profile</b>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Talk Intro</b>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>Úvodní řeč</b>:"

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    Vážený <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandone Freemane</t><br>\n"
"    S potěšením vám oznamujeme, že váš návrh <t t-out=\"object.name or ''\">O čem tato akce je</t> byl přijat a potvrzen pro událost <t t-out=\"object.event_id.name or ''\">Online odhalení kolekce OpenWood</t>.\n"
"    <br>\n"
"    Více podrobností najdete zde:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            Zobrazit přednášku\n"
"        </a>\n"
"    </div>\n"
"    <br><br>\n"
"    Děkujeme\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell me-2 text-muted\"/> Favorite Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "<i class=\"fa fa-folder-open\"/> Favorites"
msgstr "<i class=\"fa fa-folder-open\"/> Oblíbené"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid ""
"<i class=\"fa fa-fw fa-arrow-right\"/>\n"
"                        Write one."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block ms-2\">&amp;bull;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"
msgstr ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Chyba\" title=\"Chyba\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Introduction</span>\n"
"                                            <span>*</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Title</span>\n"
"                                            <span>*</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid ""
"<span for=\"contact_name\">Name</span>\n"
"                    <span>*</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"me-1\">0</span>Results"
msgstr "<span id=\"search_number\" class=\"me-1\">0</span> výsledků"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content_partner_info
msgid "<span> at </span>"
msgstr "<span> v </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<span>New track proposal</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "<span>hours</span>"
msgstr "<span>hodin</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are accepted in lightning talks."
msgstr ""
"<strong>Bleskové rozhovory</strong>. Toto jsou 30 minutové rozhovory na mnohá\n"
"                                    různá témata. Většina témat je akceptovaných v bleskových rozhovorech."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr "<strong>Poloha:</strong>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""
"<strong>Pravidelné rozhovory</strong>. Toto jsou standardní rozhovory s prezentacemi,\n"
"                                    s přidělenými 60 minutovým prostorem."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr "Technické vysvětlení, jak používat aplikace počítačového designu"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"Stav kanban sledování označuje speciální situace, které ji ovlivňují:\n"
" * Šedá je výchozí situace\n"
" * Červená znamená, že něco brání postupu ve sledování\n"
" * Zelená znamená, že sledování je připravené k přesunu do další fáze"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "Vyžaduje akci"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "Aktivní"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorace výjimečné aktivity"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr ""
"Přidejte popis, abyste svým spolupracovníkům pomohli pochopit význam a účel "
"fáze."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Add a description..."
msgstr "Přidat popis..."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "Přidejte novou fázi v ulohách obchodní nástenky"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid ""
"Add tags to your tracks to help your attendees browse your event web pages."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management : tips and tricks from the fields"
msgstr "Pokročilé řízení příležitostí: tipy a triky z terénu"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "Pokročilé přehledy"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Agenda"
msgstr "Agenda"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr "Všechny rozhovory"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr ""

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Allow push notifications?"
msgstr "Povolit push oznámení?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our website."
msgstr ""
"Umožnit video a audio záznam jejich prezentace, publikování na našich "
"webových stránkách."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr "Vždy na seznamu přání"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "Oznámeno"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Application"
msgstr "Aplikace"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "Archivováno"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "Publikum"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "Dostupné tagy sledování"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Biography"
msgstr "Životopis"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage1
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage2
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage3
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage4
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage5
#, python-format
msgid "Blocked"
msgstr "Blokováno"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr "Rezervujte si místa na nejlepší přednášky"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr "Rezervujte si své přednášky"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr "Stavba chaty pro kutily od základů"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr "Cílová adresa URL tlačítka"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr "Název tlačítka"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Button appears"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr "Tlačítko výzvy k akci je k dispozici"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "Výzva k předkládání návrhů"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "Může publikovat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Canceled Stage"
msgstr "Zrušená fáze"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "Zrušeno"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Categories"
msgstr "Kategorie"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "Kategorie"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr "Klimaticky pozitivní"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "Barva"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "Barevný index"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/controllers/event_track.py:0
#, python-format
msgid "Coming soon"
msgstr "Již brzy"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr "Již brzy ..."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "Firma"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr " Název společnosti "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of {{ object.name }}"
msgstr "Potvrzení o {{ object.name }}"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "Potvrzeno"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""
"Uvažujete o stavbě dřevěného domu? Podívejte se na toto video a zjistěte "
"více informací o postupu výstavby a konečném výsledku. Jednoduché vysvětlení"
" krok za krokem! Máte zájem?"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Contact Details"
msgstr "Kontaktní údaje"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_email
#, python-format
msgid "Contact Email"
msgstr "Kontaktní mail:"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_phone
msgid "Contact Phone"
msgstr "Kontaktní telefon"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_email
msgid "Contact email is private and used internally"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact me through a different email/phone"
msgstr "Kontaktujte mě přes jiný e-mail/telefon"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_id
msgid "Contact of the track, may be different from speaker."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_phone
msgid "Contact phone is private and used internally"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Create a Track"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid "Create a Track Location"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid "Create a Track Tag"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "Zákazník"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr "Projekt dřevěného obložení pro kutily"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "Datum"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr "Shrnutí 2. dne"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr "Nakládání s nábytkem OpenWood"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Define labels explaining kanban state management."
msgstr "Definujte štítky vysvětlující řízení stavu kanbanu."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_location__sequence
msgid "Define the order in which the location will appear on \"Agenda\" page"
msgstr "Definujte pořadí, v jakém se umístění zobrazí na stránce \"Agenda\"."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""
"Definujte kroky, které budou použity v události od\n"
"vytvoření stopy až po její uzavření.\n"
"Tyto etapy budete používat ke sledování postupu v rámci\n"
"řešení stopy události."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "Smazat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "Popis"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_tag_line
msgid "Description of the partner (name, function and company name)"
msgstr "Popis partnera (jméno, funkce a název společnosti)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "Designová soutěž (celé odpoledne)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "Podrobný plán našich nových produktů"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "Objevte náš nový návrhářský tým"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta
msgid ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."
msgstr ""
"Zobrazení tlačítka s výzvou k akci pro účastníky během sledování vašeho "
"vystoupení."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "Hotovo"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Dropdown menu"
msgstr "Rozbalovací nabídka"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "Trvání"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr "Jednoduchý způsob stavby dřevěného domu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "Upravit sledování"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Email"
msgstr "E-mail "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "Šablona e-mailu"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Error"
msgstr "Chyba"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "Událost"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "Místo události"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "Lokace Události"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Event Page"
msgstr "Stránka Události"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr "Šablona události"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "Zaměření události"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "Lokace tématu události"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "Fáze tématu události"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "Štítek tématu události"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr "Kategorie štítků tématu události"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "Sledování události"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr "Shrnutí události"

#. module: website_event_track
#: model:mail.template,name:website_event_track.mail_template_data_track_confirmation
msgid "Event: Track Confirmation"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr ""

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Favorite On"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "Oblíbené"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks..."
msgstr "Filtrovat stopy..."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Filter by Tags"
msgstr "Filtrovat podle štítků"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "Dokončeno"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr "Shrnutí prvního dne"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "Složený v kanbanu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "Sledující"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledující (partneři)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skvělá ikona písma, např. fa-úkoly"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "Formát"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_fully_accessible
msgid "Fully accessible"
msgstr "Plně přístupné"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "Budoucí činnosti"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr "Připravte se a"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "Zelená"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_done
msgid "Green Kanban Label"
msgstr "Zelený Kanban štítek"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "Šedá"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Šedý Kanban štítek"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "Vysoká"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "Nejvyšší"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr "Domovská stránka"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "Dům světových kultur"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "How can our team get in touch with you?"
msgstr "Jak se s vámi může náš tým spojit?"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr "Jak vytvořit svou marketingovou strategii v konkurenčním prostředí"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "Jak komunikovat s vaší komunitou"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "Jak navrhnout nový kus nábytku"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "Jak vyvíjet automatizované procesy"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "Jak nás sledovat v sociálních médiích"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "Jak zlepšit procesy kontroly kvality"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "Jak integrovat hardwarové materiály do nábytku"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr ""
"Jak optimalizovat svůj prodej, od příležitostí k prodejním objednávkám"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona označuje vyjímečnou aktivitu."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_fully_accessible
msgid ""
"If checked, automatically publish tracks so that access links to customers "
"are provided."
msgstr ""
"Pokud je zaškrtnuto, automaticky publikuje stopy tak, aby byly poskytnuty "
"přístupové odkazy pro zákazníky."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Pokud je zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud je zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "If checked, the related tracks will be visible in the frontend."
msgstr ""
"Pokud je zaškrtnuto, budou související vystoupení viditelné ve frontendu."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr ""
"Pokud je nastaven e-mail, bude zákazníkovi zaslán e-mail, když trať dosáhne "
"tohoto kroku."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."
msgstr ""
"Pokud je nastaveno, bude přednáška nastavena jako oblíbená pro každého "
"účastníka přihlášeného na akci."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "URL obrázku"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "In"
msgstr "V"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage1
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage2
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage3
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage4
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage5
#, python-format
msgid "In Progress"
msgstr "Probíhá"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr "V tomto videu uvidíte, jak se vyrábí řezivo na pile."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr "V tomto videu jsem popsal 6 tipů a triků, které pomohou začátečníkům:"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install"
msgstr "Instalovat"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install Application"
msgstr "Nainstalovat aplikaci"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr "Interaktivita"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "Úvod"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "Je sledující"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "Je publikováno"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr "Je na seznamu přání"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "Pracovní pozice"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Job Title"
msgstr "Pracovní pozice"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Blokované vysvětlení Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Kanban Pokračující vysvětlení"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "Stav kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state_label
msgid "Kanban State Label"
msgstr "Státní označení Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_done
msgid "Kanban Valid Explanation"
msgstr "Kanban platné vysvětlení"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "Klíčové faktory úspěchu při prodeji nábytku"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr "Kuchyně pro budoucnost"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "Zpožděné činnosti"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "Nejnovější trendy"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr "Méně nábytku znamená víc."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr "Život doma po celém světě: Příběh Williama"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "Živě"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "Místo"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr "Stavba srubu"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr "Klády na řezivo"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "Nízké"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "Oběd"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr "Kouzelné tlačítko"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."
msgstr ""
"Zde můžete spravovat místa, ve kterých organizujete své vystoupení (např. "
"Místnosti, Kanály, ...)."

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "Médium"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Typ nabídky"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zpráv"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "Minimální, ale efektivní design"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr "Minuty před zahájením Výzvy k akci"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr "Tip pro pokosovou pilu"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "Dopolední pauza"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mé aktivity"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "Globální prezentace mé společnosti"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "Jméno"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "Nový certifikační program"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "Nová stopa"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Další událost z kalendáře aktivit"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termín další aktivity"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "Souhrn další aktivity"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "Další typ aktivity"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "No Track Visitors yet!"
msgstr ""

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "No results found"
msgstr "Nenalezeny žádné výsledky"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track favorited by this visitor"
msgstr "Žádné vystoupení, které by tento návštěvník měl v oblíbených"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr ""
"Všimněte si, že štítky bez barvy nebudou na webových stránkách k dispozici."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet zpráv vyžadujících akci"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr "Staré znamená nové"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together !"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
msgid "Partner"
msgstr "Partner"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "Programy partnerství"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Phone"
msgstr "Telefon"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Picture"
msgstr "Obrázek"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr ""
"Naplánujte si svůj zážitek přidáním oblíbených rozhovorů do seznamu přání"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please enter either a contact email address or a contact phone number."
msgstr ""
"Zadejte prosím kontaktní e-mailovou adresu nebo kontaktní telefonní číslo."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please fill out the form correctly."
msgstr "Vyplňte prosím formulář správně."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "Prezentace portfolia"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr "Pěkně. Ošklivě. Půvabně."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "Předchozí strana"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "Priorita"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr ""
"Asi jedna z nejčastějších otázek, které dostávám, je, jak jsem začal "
"pracovat se dřevem! V tomto videu se s vámi podělím o to, jak/proč jsem "
"začal stavět nábytek!"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "Návrh"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr "Návrhy jsou uzavřeny!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr "Návrhy na webových stránkách"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Published"
msgstr "Publikováno"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "Zvyšování kvality informací od vašich zákazníků"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage1
#: model:event.track.stage,legend_done:website_event_track.event_track_stage2
#: model:event.track.stage,legend_done:website_event_track.event_track_stage3
#: model:event.track.stage,legend_done:website_event_track.event_track_stage4
#: model:event.track.stage,legend_done:website_event_track.event_track_stage5
#, python-format
msgid "Ready for Next Stage"
msgstr "Připraveno pro další fázi"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "Červená"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Červená Kanban visačka"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "Odmítnuto"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr "Relativní čas v porovnání se startem vystoupení (v sekundách)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "Odpovědný"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "Zodpovědný uživatel"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr "Restaurování starých dřevoobráběcích nástrojů"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr "Obnova starých dřevoobráběcích nástrojů"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr "Pravoúhlá svěrka"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimalizováno"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "Schedule some tracks to get started"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr "Zabezpečení vašeho řeziva během přepravy"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "Select categories"
msgstr "Vyber kategorie"

#. module: website_event_track
#: model:mail.template,description:website_event_track.mail_template_data_track_confirmation
msgid ""
"Sent to speakers whose track proposal is accepted (set the template on the "
"right stage)"
msgstr ""
"Odesláno přednášejícím, jejichž návrh vystoupení byl přijat (nastavte "
"šablonu na odpovídající fázi)."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "SEO jméno"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Set Favorite"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Zobrazit všechny záznamy, které mají následující datum akce před dneškem"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker"
msgstr "Mluvčí"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker Bio"
msgstr "Životopis přednášejícího"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speaker Email"
msgstr "E-mail mluvčího"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr "Fotografie mluvčího"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_email
msgid ""
"Speaker email is used for public display and may vary from contact email"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_name
msgid "Speaker name is used for public display and may vary from contact name"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_phone
msgid ""
"Speaker phone is used for public display and may vary from contact phone"
msgstr ""

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speakers"
msgstr "Řečníci"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "Fáze"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Popis fáze a popisky"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "Název fáze"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "Starting now!"
msgstr "Začínáme právě teď!"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "Stav a strategie"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stav na základě aktivit\n"
"Vypršeno: Datum již uplynulo\n"
"Dnes: Datum aktivity je dnes\n"
"Plánováno: Budoucí aktivity."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "předložení dohody"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "Odeslat návrh"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_tag_line
msgid "Tag Line"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "Název tagu"

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Název značky již existuje!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "Tagy"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Talk Proposals"
msgstr "Návrhy rozhovorů"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk added to your Favorites"
msgstr "Talk byl přidán k vašim oblíbeným"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk already in your Favorites"
msgstr ""

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk removed from your Favorites"
msgstr ""

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#, python-format
msgid "Talks"
msgstr "Projevy"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "Typy rozhovorů"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Probíhá úloha. Klepnutím zablokujete nebo nastavíte jako hotovo."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "Úkol je blokován. Kliknutím odblokujete nebo nastavíte jako hotové."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Thank you for your proposal."
msgstr "Děkujeme za váš návrh."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "Celá adresa URL pro přístup k dokumentu prostřednictvím webstránky."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "Nová marketingová strategie"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "Nový způsob propagace vašich výtvorů"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr ""
"Existuje mnoho nápadů, které stojí za to prozkoumat, takže začněte s 10 "
"nápady na nábytek pro úplné začátečníky."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""
"Existuje několik variant dřeva, které jsou na světě k dispozici, ale my "
"mluvíme o těch nejdražších na světě a v souladu s tímto bodem jsme "
"uspořádali deset nejdražších dřev."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"They will be created automatically once attendees start browsing your "
"events."
msgstr ""
"Vytvoří se automaticky, jakmile účastníci začnou procházet vaše události."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr "Tato akce nepřijímá návrhy."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr ""
"Toto pole obsahuje obrázek použitý jako ikona mobilní aplikace na webových "
"stránkách (formát PNG)."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr "Toto pole obsahuje název progresivní webové aplikace události."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr ""
"Tato stránka ještě nebyla uložena pro čtení offline.<br/>Zkontrolujte prosím"
" své připojení k síti."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Tato fáze je v kanbanovém pohledu přeložena, pokud v této fázi nejsou žádné "
"záznamy k zobrazení."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "Tento krok je proveden. Klikněte pro zablokování nebo nastavení."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "This track does not have a description."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""
"Včasné zveřejnění prezentačních materiálů (\"prezentací\"),\n"
"                                    ke zveřejnění na našich webových stránkách."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "Název"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "Dnešní činnosti"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr "Nářadí pro začátečníky v práci se dřevem"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr "10 nejdražších dřev na světě"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""
"Nejdražší dřevo na světě je docela zajímavé téma a několik lidí možná "
"překvapí, že na světě existují stovky druhů dřeva, které se liší svými "
"vlastnostmi a použitím."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "Track"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "Vazba Téma / Návštěvník"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr "Datum sledování"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr ""

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr "Lokace aktivit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Track Proposals Menu Item"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr "Fáze aktivit"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr "Kategorie štítků aktivit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "Štítky aktivit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr "Sledovat návštěvníka"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr "Návštěvníci aktivit"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on your events, including how many times "
"tracks have been wishlisted."
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__duration
msgid "Track duration in hours."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "Aktivity"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Tracks Menu Item"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define your event schedule. They can be talks, workshops or any "
"similar activity."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr "Skladby na webových stránkách"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ zaznamenané výjimečné aktivity."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "Nepublikovaný"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "Nepřečtené zprávy"

#. module: website_event_track
#. odoo-python
#: code:addons/website_event_track/models/event_track.py:0
#: code:addons/website_event_track/models/website_visitor.py:0
#, python-format
msgid "Unsupported 'Not In' operation on track wishlist visitors"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""
"Pomocí těchto jednoduchých kroků můžete snadno převážet dlouhé řezivo v "
"krátkém skříňovém pickupu. Dávka tesařského důvtipu spolu s několika prkny, "
"pevným popruhem a několika šrouby je vše, co používám ke snadnému převážení "
"dlouhých prken ze skladu dřeva do dílny nebo na staveniště Next Level "
"Carpentry."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""
"Použití jedinečného způsobu omotání vázacího popruhu (NE Bungee šňůr!!!) "
"umožňuje bezpečné přichycení řeziva BEZ nutnosti vázat a rozvazovat složité "
"nebo komplikované uzly."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "Zobrazení sledování"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "Visible in agenda"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "Viditelné na aktuální webstránce"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "Návštěvník"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr "Seznam přání návštěvníků"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr "Seznam přání návštěvníků"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr "Hlas od zákazníka"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to add this track to their list of favorites"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We did not find any track matching your"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr "Požadujeme od řečníků, aby přijali dohodu, ve které se zavazují:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                            presentations, from reports on academic and\n"
"                            commercial projects to tutorials and case\n"
"                            studies. As long as the presentation is\n"
"                            interesting and potentially useful to the\n"
"                            audience, it will be considered for\n"
"                            inclusion in the programme."
msgstr ""
"Přijímáme širokou škálu prezentací, od zpráv o akademických a komerčních "
"projektech až po výukové programy a případové studie. Pokud bude prezentace "
"zajímavá a potenciálně užitečná pro posluchače, bude zváženo její zařazení "
"do programu."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "Vyhodnotíme váš návrh a brzy se k vám vrátíme."

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
msgid "Website"
msgstr "Webstránka"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr "Ikona webové aplikace"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "Nabídka událostí na webu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr "Obrázek webové stránky"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "Menu webstránky"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "Zprávy webstránky"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "URL webové stránky"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "Návštěvník webstránky"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "Historie komunikace webstránky"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "Meta-popis webových stránek"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta-klíčová slova webových stránek"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "Meta-název webových stránek"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "Opengraph obrázek webových stránek"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr "Vítejte ve 2. dni"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr "O čem tato událost je"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "What is your talk about?"
msgstr "O čem je řeč?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Who will give this talk? We will show this to attendees to showcase your "
"talk."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway ?"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr "Na seznamu přání od"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
msgid "Wishlisted Tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.snippet_options
msgid "Wishlists"
msgstr "Seznamy přání"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr "Práce se dřevem: Jak jsem začínal!"

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "You cannot access this page."
msgstr "Na tuto stránku nemáte přístup."

#. module: website_event_track
#. odoo-javascript
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid ""
"You have to enable push notifications to get reminders for your favorite "
"tracks."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr "Jste offline!"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "a few seconds"
msgstr "několik sekund"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "ago"
msgstr "před"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. \"John Doe was born in...\""
msgstr "Např. \"Jan Novák se narodil v...\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. \"This talk will be about...\""
msgstr "Např. \"Tato přednáška bude o...\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now !"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "např. Inspirující Business Talk"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr "Např. http://www.example.com"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "hodin"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after track starts"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_online
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "search."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr "začíná v"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr "začíná v"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "tracks"
msgstr ""
