# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_delivery
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# far<PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:37+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_shipping_method
msgid "<b>Shipping Method: </b>"
msgstr "<b>روش حمل و نقل:</b>"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"شرح محصولی که می خواهید به مشتریان خود اطلاع دهید. این توضیحات در هر سفارش "
"فروش، سفارش تحویل و فاکتور مشتری/یادداشت اعتبار کپی می‌شود."

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "Anonymous express checkout partner for order %s"
msgstr "شریک تسویه حساب سریع ناشناس برای سفارش%s"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__can_publish
msgid "Can Publish"
msgstr "می تواند منتشر کند"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose a delivery method"
msgstr "یک روش تحویل را انتخاب کنید"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_res_country
msgid "Country"
msgstr "کشور"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "مبلغ تحویل"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "تحویل پس از انتخاب روش تحویل جدید به روز می شود"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery:"
msgstr "تحویل:"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "توضیحات در تجارت الکترونیک و در نقل قول های آنلاین نمایش داده می شود."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "توضیحات برای نقل قول های آنلاین"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "روش های حمل و نقل Easypost"

#. module: website_sale_delivery
#. odoo-javascript
#: code:addons/website_sale_delivery/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Free"
msgstr "رایگان"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__is_published
msgid "Is Published"
msgstr "منتشر شده است"

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"به نظر می رسد یک روش تحویل با آدرس شما سازگار نیست. لطفا صفحه را مجددا "
"بارگزاری کرده و دوباره تلاش کنید."

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"به نظر می رسد در حال حاضر تراکنشی برای سفارش شما انجام شده است، دیگر نمی "
"توانید روش تحویل را تغییر دهید"

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"هیچ روش حمل و نقلی برای سفارش فعلی و آدرس حمل و نقل شما موجود نیست. لطفا "
"برای اطلاعات بیشتر با ما تماس بگیرید."

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "No shipping method is selected."
msgstr "هیچ روش حمل و نقل انتخاب نشده است."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_search_inherit_website_sale_delivery
msgid "Published"
msgstr "منتشر شده"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_id
msgid "Restrict publishing to this website."
msgstr "محدود کردن انتشار به این وبسایت."

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Sales Order"
msgstr "سفارش فروش"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "سطر سفارش‌فروش"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
msgid "Select to compute delivery rate"
msgstr "برای محاسبه نرخ تحویل انتخاب کنید"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale_delivery.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "روش ارسال"

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "با عرض پوزش، ما نمی توانیم سفارش شما را ارسال کنیم"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order__amount_delivery
msgid "The amount without tax."
msgstr "مبلغ بدون مالیات."

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_url
msgid "The full URL to access the document through the website."
msgstr "آدرس URL کامل برای دسترسی سند در وبسایت."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_published
msgid "Visible on current website"
msgstr "قابل دید در وبسایت حاضر"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_id
msgid "Website"
msgstr "تارنما"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_url
msgid "Website URL"
msgstr "URL وبسایت"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "روش های ارسال bpost"
