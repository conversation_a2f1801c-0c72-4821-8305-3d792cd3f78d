# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_delivery
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <juhasz.<PERSON><PERSON><PERSON><PERSON>@josafar.hu>, 2022
# <AUTHOR> <EMAIL>, 2022
# krnkris, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>é<PERSON> <<EMAIL>>, 2022
# 5768b353f27900ae76ad88cc42dfd5b8_3bb349f, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:37+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: 5768b353f27900ae76ad88cc42dfd5b8_3bb349f, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_shipping_method
msgid "<b>Shipping Method: </b>"
msgstr "<b>Szállítási mód: </b>"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"A termék leírása, mely kommunikálásra kerül a vevők felé.  Ez a leírás kerül"
" átmásolásra minden egyes vevői rendelésre, szállítási rendelésre és vevői "
"számlára/jóváírásra."

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "Anonymous express checkout partner for order %s"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__can_publish
msgid "Can Publish"
msgstr "Publikálhat"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose a delivery method"
msgstr "Válassz szállítási módot"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_res_country
msgid "Country"
msgstr "Ország"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "Szállítási mennyiség"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr ""
"A szállítás részletei frissülnek az új szállítási mód kiválasztása után"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery:"
msgstr "Szállítás:"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "Leírás az eÉrtékesítésen és az Online árajánlaton megjelenítve."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Leírás az online árajánlatokhoz"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr ""

#. module: website_sale_delivery
#. odoo-javascript
#: code:addons/website_sale_delivery/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Free"
msgstr "Szabad"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__is_published
msgid "Is Published"
msgstr "Közzétett"

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"Úgy tűnik, hogy egy szállítási mód nem érhető el az Ön címén. Kérjük, "
"frissítse az oldalt, és próbálja újra."

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"Úgy tűnik, hogy már történt egy tranzakció a rendeléséhez kapcsolódóan. A "
"szállítási módot már nem tudja megváltoztatni"

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Nincs elérhető szállítási mód a jelenlegi vásárlói megrendelésére és "
"szállítási címre. Kérem lépjen velünk kapcsolatba további információért."

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "No shipping method is selected."
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_search_inherit_website_sale_delivery
msgid "Published"
msgstr "Közzétett"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_id
msgid "Restrict publishing to this website."
msgstr "Közzététel korlátozása erre a weboldalra."

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Sales Order"
msgstr "Vevői rendelések"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Értékesítési megrendelés sor"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
msgid "Select to compute delivery rate"
msgstr "Válasszon a szállítási díj kalkulációjához"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale_delivery.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Szállítási módok"

#. module: website_sale_delivery
#. odoo-python
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "Bocsánat, nem tudjuk szállítani a megrendelését"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order__amount_delivery
msgid "The amount without tax."
msgstr "Összeg adó nélkül."

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"A teljes elérési út/URL a dokumentum weboldalon keresztüli eléréséhez."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_published
msgid "Visible on current website"
msgstr "Látható ezen a weboldalon"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_id
msgid "Website"
msgstr "Honlap"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_url
msgid "Website URL"
msgstr "Honlap címe"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr ""
