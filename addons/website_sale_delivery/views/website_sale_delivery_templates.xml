<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="cart_delivery" name="Delivery Costs" inherit_id="website_sale.total">
        <xpath expr="//tr[@id='order_total_untaxed']" position="before">
            <tr id="order_delivery" t-if="website_sale_order and website_sale_order.carrier_id">
              <td class="text-end border-0 text-muted"  title="Delivery will be updated after choosing a new delivery method">Delivery:</td>
              <td class="text-xl-end border-0 text-muted" >
                   <span t-field="website_sale_order.amount_delivery" class="monetary_field" style="white-space: nowrap;" t-options='{
                      "widget": "monetary",
                      "display_currency": website_sale_order.currency_id,
                  }'/>
              </td>
            </tr>
        </xpath>
    </template>

    <template id="payment_delivery_methods">
        <input t-att-value="delivery.id" t-att-id="'delivery_%i' % delivery.id" type="radio" name="delivery_type" t-att-checked="order.carrier_id and order.carrier_id.id == delivery.id and 'checked' or False" t-att-class="'d-none' if delivery_nb == 1 else ''"/>
        <label class="label-optional" t-field="delivery.name"/>
        <t t-set='badge_class' t-value="(delivery_nb != 1 and 'float-end ' or '') + 'badge text-bg-secondary'" />
        <span t-attf-class="#{badge_class} o_wsale_delivery_badge_price">Select to compute delivery rate</span>
        <t t-if="delivery.website_description">
            <div t-field="delivery.website_description" class="text-muted mt8"/>
        </t>
    </template>

    <template id="payment_delivery_shipping_method" name="Delivery Shipping method" inherit_id="website_sale.address_on_payment">
        <xpath expr="//div[@id='shipping_and_billing']" position="inside">
            <t t-set="delivery_nb" t-value="deliveries and len(deliveries) or 0"/>
            <div t-if="delivery_nb == 1" id="delivery_carrier" class="mt4">
                <b>Shipping Method: </b>
                <t t-foreach="deliveries" t-as="delivery">
                    <div class="o_delivery_carrier_select d-inline">
                        <t t-call="website_sale_delivery.payment_delivery_methods"/>
                    </div>
                </t>
            </div>
        </xpath>
    </template>

    <template id="payment_delivery" name="Delivery Costs" inherit_id="website_sale.payment">
        <!-- //t[@t-if='website_sale_order.amount_total'] should be removed in master -->
        <xpath expr="//t[@name='website_sale_non_free_cart'] | //t[@t-if='website_sale_order.amount_total']" position="before">
            <div t-if="deliveries" id="delivery_carrier">
                <t t-set="delivery_nb" t-value="len(deliveries)"/>
                <h3 t-if="delivery_nb &gt; 1" class="mb24">Choose a delivery method</h3>
                <div t-if="delivery_nb &gt; 1" class="card border-0" id="delivery_method">
                    <ul class="list-group">
                    <t t-foreach="deliveries" t-as="delivery">
                        <li class="list-group-item o_delivery_carrier_select">
                            <t t-call="website_sale_delivery.payment_delivery_methods"/>
                        </li>
                    </t>
                    </ul>
                </div>
            </div>
        </xpath>
    </template>

</odoo>
