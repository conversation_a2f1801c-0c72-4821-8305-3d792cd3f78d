<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="slide_channel_invite_view_form" model="ir.ui.view">
            <field name="name">slide.channel.invite.view.form</field>
            <field name="model">slide.channel.invite</field>
            <field name="arch" type="xml">
                <form string="Compose Email" class="o_mail_composer_form">
                    <group col="1">
                        <group col="2">
                            <field name="channel_id" invisible="1"/>
                            <field name="partner_ids"
                                widget="many2many_tags_email"
                                placeholder="Add existing contacts..."
                                required="1"
                                context="{'force_email':True, 'show_email':True, 'no_create_edit': True, 'no_quick_create': True}"/>
                        </group>
                        <group col="2">
                            <field name="render_model" invisible="1"/>
                            <field name="subject" placeholder="Subject..."/>
                        </group>
                        <field name="can_edit_body" invisible="1"/>
                        <field name="body" class="oe-bordered-editor" options="{'style-inline': true}" attrs="{'readonly': [('can_edit_body', '=', False)]}" force_save="1"/>
                        <group>
                            <group>
                                <field name="attachment_ids" widget="many2many_binary"/>
                            </group>
                            <group>
                                <field name="template_id" label="Use template" context="{'default_model': 'slide.channel.partner'}"/>
                            </group>
                        </group>
                    </group>
                    <footer>
                        <button string="Send" name="action_invite" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>
