<?xml version="1.0"?>
<odoo>

<record id="slide_channel_pages_tree_view" model="ir.ui.view">
    <field name="name">Course Pages Tree</field>
    <field name="model">slide.channel</field>
    <field name="priority">99</field>
    <field name="mode">primary</field>
    <field name="inherit_id" ref="slide_channel_view_tree"/>
    <field name="arch" type="xml">
        <xpath expr="//tree" position="attributes">
            <attribute name="js_class">website_pages_list</attribute>
            <attribute name="type">object</attribute>
            <attribute name="action">open_website_url</attribute>
        </xpath>

        <field name="name" position="after">
            <field name="website_url"/>
        </field>
        <xpath expr="//tree">
            <field name="is_seo_optimized"/>
            <field name="is_published"/>

            <field name="website_id" position="move"/>
        </xpath>
    </field>
</record>

<record id="slide_channel_pages_kanban_view" model="ir.ui.view">
    <field name="name">Course Pages Kanban</field>
    <field name="model">slide.channel</field>
    <field name="priority">99</field>
    <field name="mode">primary</field>
    <field name="inherit_id" ref="slide_channel_view_kanban"/>
    <field name="arch" type="xml">
        <xpath expr="//kanban" position="attributes">
            <attribute name="js_class">website_pages_kanban</attribute>
            <attribute name="type">object</attribute>
            <attribute name="action">open_website_url</attribute>
        </xpath>
        <xpath expr="//kanban" position="inside">
            <field name="website_url" invisible="1"/>
        </xpath>
        <xpath expr="//div[hasclass('o_kanban_primary_left')]" position="replace">
            <div class="col-6 text-primary" t-if="record.website_id.value" groups="website.group_multi_website">
                <i class="fa fa-globe me-1" title="Website"/>
                <field name="website_id"/>
            </div>
        </xpath>
        <xpath expr="//div[hasclass('o_kanban_card_content')]" position="after">
            <div class="border-top mt-2 pt-2">
                <field name="is_published" widget="boolean_toggle"/>
                <t t-if="record.is_published.raw_value">Published</t>
                <t t-else="">Not Published</t>
            </div>
        </xpath>
    </field>
</record>

<record id="action_slide_channel_pages_list" model="ir.actions.act_window">
    <field name="name">Course Pages</field>
    <field name="res_model">slide.channel</field>
    <field name="view_mode">tree,kanban,form</field>
    <field name="view_ids" eval="[(5, 0, 0),
        (0, 0, {'view_mode': 'tree', 'view_id': ref('slide_channel_pages_tree_view')}),
        (0, 0, {'view_mode': 'kanban', 'view_id': ref('slide_channel_pages_kanban_view')}),
    ]"/>
    <field name="context">{'create_action': 'website_slides.slide_channel_action_add'}</field>
</record>

<menuitem id="menu_slide_channel_pages"
    parent="website.menu_content"
    sequence="50"
    name="Courses"
    action="action_slide_channel_pages_list"/>

</odoo>
