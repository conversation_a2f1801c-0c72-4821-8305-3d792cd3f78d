# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_comparison
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr "134.7 x 200 x 7.2 mm"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr "308 γρ"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<i class=\"fa fa-exchange\"/> Compare"
msgstr "<i class=\"fa fa-exchange\"/> Σύγκριση"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart\"/>&amp;nbsp;Add to Cart"
msgstr "<i class=\"fa fa-shopping-cart\"/>&amp;nbsp;Προσθήκη στο καλάθι"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<span class=\"h3\">Suggested alternatives: </span>"
msgstr "<span class=\"h3\">Προτεινόμενες εναλλακτικές λύσεις: </span>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr "<strong>Τιμή:</strong>"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr "Apple"

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr "Κατηγορίες Χαρακτηριστικών"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_1
msgid "Brand"
msgstr "Μάρκα"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "Category"
msgstr "Κατηγορία"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "Όνομα Κατηγορίας"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:8
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:20
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#, python-format
msgid "Compare"
msgstr "Σύγκριση"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:51
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr "Συγκρίνετε τα Προϊόντα"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
msgid "Dimensions"
msgstr "Διαστάσεις"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "Κωδικός"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:7
#, python-format
msgid "Product"
msgstr "Είδος"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "Χαρακτηριστικό Είδους"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr "Κατηγορία Χαρακτηριστικών Είδους"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template
msgid "Product Template"
msgstr "Πρότυπο Είδους "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "Αφαίρεση"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce"
msgstr ""
"Ορίστε μια κατηγορία για να αναομαδοποιήσετε παρόμοια χαρακτηριστικά κάτω "
"από την ίδια ενότητα στη σελίδα σύγκρισης του eshop"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr "Συγκριτής Καταστήματος"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications for"
msgstr "Προδιαγραφές για"

#. module: website_sale_comparison
#: code:addons/website_sale_comparison/controllers/main.py:23
#: code:addons/website_sale_comparison/models/website_sale_comparison.py:30
#, python-format
msgid "Uncategorized"
msgstr "Μη κατηγοριοποιημένη"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:15
#, python-format
msgid "Warning"
msgstr "Προσοχή"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "Βάρος"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:15
#, python-format
msgid "You can compare max 4 products."
msgstr "Μπορείτε να συγκρίνετε μέχρι 4 είδη."

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "ή"
