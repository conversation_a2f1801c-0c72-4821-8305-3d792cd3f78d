<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Scheduler for Lead Generation -->
        <record id="ir_cron_crm_reveal_lead" model="ir.cron">
            <field name="name">Lead Generation: Leads/Opportunities Generation</field>
            <field name="model_id" ref="model_crm_reveal_rule"/>
            <field name="state">code</field>
            <field name="code">model._process_lead_generation()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>
    </data>
</odoo>
