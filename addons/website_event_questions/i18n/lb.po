# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_questions
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-27 09:12+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.registration_attendee_details_questions
msgid "<strong>Questions</strong>"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report__answer_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__answer_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_registration_search_inherit_question
msgid "Answers"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__is_individual
msgid "Ask each attendee"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_uid
msgid "Created by"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_date
msgid "Created on"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__display_name
msgid "Display Name"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_event
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report__event_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Event"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_answer
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__event_answer_id
msgid "Event Answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_type
msgid "Event Category"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question
msgid "Event Question"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question_report
msgid "Event Question Report"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__event_registration_id
msgid "Event Registration"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration_answer
msgid "Event Registration Answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_type_id
msgid "Event Type"
msgstr ""

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Exclude cancelled registrations"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__general_question_ids
msgid "General Questions"
msgstr ""

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Group By"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__id
msgid "ID"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,help:website_event_questions.field_event_question__is_individual
msgid ""
"If True, this question will be asked for every attendee of a reservation. If"
" not it will be asked only once and its value propagated to every attendees."
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer____last_update
msgid "Last Modified on"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_uid
msgid "Last Updated by"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_date
msgid "Last Updated on"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__question_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report__question_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_answer_simplified_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Question"
msgstr ""

#. module: website_event_questions
#: code:addons/website_event_questions/models/event.py:0
#, python-format
msgid "Question cannot belong to both the event category and itself."
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_type__question_ids
#: model:ir.ui.menu,name:website_event_questions.menu_report_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_form_inherit_question
msgid "Questions"
msgstr ""

#. module: website_event_questions
#: model:ir.actions.act_window,name:website_event_questions.action_event_question_report
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_graph
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_pivot
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Questions Analysis"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_type__use_questions
msgid "Questions to Attendees"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report__attendee_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Registration"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer__sequence
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__sequence
msgid "Sequence"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__title
msgid "Title"
msgstr ""
