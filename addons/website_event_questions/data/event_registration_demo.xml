<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="event_registration_0_0_registration_answer_0" model="event.registration.answer">
        <field name="value_answer_id" ref="website_event_questions.event_0_question_0_answer_0" />
        <field name="question_id" ref="website_event_questions.event_0_question_0" />
        <field name="registration_id" ref="event.event_registration_0_0" />
    </record>
    <record id="event_registration_0_0_registration_answer_1" model="event.registration.answer">
        <field name="value_text_box">Fish
Nuts</field>
        <field name="question_id" ref="website_event_questions.event_0_question_1" />
        <field name="registration_id" ref="event.event_registration_0_0" />
    </record>
    <record id="event_registration_0_0_registration_answer_2" model="event.registration.answer">
        <field name="value_answer_id" ref="website_event_questions.event_0_question_2_answer_0" />
        <field name="question_id" ref="website_event_questions.event_0_question_2" />
        <field name="registration_id" ref="event.event_registration_0_0" />
    </record>
    <record id="event_registration_0_1_registration_answer_0" model="event.registration.answer">
        <field name="value_answer_id" ref="website_event_questions.event_0_question_0_answer_1" />
        <field name="question_id" ref="website_event_questions.event_0_question_0" />
        <field name="registration_id" ref="event.event_registration_0_1" />
    </record>
    <record id="event_registration_0_1_registration_answer_1" model="event.registration.answer">
        <field name="value_answer_id" ref="website_event_questions.event_0_question_2_answer_0" />
        <field name="question_id" ref="website_event_questions.event_0_question_2" />
        <field name="registration_id" ref="event.event_registration_0_1" />
    </record>
    <record id="event_registration_0_2_registration_answer_0" model="event.registration.answer">
        <field name="value_answer_id" ref="website_event_questions.event_0_question_0_answer_2" />
        <field name="question_id" ref="website_event_questions.event_0_question_0" />
        <field name="registration_id" ref="event.event_registration_0_2" />
    </record>
    <record id="event_registration_0_2_registration_answer_1" model="event.registration.answer">
        <field name="value_answer_id" ref="website_event_questions.event_0_question_2_answer_2" />
        <field name="question_id" ref="website_event_questions.event_0_question_2" />
        <field name="registration_id" ref="event.event_registration_0_2" />
    </record>
</data>
</odoo>
