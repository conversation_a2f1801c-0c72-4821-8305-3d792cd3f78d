# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_customer
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-12-07 04:03+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> Todos"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_active
msgid "Active"
msgstr "Activo"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:68
#, python-format
msgid "All Countries"
msgstr "Todos los países"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag_classname
msgid "Bootstrap class to customize the color"
msgstr "Bootstrap class para personalizar el color"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_name
msgid "Category Name"
msgstr "Nombre de categoría"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_classname
msgid "Class"
msgstr "Clase"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Click to create a new partner tag."
msgstr "Click para crear una nueva etiqueta para la Empresa."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_uid
msgid "Created by"
msgstr "Creado por:"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_date
msgid "Created on"
msgstr "Creado"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "Implemetado por"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_uid
msgid "Last Updated by"
msgstr "Ultima Actualización por"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_date
msgid "Last Updated on"
msgstr "Actualizado en"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage the partner tags to better classify them on your website.\n"
"                    You can add the filter by tag on your website in the "
"\"Customize\" menu."
msgstr ""
"Administrar las etiquetas de los clientes para clasificar mejor en su sitio "
"web.\n"
"Puedes añadir el filtro por etiqueta en la página web a través del menú "
"\"Personalizar\"."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "Sin resultados"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.footer_custom
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "Nuestras referencias"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Partner"
msgstr "Empresa"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Etiqueta de Empresa"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ... "
msgstr ""
"Etiquetas de Empresas - Estas etiquetas se pueden utilizar en el sitio web "
"para encontrar clientes por sector, o ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_partner_ids
msgid "Partners"
msgstr "Empresas"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
msgid "References"
msgstr "Referencias"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "Referencias por País"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "Referencias por Etiquetas"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Buscar"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "Comprobado por millones alrededor del mundo"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Etiquetas del Sitio Web"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_website_tag_ids
msgid "Website tags"
msgstr "Etiquetas de Sitio Web"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Mapa mundial"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "referencia(s))"

#~ msgid "Partner Tags"
#~ msgstr "Etiquetas de Empresas"

#~ msgid "Tags"
#~ msgstr "Etiquetas"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través de la web."

#~ msgid "Visible in Website"
#~ msgstr "Visible en el sitio web"

#~ msgid "Website URL"
#~ msgstr "URL del sitio web"
