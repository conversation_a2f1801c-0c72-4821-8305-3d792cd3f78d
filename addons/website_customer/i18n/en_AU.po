# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_customer
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-08 10:10+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (Australia) (http://www.transifex.com/odoo/odoo-9/"
"language/en_AU/)\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_active
msgid "Active"
msgstr ""

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:68
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag_classname
msgid "Bootstrap class to customize the color"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_name
msgid "Category Name"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_classname
msgid "Class"
msgstr ""

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Click to create a new partner tag."
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_uid
msgid "Created by"
msgstr "Created by"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_date
msgid "Created on"
msgstr "Created on"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage the partner tags to better classify them on your website.\n"
"                    You can add the filter by tag on your website in the "
"\"Customize\" menu."
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.footer_custom
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr ""

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Partner"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr ""

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ... "
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_partner_ids
msgid "Partners"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
msgid "References"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Search"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr ""

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_website_tag_ids
msgid "Website tags"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr ""

#~ msgid "The full URL to access the document through the website."
#~ msgstr "The full URL to access the document through the website."

#~ msgid "Visible in Website"
#~ msgstr "Visible in Website"

#~ msgid "Website URL"
#~ msgstr "Website URL"
