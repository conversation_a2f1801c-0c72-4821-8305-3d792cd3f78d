# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_tour
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:485
#, python-format
msgid "Click the <i>Home icon</i> to navigate across apps."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_user_id
msgid "Consumed by"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:8
#, python-format
msgid "Disable Tours"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Meni"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:21
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:22
#, python-format
msgid "Path"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:198
#, python-format
msgid "Skip tour"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:11
#, python-format
msgid "Start Tour"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_name
msgid "Tour name"
msgstr ""

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr ""
