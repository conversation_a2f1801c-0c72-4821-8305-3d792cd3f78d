<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="tracks_display_list" inherit_id="website_event_track.tracks_display_list">
    <!-- TRACK LIST: ADD REPLAY TAG FOR FINISHED TRACKS -->
    <xpath expr="//div[@t-foreach='tracks']//div[hasclass('col-md-7')]//span[@t-elif='not track.is_track_done and not track.is_track_soon']" position="after">
        <a t-elif="track.youtube_video_url and (track.is_published or is_event_user)"
            t-att-href="track.website_url" class="badge text-bg-danger ms-2">Replay
        </a>
    </xpath>
    <!-- ADD YOUTUBE ICON -->
    <xpath expr="//div[@t-foreach='tracks']//div[hasclass('col-md-7')]//a/span[@t-field='track.name']" position="before">
        <i t-if="track.date and track.youtube_video_url and (track.is_track_soon or track.is_track_live or track.is_youtube_replay)"
            class="fa fa-youtube-play text-danger me-1"/>
    </xpath>
    <xpath expr="//div[@t-foreach='tracks']//div[hasclass('col-md-7')]//t/span[@t-field='track.name']" position="before">
        <i t-if="track.date and track.youtube_video_url and (track.is_track_soon or track.is_track_live or track.is_youtube_replay)"
            class="fa fa-youtube-play text-danger me-1"/>
    </xpath>
</template>

</odoo>
