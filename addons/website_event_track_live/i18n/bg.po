# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_live
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_content
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch position-relative\"/>\n"
"                <span class=\"ps-2\">Loading Video...</span>"
msgstr ""

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_aside
msgid "Chat"
msgstr "Чат"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__is_youtube_replay
msgid ""
"Check this option if the video is already available on YouTube to avoid "
"showing 'Direct' options (Chat, ...)"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_url
msgid ""
"Configure this URL so that event attendees can see your Track in video!"
msgstr ""

#. module: website_event_track_live
#: model:ir.model,name:website_event_track_live.model_event_track
msgid "Event Track"
msgstr "Направление на събитие"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_id
msgid ""
"Extracted from the video URL and used to infer various links "
"(embed/thumbnail/...)"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_chat_available
msgid "Is Chat Available"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_replay
msgid "Is YouTube Replay"
msgstr ""

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.tracks_display_list
msgid "Replay"
msgstr ""

#. module: website_event_track_live
#. odoo-javascript
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Replay Video"
msgstr ""

#. module: website_event_track_live
#. odoo-javascript
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Starts in"
msgstr ""

#. module: website_event_track_live
#. odoo-javascript
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Up Next:"
msgstr ""

#. module: website_event_track_live
#. odoo-javascript
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "You just watched:"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_url
msgid "YouTube Video Link"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_id
msgid "YouTube video ID"
msgstr ""
