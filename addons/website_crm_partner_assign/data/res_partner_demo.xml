<?xml version="1.0" encoding="UTF-8"?>
<odoo>
        <record id="base.res_partner_3" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_bronze"/>
            <field name="partner_weight">10</field>
        </record>
        <record model="res.partner" id="base.res_partner_2">
            <field name="assigned_partner_id" ref="base.res_partner_3"/>
        </record>
       <record id="base.res_partner_4" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_bronze"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_bronze"/>
            <field name="partner_weight">10</field>
        </record>

        <record id="base.res_partner_3" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_silver"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_silver"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_silver"/>
            <field name="partner_weight">10</field>
        </record>

        <record id="base.res_partner_10" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_gold"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_18" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_gold"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_1" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_data_gold"/>
            <field name="partner_weight">10</field>
        </record>

        <record model="res.partner" id="base.res_partner_10">
            <field name="assigned_partner_id" ref="base.res_partner_4"/>
        </record>
        <record model="res.partner" id="base.res_partner_12">
            <field name="assigned_partner_id" ref="base.res_partner_10"/>
        </record>
        <record model="res.partner" id="base.res_partner_4">
            <field name="assigned_partner_id" ref="base.res_partner_4"/>
        </record>
        <record model="res.partner" id="base.res_partner_10">
            <field name="assigned_partner_id" ref="base.res_partner_12"/>
        </record>
        <record model="res.partner" id="base.res_partner_3">
            <field name="assigned_partner_id" ref="base.res_partner_3"/>
        </record>
        <record model="res.partner" id="base.res_partner_2">
            <field name="assigned_partner_id" ref="base.res_partner_1"/>
        </record>
        <record model="res.partner" id="base.res_partner_4">
            <field name="assigned_partner_id" ref="base.res_partner_18"/>
        </record>
        <record model="res.partner" id="base.res_partner_4">
            <field name="assigned_partner_id" ref="base.res_partner_3"/>
        </record>
        <record model="res.partner" id="base.res_partner_1">
            <field name="assigned_partner_id" ref="base.res_partner_12"/>
        </record>
        <record model="res.partner" id="base.res_partner_1">
            <field name="assigned_partner_id" ref="base.res_partner_2"/>
        </record>
        <record model="res.partner" id="base.res_partner_1">
            <field name="assigned_partner_id" ref="base.res_partner_2"/>
        </record>
        <record model="res.partner" id="base.res_partner_12">
            <field name="assigned_partner_id" ref="base.res_partner_12"/>
        </record>
</odoo>
