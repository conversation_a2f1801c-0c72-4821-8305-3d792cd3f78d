# #-#-#-#-#  en_GB.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm_partner_assign
#
# Translators:
# #-#-#-#-#  en_GB.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm_partner_assign
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-16 17:56+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  en_GB.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  en_GB.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:60
#, python-format
msgid "All Categories"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:79
#, python-format
msgid "All Countries"
msgstr "All Countries"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Contact a reseller"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Country"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Level"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Looking For a Local Store?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No result found"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Our Partners"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.grade_in_detail
msgid "Partner"
msgstr "Partner"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Partners"
msgstr "Partners"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr "Reference"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.footer_custom
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "Resellers"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Search"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "World Map"
msgstr "World Map"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "pull-left"
msgstr "pull-left"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference(s)"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "res.partner.grade"
msgstr ""

#~ msgid "Active"
#~ msgstr "Active"

#~ msgid "Automatically sanitized HTML contents"
#~ msgstr "Automatically sanitised HTML contents"

#~ msgid "Cancel"
#~ msgstr "Cancel"

#~ msgid "Comment"
#~ msgstr "Comment"

#~ msgid "Communication"
#~ msgstr "Communication"

#~ msgid "Company"
#~ msgstr "Company"

#~ msgid "Confirm"
#~ msgstr "Confirm"

#~ msgid "Contact"
#~ msgstr "Contact"

#~ msgid "Country"
#~ msgstr "Country"

#~ msgid "Create Date"
#~ msgstr "Create Date"

#~ msgid "Created by"
#~ msgstr "Created by"

#~ msgid "Created on"
#~ msgstr "Created on"

#~ msgid "Customer"
#~ msgstr "Customer"

#~ msgid "Details"
#~ msgstr "Details"

#~ msgid "Display Name"
#~ msgstr "Display Name"

#~ msgid "Email Template"
#~ msgstr "Email Template"

#~ msgid "Expected Revenues"
#~ msgstr "Expected Turnover"

#~ msgid "Extended Filters..."
#~ msgstr "Extended Filters..."

#~ msgid "Geo Latitude"
#~ msgstr "Geo Latitude"

#~ msgid "Geo Longitude"
#~ msgstr "Geo Longitude"

#~ msgid "Group By"
#~ msgstr "Group By"

#~ msgid "High"
#~ msgstr "High"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Last Modified on"

#~ msgid "Last Updated by"
#~ msgstr "Last Updated by"

#~ msgid "Last Updated on"
#~ msgstr "Last Updated on"

#~ msgid "Lead"
#~ msgstr "Lead"

#~ msgid "Lead/Opportunity"
#~ msgstr "Lead/Opportunity"

#~ msgid "Leads"
#~ msgstr "Lead"

#~ msgid "Leads & Opportunities"
#~ msgstr "Leads & Opportunities"

#~ msgid "Level"
#~ msgstr "Level"

#~ msgid "Low"
#~ msgstr "Low"

#~ msgid "Name"
#~ msgstr "Name"

#~ msgid "Normal"
#~ msgstr "Normal"

#~ msgid "Opportunities"
#~ msgstr "Opportunities"

#~ msgid "Priority"
#~ msgstr "Priority"

#~ msgid "Salesperson"
#~ msgstr "Salesperson"

#~ msgid "Sequence"
#~ msgstr "Sequence"

#~ msgid "Subject"
#~ msgstr "Subject"

#~ msgid "Type"
#~ msgstr "Type"

#~ msgid "User"
#~ msgstr "User"
