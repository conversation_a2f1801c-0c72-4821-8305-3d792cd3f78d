# #-#-#-#-#  es_BO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm_partner_assign
#
# Translators:
# #-#-#-#-#  es_BO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm_partner_assign
#
# Translators:
# <AUTHOR> <EMAIL>, 2015
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-12-11 15:37+0000\n"
"Last-Translator: phoenixsampras <<EMAIL>>\n"
"Language-Team: Spanish (Bolivia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_BO/)\n"
"Language: es_BO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  es_BO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  es_BO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:60
#, python-format
msgid "All Categories"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:79
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Contact a reseller"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Country"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Level"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Looking For a Local Store?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No result found"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Our Partners"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.grade_in_detail
msgid "Partner"
msgstr "Empresa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Partners"
msgstr "Empresas"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.footer_custom
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "Resellers"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Buscar"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "World Map"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "pull-left"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference(s)"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "res.partner.grade"
msgstr ""

#~ msgid "Active"
#~ msgstr "Activo"

#~ msgid "Cancel"
#~ msgstr "Cancelar"

#~ msgid "Comment"
#~ msgstr "Comentario"

#~ msgid "Communication"
#~ msgstr "Comunicación"

#~ msgid "Company"
#~ msgstr "Compañía"

#~ msgid "Confirm"
#~ msgstr "Confirmar"

#~ msgid "Contact"
#~ msgstr "Contacto"

#~ msgid "Create Date"
#~ msgstr "Fecha de creación"

#~ msgid "Created by"
#~ msgstr "Creado por"

#~ msgid "Created on"
#~ msgstr "Creado en"

#~ msgid "Customer"
#~ msgstr "Cliente"

#~ msgid "Extended Filters..."
#~ msgstr "Filtros extendidos..."

#~ msgid "Group By"
#~ msgstr "Agrupar por"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Updated by"
#~ msgstr "Última actualización de"

#~ msgid "Last Updated on"
#~ msgstr "Última actualización en"

#~ msgid "Level"
#~ msgstr "Nivel"

#~ msgid "Name"
#~ msgstr "Nombre"

#~ msgid "Normal"
#~ msgstr "Normal"

#~ msgid "Opportunity"
#~ msgstr "Oportunidades"

#~ msgid "Partner Name"
#~ msgstr "Nombre de la empresa"

#~ msgid "Priority"
#~ msgstr "Prioridad"

#~ msgid "Salesperson"
#~ msgstr "Vendedor"

#~ msgid "Sequence"
#~ msgstr "Secuencia"

#~ msgid "Type"
#~ msgstr "Tipo"

#~ msgid "User"
#~ msgstr "Usuario"
