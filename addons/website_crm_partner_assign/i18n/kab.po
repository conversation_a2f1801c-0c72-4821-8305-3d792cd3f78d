# #-#-#-#-#  kab.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm_partner_assign
#
# Translators:
# #-#-#-#-#  kab.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm_partner_assign
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-14 09:06+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Kabyle (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  kab.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  kab.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:60
#, python-format
msgid "All Categories"
msgstr "Tiggayin imaṛṛa"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:79
#, python-format
msgid "All Countries"
msgstr "Timura imaṛṛa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Contact a reseller"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Country"
msgstr "Zizdeg s tmurt"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Level"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Looking For a Local Store?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No result found"
msgstr "Ulac agmuḍ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Our Partners"
msgstr "Imendiden-neɣ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.grade_in_detail
msgid "Partner"
msgstr "Amendid"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Partners"
msgstr "Imendiden"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr "Timsisɣal"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.footer_custom
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "Resellers"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Nadi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "World Map"
msgstr "Takarḍa n umaḍal"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "pull-left"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference(s)"
msgstr "Tamsisɣlt(Timsisɣal )"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "res.partner.grade"
msgstr "res.partner.grade"

#~ msgid "Website URL"
#~ msgstr "URL n usmel WEB"

#~ msgid "# of Opportunity"
#~ msgstr "# n tegwnatin"

#~ msgid "Activation"
#~ msgstr "Armed"

#~ msgid "Active"
#~ msgstr "Urmid"

#~ msgid "CRM Lead Report"
#~ msgstr "Assaɣ n tfulɣalt n CRM"

#~ msgid "Cancel"
#~ msgstr "Sefsex"

#~ msgid "Categorization"
#~ msgstr "Tuggayt"

#~ msgid "Close Date"
#~ msgstr "Azemz n umdel"

#~ msgid "Comment"
#~ msgstr "Awennit"

#~ msgid "Communication"
#~ msgstr "Taywalt"

#~ msgid "Company"
#~ msgstr "Takebbwanit"

#~ msgid "Confirm"
#~ msgstr "Sentem"

#~ msgid "Contact"
#~ msgstr "Anermis"

#~ msgid "Contents"
#~ msgstr "Igburen"

#~ msgid "Country"
#~ msgstr "Tamurt"

#~ msgid "Create Date"
#~ msgstr "Azemz n tmerna"

#~ msgid "Created by"
#~ msgstr "Yerna-t"

#~ msgid "Created on"
#~ msgstr "Yerna di"

#~ msgid "Customer"
#~ msgstr "Amsaɣ"

#~ msgid "Customer Name"
#~ msgstr "Isem n umsaɣ"

#~ msgid "Delay to Close"
#~ msgstr "Imenḍaṛ i wemdel"

#~ msgid "Email Template"
#~ msgstr "Taneɣruft n Imayl"

#~ msgid "Extended Filters..."
#~ msgstr "Imzizdigen leqqayen..."

#~ msgid "Geo Latitude"
#~ msgstr "Tarrut n tirakalt"

#~ msgid "Geo Longitude"
#~ msgstr "Tazegrart n  tirakalt"

#~ msgid "Group By"
#~ msgstr "Sdukel s"

#~ msgid "ID"
#~ msgstr "Asulay"

#~ msgid "Last Modified on"
#~ msgstr "Aleqqem aneggaru di"

#~ msgid "Last Updated by"
#~ msgstr "Aleqqem aneggaru sɣuṛ"

#~ msgid "Last Updated on"
#~ msgstr "Aleqqem aneggaru di"

#~ msgid "Lead"
#~ msgstr "Tafuɣalt"

#~ msgid "Lead/Opportunity"
#~ msgstr "Tafuɣalt / Tagwniţ"

#~ msgid "Leads"
#~ msgstr "Tifuɣalin"

#~ msgid "Leads & Opportunities"
#~ msgstr "Tifuɣalin & Tegnatin"

#~ msgid "Leads Analysis"
#~ msgstr "Asefrek n tfuɣalin"

#~ msgid "Level"
#~ msgstr "Aswir"

#~ msgid "Name"
#~ msgstr "Isem"

#~ msgid "Normal"
#~ msgstr "Amagnu"

#~ msgid "Opportunities"
#~ msgstr "Tigwnatin"

#~ msgid "Opportunity"
#~ msgstr "Tagwniţ"

#~ msgid "Partner Activation"
#~ msgstr "Armed n umendid"

#~ msgid "Partner Activations"
#~ msgstr "Irmaden n umendid"

#~ msgid "Partner Name"
#~ msgstr "Isem n Umendid"

#~ msgid "Priority"
#~ msgstr "Tazwart"

#~ msgid "Sales Team"
#~ msgstr "Tarbaɛt n uzenzi"

#~ msgid "Salesperson"
#~ msgstr "Amzenzi"

#~ msgid "Send"
#~ msgstr "Azen"

#~ msgid "Send Email"
#~ msgstr "Azen Imayl"

#~ msgid "Send Mail"
#~ msgstr "Azen email"

#~ msgid "Sequence"
#~ msgstr "Agzum"

#~ msgid "Stage"
#~ msgstr "Amecwaṛ"

#~ msgid "Subject"
#~ msgstr "Amgay"

#~ msgid "Type"
#~ msgstr "Tawsit"

#~ msgid "User"
#~ msgstr "Aseqdac"

#~ msgid "res.partner.activation"
#~ msgstr "res.partner.activation"
