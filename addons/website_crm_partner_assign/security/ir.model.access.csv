id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_crm_partner_report,crm.partner.report.assign.all,model_crm_partner_report_assign,sales_team.group_sale_salesman,1,0,0,0
access_res_partner_grade,res.partner.grade,model_res_partner_grade,sales_team.group_sale_salesman,1,1,1,0
access_res_partner_grade_employee,res.partner.grade,model_res_partner_grade,base.group_user,1,0,0,0
access_res_partner_grade_portal,res.partner.grade,model_res_partner_grade,base.group_portal,1,0,0,0
access_res_partner_grade_public,res.partner.grade,model_res_partner_grade,base.group_public,1,0,0,0
access_res_partner_grade_manager,res.partner.grade.manager,model_res_partner_grade,sales_team.group_sale_manager,1,1,1,1
access_res_partner_activation_user,res.partner.activation.user,model_res_partner_activation,base.group_user,1,0,0,0
access_partner_activation_manager,res.partner.activation.manager,model_res_partner_activation,base.group_partner_manager,1,1,1,1
partner_access_crm_lead,crm.lead,crm.model_crm_lead,base.group_portal,1,1,0,0
access_res_partner_grade_invoicing_payment_readonly,res.partner.grade,model_res_partner_grade,account.group_account_readonly,1,0,0,0
access_res_partner_grade_invoicing_payment,res.partner.grade,model_res_partner_grade,account.group_account_invoice,1,0,0,0
access_crm_lead_forward_to_partner,access.crm.lead.forward.to.partner,model_crm_lead_forward_to_partner,sales_team.group_sale_salesman,1,1,1,0
access_crm_lead_assignation,access.crm.lead.assignation,model_crm_lead_assignation,sales_team.group_sale_salesman,1,1,1,0
