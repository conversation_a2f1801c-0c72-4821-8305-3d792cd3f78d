<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

        <!-- CRM Lead portal -->
        <record id="assigned_lead_portal_rule_1" model="ir.rule">
            <field name="name">Portal Graded Partner: read and write assigned leads</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="domain_force">[('partner_assigned_id','child_of',user.commercial_partner_id.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- Partner Grade : portal and public -->
        <record id="res_partner_grade_rule_portal_public" model="ir.rule">
            <field name="name">Portal/Public user: read only website published</field>
            <field name="model_id" ref="website_crm_partner_assign.model_res_partner_grade"/>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_public'))]"/>
            <field name="domain_force">[('website_published','=', True)]</field>
            <field name="perm_read" eval="True"/>
        </record>

    <record id="ir_rule_crm_partner_report_assign_all" model="ir.rule">
         <field name="name">CRM partner assign report: All Assignations</field>
         <field name="model_id" ref="model_crm_partner_report_assign"/>
         <field name="domain_force">[(1, '=', 1)]</field>
         <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman_all_leads'))]"/>
     </record>

     <record id="ir_rule_crm_partner_report_assign_salesman" model="ir.rule">
         <field name="name">CRM partner assign report: Personal / Global Assignations</field>
         <field name="model_id" ref="model_crm_partner_report_assign"/>
         <field name="domain_force">['|', ('user_id', '=', user.id), ('user_id', '=', False)]</field>
         <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
     </record>

</odoo>
