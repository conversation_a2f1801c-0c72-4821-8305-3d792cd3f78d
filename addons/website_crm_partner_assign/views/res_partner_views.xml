<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_res_partner_filter_assign_tree" model="ir.ui.view">
        <field name="name">res.partner.geo.inherit.tree</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_tree"/>
        <field name="arch" type="xml">
            <field name="vat" position="after">
                <field name="date_review_next" optional="hide"/>
                <field name="grade_id" optional="hide"/>
                <field name="activation" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_res_partner_filter_assign" model="ir.ui.view">
        <field name="name">res.partner.geo.inherit.search</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="grade_id"/>
            </field>
        </field>
    </record>

    <record id="view_crm_partner_assign_form" model="ir.ui.view">
        <field name="name">res.partner.assign.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base_geolocalize.view_crm_partner_geo_form"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//page[@name='geo_location']/group" position="before">
                    <group name="group_partner_activation_review">
                        <group>
                            <separator string="Partner Activation" colspan="2"/>
                            <field name="grade_id" options="{'no_open': True}"/>
                            <field name="activation" options="{'no_open': True}"/>
                            <field name="partner_weight"/>
                        </group>
                        <group>
                            <separator string="Partner Review" colspan="2"/>
                            <field name="date_review"/>
                            <field name="date_review_next"/>
                            <field name="date_partnership"/>
                        </group>
                    </group>
                </xpath>
                <xpath expr="//group[@name='sale']" position="inside">
                    <field name="assigned_partner_id" groups="base.group_no_one"/>
                </xpath>
            </data>
        </field>
    </record>
</odoo>
