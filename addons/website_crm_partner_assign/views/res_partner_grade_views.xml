<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_partner_grade_tree" model="ir.ui.view">
        <field name="name">res.partner.grade.tree</field>
        <field name="model">res.partner.grade</field>
        <field name="arch" type="xml">
            <tree string="Partner Level">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="view_partner_grade_form" model="ir.ui.view">
        <field name="name">res.partner.grade.form</field>
        <field name="model">res.partner.grade</field>
        <field name="arch" type="xml">
            <form string="Partner Level">
                <sheet string="Level">
                    <div class="oe_button_box" name="button_box">
                        <field name="is_published" widget="website_redirect_button"/>
                    </div>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" placeholder="e.g. Gold Partner" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <field name="partner_weight"/>
                        <field name="sequence"/>
                        <field name="active" widget="boolean_toggle"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="res_partner_grade_view_search" model="ir.ui.view">
        <field name="name">res.partner.grade.view.search</field>
        <field name="model">res.partner.grade</field>
        <field name="arch" type="xml">
            <search string="Search Partner Grade">
                <field name="name"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="res_partner_grade_action" model="ir.actions.act_window">
        <field name="name">Partner Levels</field>
        <field name="res_model">res.partner.grade</field>
        <field name="search_view_id" ref="res_partner_grade_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Partner Level
            </p><p>
                Partner Levels allow you to rank your Partners based on their performances.
            </p>
        </field>
    </record>
</odoo>
