# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_editor
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>le <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:06+0000\n"
"PO-Revision-Date: 2018-10-02 10:06+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:35
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:44
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:36
#, python-format
msgid "(ALT Tag)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:45
#, python-format
msgid "(TITLE Tag)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:195
#, python-format
msgid "(URL or Embed)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1028
#, python-format
msgid "(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<i class=\"fa fa-th-large\"/> First Panel"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:574
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Accepts"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1078
#, python-format
msgid "Action"
msgstr "Akcija"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:112
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:108
#, python-format
msgid "Add an image URL"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Add blocks"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1063
#, python-format
msgid "Align center"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1062
#, python-format
msgid "Align left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1064
#, python-format
msgid "Align right"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:101
#, python-format
msgid "Alternate Upload"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:331
#, python-format
msgid "Aspect Ratio"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:370
#, python-format
msgid "Assign a focal point that will always be visible"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "Zakačka"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "URL Zakačke"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:197
#, python-format
msgid "Attention"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:206
#, python-format
msgid "Autoplay"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1069
#: code:addons/web_editor/static/src/xml/snippets.xml:28
#, python-format
msgid "Background Color"
msgstr "Pozadinska boja"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:951
#, python-format
msgid "Background Image Sizing"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:411
#, python-format
msgid "Background height"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:423
#, python-format
msgid "Background position"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:449
#, python-format
msgid "Background repeat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:397
#, python-format
msgid "Background size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:400
#, python-format
msgid "Background width"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:255
#, python-format
msgid "Block"
msgstr "Blokiraj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:989
#, python-format
msgid "Bold"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:833
#, python-format
msgid "Careful !"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:136
#, python-format
msgid "Center"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:81
#, python-format
msgid "Change media description and tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:197
#, python-format
msgid "Checked"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1076
#: code:addons/web_editor/static/src/js/editor/translator.js:56
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1630
#: code:addons/web_editor/static/src/xml/ace.xml:36
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1040
#, python-format
msgid "Code"
msgstr "Šifra"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1056
#, python-format
msgid "Code View"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:278
#, python-format
msgid "Color"
msgstr "Boja"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:253
#, python-format
msgid "Column"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:82
#, python-format
msgid "Common colors"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:365
#, python-format
msgid "Contain"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:7
#, python-format
msgid "Content to translate"
msgstr "Sadržaj za prevođenje"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:196
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:364
#, python-format
msgid "Cover"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:201
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1628
#, python-format
msgid "Crop Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:366
#, python-format
msgid "Custom"
msgstr "Prilagođeno"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1068
#, python-format
msgid "Custom Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:15
#, python-format
msgid "Customize"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Dailymotion"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/summernote.js:2021
#: code:addons/web_editor/static/src/xml/editor.xml:269
#, python-format
msgid "Default"
msgstr "Uobičajeno"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:450
#, python-format
msgid "Define if/how the background image will be repeated"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:211
#: code:addons/web_editor/static/src/xml/editor.xml:35
#, python-format
msgid "Description"
msgstr "Opis"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:955
#: code:addons/web_editor/static/src/js/widgets/widgets.js:38
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1637
#: code:addons/web_editor/static/src/xml/editor.xml:12
#: code:addons/web_editor/static/src/xml/editor.xml:138
#, python-format
msgid "Discard"
msgstr "Odbaci"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:57
#, python-format
msgid "Document"
msgstr "Dokument"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1080
#, python-format
msgid "Document Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:966
#, python-format
msgid "Double-click to edit"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1009
#, python-format
msgid "Drag an image here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:18
#, python-format
msgid "Drag to Move"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:19
#, python-format
msgid "Duplicate Container"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:192
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1018
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:97
#, python-format
msgid "Expected "
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1001
#, python-format
msgid "File / Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:273
#, python-format
msgid "Flat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:348
#, python-format
msgid "Flip Horizontal"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:349
#, python-format
msgid "Flip Vertical"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1006
#, python-format
msgid "Float Left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1008
#, python-format
msgid "Float None"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1007
#, python-format
msgid "Float Right"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1070
#, python-format
msgid "Font Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:997
#, python-format
msgid "Font Family"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:998
#, python-format
msgid "Font Size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:43
#, python-format
msgid "Format"
msgstr "Format"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1614
#, python-format
msgid "Free"
msgstr "Slobodno"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1055
#, python-format
msgid "Full Screen"
msgstr "Cijeli ekran"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:21
#, python-format
msgid "Fullscreen"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1042
#, python-format
msgid "Header 1"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1043
#, python-format
msgid "Header 2"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1044
#, python-format
msgid "Header 3"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1045
#, python-format
msgid "Header 4"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1046
#, python-format
msgid "Header 5"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1047
#, python-format
msgid "Header 6"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1054
#, python-format
msgid "Help"
msgstr "Pomoć"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:5
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:221
#, python-format
msgid "Hide Dailymotion logo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:218
#, python-format
msgid "Hide Youtube logo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:215
#, python-format
msgid "Hide fullscreen button"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:212
#, python-format
msgid "Hide player controls"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:224
#, python-format
msgid "Hide sharing button"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:426
#, python-format
msgid "Horizontal"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/editor.js:126
#, python-format
msgid ""
"If you discard the current edition, all unsaved changes will be lost. You "
"can cancel to return to the edition mode."
msgstr ""
"Ako otkažete trenutno uređivanje, sve nesačuvane promjene će biti "
"izgubljene. Možete otkazati da se vratite u mod uređivanja."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:832
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:322
#, python-format
msgid ""
"If you want to crop it, please first download it from the original source "
"and upload it in Odoo."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:56
#, python-format
msgid "Image"
msgstr "Slika"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1011
#, python-format
msgid "Image URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:10
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:31
#, python-format
msgid "Include All SCSS Files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:22
#, python-format
msgid "Include Asset Bundles"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1061
#, python-format
msgid "Indent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1034
#, python-format
msgid "Insert Horizontal Rule"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1002
#, python-format
msgid "Insert Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1016
#, python-format
msgid "Insert Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1026
#, python-format
msgid "Insert Video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Instagram"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:1064
#, python-format
msgid "Install"
msgstr "Instalacija"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:990
#, python-format
msgid "Italic"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1065
#, python-format
msgid "Justify full"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1075
#, python-format
msgid "Keyboard shortcuts"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:150
#: code:addons/web_editor/static/src/xml/editor.xml:261
#, python-format
msgid "Large"
msgstr "Veliko"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:996
#, python-format
msgid "Line Height"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1015
#, python-format
msgid "Link"
msgstr "Veza"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:244
#, python-format
msgid "Link Label"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1346
#, python-format
msgid "Link to"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:209
#, python-format
msgid "Loop"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:149
#: code:addons/web_editor/static/src/xml/editor.xml:260
#, python-format
msgid "Medium"
msgstr "Medijum"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "Naziv:"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:69
#, python-format
msgid "Next"
msgstr "Slijedeće"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:452
#, python-format
msgid "No repeat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:147
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1072
#: code:addons/web_editor/static/src/xml/snippets.xml:30
#, python-format
msgid "None"
msgstr "Ništa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1038
#, python-format
msgid "Normal"
msgstr "Normalan"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:954
#: code:addons/web_editor/static/src/js/editor/translator.js:98
#, python-format
msgid "Ok"
msgstr "U redu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/translator.js:97
#, python-format
msgid "Ok, never show me this again"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1021
#: code:addons/web_editor/static/src/xml/editor.xml:299
#, python-format
msgid "Open in new window"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:203
#, python-format
msgid "Options"
msgstr "Opcije"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1051
#, python-format
msgid "Ordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1060
#, python-format
msgid "Outdent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:270
#, python-format
msgid "Outline"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:272
#, python-format
msgid "Outline-Rounded"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:154
#, python-format
msgid "Padding"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1059
#, python-format
msgid "Paragraph"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1079
#, python-format
msgid "Paragraph formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:58
#, python-format
msgid "Pictogram"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:230
#: code:addons/web_editor/static/src/xml/editor.xml:307
#: code:addons/web_editor/static/src/xml/editor.xml:309
#, python-format
msgid "Preview"
msgstr "Pregled"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:68
#, python-format
msgid "Previous"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1039
#, python-format
msgid "Quote"
msgstr "Ponuda"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monerary"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.js:326
#, python-format
msgid "Readonly field"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1084
#, python-format
msgid "Redo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:196
#, python-format
msgid "Remove"
msgstr "Ukloni"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:20
#, python-format
msgid "Remove Block"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:995
#, python-format
msgid "Remove Font Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1012
#, python-format
msgid "Remove Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:384
#, python-format
msgid "Repeat"
msgstr "Ponavljaj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:453
#, python-format
msgid "Repeat both"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:454
#, python-format
msgid "Repeat x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:455
#, python-format
msgid "Repeat y"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:42
#, python-format
msgid "Reset"
msgstr "Resetuj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:352
#, python-format
msgid "Reset Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:490
#, python-format
msgid "Reseting views is not supported yet"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1003
#, python-format
msgid "Resize Full"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1004
#, python-format
msgid "Resize Half"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1005
#, python-format
msgid "Resize Quarter"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:344
#, python-format
msgid "Rotate Left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:345
#, python-format
msgid "Rotate Right"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:271
#, python-format
msgid "Rounded"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:11
#, python-format
msgid "SCSS (CSS)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:364
#, python-format
msgid "SCSS file: %s"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:37
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1633
#: code:addons/web_editor/static/src/xml/ace.xml:35
#: code:addons/web_editor/static/src/xml/editor.xml:13
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:63
#: code:addons/web_editor/static/src/xml/snippets.xml:44
#, python-format
msgid "Search"
msgstr "Pretraži"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:42
#, python-format
msgid "Search Contact"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:13
#, python-format
msgid "Select Parent Container"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1123
#, python-format
msgid "Select a Media"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:130
#, python-format
msgid "Select a Picture"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1010
#, python-format
msgid "Select from files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:572
#, python-format
msgid "Server error"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:424
#, python-format
msgid "Set the starting position of the background image."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:398
#, python-format
msgid ""
"Sets the width and height of the background image in percent of the parent "
"element."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:161
#, python-format
msgid "Shadow"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:256
#, python-format
msgid "Size"
msgstr "Veličina"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:148
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1041
#: code:addons/web_editor/static/src/xml/editor.xml:259
#, python-format
msgid "Small"
msgstr "Malo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:169
#, python-format
msgid "Spin"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:992
#, python-format
msgid "Strikethrough"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1037
#: code:addons/web_editor/static/src/xml/editor.xml:266
#, python-format
msgid "Style"
msgstr "Stil"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:993
#, python-format
msgid "Subscript"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:994
#, python-format
msgid "Superscript"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1031
#, python-format
msgid "Table"
msgstr "Sto"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:362
#, python-format
msgid "Template ID: %s"
msgstr "Predložak ID: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1077
#, python-format
msgid "Text formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1019
#, python-format
msgid "Text to display"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:157
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1056
#, python-format
msgid "The provided url does not reference any supported video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1050
#, python-format
msgid "The provided url is not valid"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:74
#, python-format
msgid "Theme colors"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/editor.js:86
#: code:addons/web_editor/static/src/js/editor/translator.js:181
#, python-format
msgid "This document is not saved!"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:148
#, python-format
msgid "This file is a public view attachment"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:149
#, python-format
msgid "This file is attached to the current record"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:320
#, python-format
msgid "This image is an external image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:321
#, python-format
msgid "This type of image is not supported for cropping."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1020
#, python-format
msgid "To what URL should this link go?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:44
#, python-format
msgid "Tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:12
#, python-format
msgid "Translate"
msgstr "Prevedi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/translator.js:54
#, python-format
msgid "Translate Attribute"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:8
#, python-format
msgid "Translated content"
msgstr "Prevedeni sadržaj"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "Translation"
msgstr "Prevod"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/translator.js:95
#, python-format
msgid "Translation Info"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1071
#, python-format
msgid "Transparent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:250
#, python-format
msgid "URL or Email"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:991
#, python-format
msgid "Underline"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1083
#, python-format
msgid "Undo"
msgstr "Vrati"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:90
#, python-format
msgid "Unexpected "
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1017
#, python-format
msgid "Unlink"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1050
#, python-format
msgid "Unordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:99
#, python-format
msgid "Upload an image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:104
#, python-format
msgid "Upload image without optimization"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:437
#, python-format
msgid "Vertical"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1024
#: code:addons/web_editor/static/src/xml/editor.xml:59
#, python-format
msgid "Video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1025
#, python-format
msgid "Video Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:1027
#, python-format
msgid "Video URL?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:195
#, python-format
msgid "Video code"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "Pregled"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Vimeo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Vine.co"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:8
#: code:addons/web_editor/static/src/xml/ace.xml:10
#, python-format
msgid "XML (HTML)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:151
#, python-format
msgid "Xl"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:4
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Ulazite u mod za prevođenje."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Youku"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "Youtube"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:340
#, python-format
msgid "Zoom In"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:341
#, python-format
msgid "Zoom Out"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "and"
msgstr "i"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:403
#: code:addons/web_editor/static/src/xml/editor.xml:414
#: code:addons/web_editor/static/src/xml/editor.xml:429
#: code:addons/web_editor/static/src/xml/editor.xml:440
#, python-format
msgid "auto"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:110
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:200
#, python-format
msgid "videos"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:107
#, python-format
msgid "— or —"
msgstr ""
