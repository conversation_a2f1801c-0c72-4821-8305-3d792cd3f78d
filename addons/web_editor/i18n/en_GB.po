# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_editor
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-22 02:05+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:900
#, python-format
msgid "(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:338
#, python-format
msgid "(Youtube, Vimeo, Dailymotion)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<i class=\"fa fa-th-large\"/> First Panel"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:952
#, python-format
msgid "Action"
msgstr "Action"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:224
#, python-format
msgid "Add"
msgstr "Add"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:221
#, python-format
msgid "Add an image URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:934
#, python-format
msgid "Align center"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:933
#, python-format
msgid "Align left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:935
#, python-format
msgid "Align right"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Alpha"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:209
#, python-format
msgid "Alternate Upload"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:50
#, python-format
msgid ""
"Assigning a focal point to ensure that the targeted area will be always "
"visible"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment_local_url
msgid "Attachment URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:24
#, python-format
msgid "Auto Resize"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:359
#, python-format
msgid "Autoplay"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:941
#: code:addons/web_editor/static/src/xml/snippets.xml:49
#, python-format
msgid "Background Color"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/snippets.options.js:461
#, fuzzy, python-format
msgid "Background Image Options"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:100
#, fuzzy, python-format
msgid "Background height"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:110
#, fuzzy, python-format
msgid "Background position"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:132
#, fuzzy, python-format
msgid "Background repeat"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:88
#, fuzzy, python-format
msgid "Background size"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:91
#, fuzzy, python-format
msgid "Background width"
msgstr "Background Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:404
#, python-format
msgid "Basic"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Beta"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Black"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:8
#, python-format
msgid "Block style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:861
#, python-format
msgid "Bold"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:293
#, python-format
msgid "Cancel"
msgstr "Cancel"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:127
#, python-format
msgid "Center"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:301
#, python-format
msgid "Change media description and tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:52
#, python-format
msgid "Click on the picture"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:950
#: code:addons/web_editor/static/src/xml/translator.xml:16
#, python-format
msgid "Close"
msgstr "Close"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:912
#, python-format
msgid "Code"
msgstr "Code"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:927
#, python-format
msgid "Code View"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:396
#, python-format
msgid "Color Style"
msgstr "Colour Style"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:37
#, python-format
msgid "Contain"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:36
#, python-format
msgid "Cover"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_create_uid
msgid "Created by"
msgstr "Created by"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_create_date
msgid "Created on"
msgstr "Created on"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:38
#, fuzzy, python-format
msgid "Custom"
msgstr "Customise"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:32
#, python-format
msgid "Customize"
msgstr "Customise"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:424
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Danger"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/summernote.js:1920
#: code:addons/web_editor/static/src/xml/editor.xml:444
#, python-format
msgid "Default"
msgstr "Default"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:133
#, python-format
msgid "Define if/how the background image will be repeated"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Delta"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:136
#: code:addons/web_editor/static/src/xml/editor.xml:304
#, python-format
msgid "Description"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/snippets.options.js:465
#: code:addons/web_editor/static/src/xml/editor.xml:7
#: code:addons/web_editor/static/src/xml/editor.xml:29
#: code:addons/web_editor/static/src/xml/editor.xml:187
#: code:addons/web_editor/static/src/xml/editor.xml:250
#: code:addons/web_editor/static/src/xml/editor.xml:292
#, python-format
msgid "Discard"
msgstr "Discard"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:285
#, python-format
msgid "Discard edition"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:154
#, python-format
msgid "Document"
msgstr "Document"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:954
#, python-format
msgid "Document Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:768
#, python-format
msgid "Double-click to edit"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:881
#, python-format
msgid "Drag an image here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:35
#, python-format
msgid "Drag to Move"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:36
#, python-format
msgid "Duplicate Container"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:190
#: code:addons/web_editor/static/src/js/rte.summernote.js:890
#, python-format
msgid "Edit"
msgstr "Edit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:345
#, python-format
msgid "Embed Video (HTML)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:44
#, python-format
msgid "English"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:43
#, python-format
msgid "English (edit mode)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Epsilon"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:436
#, python-format
msgid "Extra Small"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:878
#, python-format
msgid "Float Left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:880
#, python-format
msgid "Float None"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:879
#, python-format
msgid "Float Right"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:942
#, fuzzy, python-format
msgid "Font Color"
msgstr "Recent Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:869
#, python-format
msgid "Font Family"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:870
#, python-format
msgid "Font Size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:926
#, python-format
msgid "Full Screen"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gamma"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Dark"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Darker"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Light"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Lighter"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:913
#, python-format
msgid "Header 1"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:914
#, python-format
msgid "Header 2"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:915
#, python-format
msgid "Header 3"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:916
#, python-format
msgid "Header 4"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:917
#, python-format
msgid "Header 5"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:918
#, python-format
msgid "Header 6"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:925
#, python-format
msgid "Help"
msgstr "Help"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:113
#, python-format
msgid "Horizontal"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:288
#, python-format
msgid "If you discard the current edition,"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:153
#, python-format
msgid "Image"
msgstr "Image"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:883
#, python-format
msgid "Image URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:932
#, python-format
msgid "Indent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:416
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Info"
msgstr "Information"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:906
#, python-format
msgid "Insert Horizontal Rule"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:874
#, python-format
msgid "Insert Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:888
#, python-format
msgid "Insert Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:898
#, python-format
msgid "Insert Video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:862
#, python-format
msgid "Italic"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:936
#, python-format
msgid "Justify full"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:949
#, python-format
msgid "Keyboard shortcuts"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:145
#: code:addons/web_editor/static/src/xml/editor.xml:448
#, python-format
msgid "Large"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:868
#, python-format
msgid "Line Height"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:887
#: code:addons/web_editor/static/src/xml/editor.xml:400
#, python-format
msgid "Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:371
#, python-format
msgid "Link Label"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:366
#, python-format
msgid "Link to"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:144
#, python-format
msgid "Medium"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:940
#, python-format
msgid "More Color"
msgstr "More Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:163
#, python-format
msgid "Next →"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:135
#, python-format
msgid "No repeat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:142
#: code:addons/web_editor/static/src/xml/snippets.xml:51
#, python-format
msgid "None"
msgstr "None"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:910
#, python-format
msgid "Normal"
msgstr "Normal"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.layout
msgid "Odoo Editor layout"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/snippets.options.js:464
#, python-format
msgid "Ok"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:893
#: code:addons/web_editor/static/src/xml/editor.xml:381
#, python-format
msgid "Open in new window"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:922
#, python-format
msgid "Ordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:931
#, python-format
msgid "Outdent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:149
#, python-format
msgid "Padding"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:930
#, python-format
msgid "Paragraph"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:953
#, python-format
msgid "Paragraph formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:155
#, python-format
msgid "Pictogram"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:873
#, python-format
msgid "Picture"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:341
#: code:addons/web_editor/static/src/xml/editor.xml:347
#: code:addons/web_editor/static/src/xml/editor.xml:458
#, python-format
msgid "Preview"
msgstr "Preview"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:412
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Primary"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:911
#, python-format
msgid "Quote"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.js:343
#, python-format
msgid "Readonly field"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:939
#, python-format
msgid "Recent Color"
msgstr "Recent Colour"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:958
#, python-format
msgid "Redo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:194
#, python-format
msgid "Remove"
msgstr "Remove"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:37
#, python-format
msgid "Remove Block"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:867
#, python-format
msgid "Remove Font Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:884
#, python-format
msgid "Remove Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:75
#, python-format
msgid "Repeat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:136
#, python-format
msgid "Repeat both"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:137
#, python-format
msgid "Repeat x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:138
#, python-format
msgid "Repeat y"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:945
#, python-format
msgid "Reset"
msgstr "Reset"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:18
#, python-format
msgid "Reset from source"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:946
#, python-format
msgid "Reset to default"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:25
#, python-format
msgid "Resize"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:875
#, python-format
msgid "Resize Full"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:876
#, python-format
msgid "Resize Half"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:877
#, python-format
msgid "Resize Quarter"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:8
#: code:addons/web_editor/static/src/xml/editor.xml:26
#: code:addons/web_editor/static/src/xml/editor.xml:185
#, python-format
msgid "Save"
msgstr "Save"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:47
#, python-format
msgid ""
"Scale the image to be as large as it needs to fill the whole content area. "
"Part of the image may not be visible."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:70
#, python-format
msgid ""
"Scale the image to the largest size such that both its width and height can "
"fit inside the content area"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:66
#, python-format
msgid "Search"
msgstr "Search"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:64
#, python-format
msgid "Search Contact"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:30
#, python-format
msgid "Select Container Block"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:149
#, python-format
msgid "Select a Media"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:243
#, python-format
msgid "Select a Picture"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:882
#, python-format
msgid "Select from files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:339
#, python-format
msgid "Set a video URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:111
#, python-format
msgid "Set the starting position of the background image."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:944
#, python-format
msgid "Set transparent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:89
#, python-format
msgid ""
"Sets the width and height of the background image in percent of the parent "
"element."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:156
#, python-format
msgid "Shadow"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:432
#, python-format
msgid "Size"
msgstr "Size"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:143
#: code:addons/web_editor/static/src/xml/editor.xml:440
#, python-format
msgid "Small"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:164
#, python-format
msgid "Spin"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:864
#, python-format
msgid "Strikethrough"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:909
#: code:addons/web_editor/static/src/xml/editor.xml:392
#, python-format
msgid "Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:865
#, python-format
msgid "Subscript"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:408
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Success"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:866
#, python-format
msgid "Superscript"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:903
#, python-format
msgid "Table"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:951
#, python-format
msgid "Text formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:891
#, python-format
msgid "Text to display"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:268
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor.js:93
#: code:addons/web_editor/static/src/js/translator.js:165
#, python-format
msgid "This document is not saved!"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:892
#, python-format
msgid "To what URL should this link go?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:310
#, python-format
msgid "Tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:10
#, python-format
msgid "Translate Attribute"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:943
#, python-format
msgid "Transparent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:384
#, python-format
msgid "URL or Email Address"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:863
#, python-format
msgid "Underline"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:957
#, python-format
msgid "Undo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:889
#, python-format
msgid "Unlink"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:921
#, python-format
msgid "Unordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:206
#, python-format
msgid "Upload an image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:213
#, python-format
msgid "Upload image without optimization"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:217
#, python-format
msgid "Uploading..."
msgstr "Uploading..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:122
#, python-format
msgid "Vertical"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:896
#: code:addons/web_editor/static/src/xml/editor.xml:156
#, python-format
msgid "Video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:897
#, python-format
msgid "Video Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:899
#, python-format
msgid "Video URL?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:420
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Warning"
msgstr "Warning"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "White"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor.js:104
#, python-format
msgid "Write Your Text Here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor.js:108
#, python-format
msgid "Write Your Text or Drag a Block Here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/rte.summernote.js:146
#, python-format
msgid "Xl"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:289
#, python-format
msgid "You can cancel to return to the edition mode."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:288
#, python-format
msgid "all"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:94
#: code:addons/web_editor/static/src/xml/editor.xml:103
#: code:addons/web_editor/static/src/xml/editor.xml:116
#: code:addons/web_editor/static/src/xml/editor.xml:125
#, python-format
msgid "auto"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:387
#, python-format
msgid "http://openerp.com"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:223
#, python-format
msgid "http://openerp.com/logo.png"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "ir.attachment"
msgstr "ir.attachment"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "ir.qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "ir.qweb.field"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_barcode
#, fuzzy
msgid "ir.qweb.field.barcode"
msgstr "ir.qweb.field.float"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "ir.qweb.field.contact"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "ir.qweb.field.date"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "ir.qweb.field.datetime"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "ir.qweb.field.duration"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "ir.qweb.field.float"
msgstr "ir.qweb.field.float"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "ir.qweb.field.html"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "ir.qweb.field.image"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "ir.qweb.field.integer"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "ir.qweb.field.many2one"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "ir.qweb.field.monetary"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "ir.qweb.field.qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "ir.qweb.field.relative"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "ir.qweb.field.selection"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "ir.qweb.field.text"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "ir.translation"
msgstr "ir.translation"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_name
msgid "unknown"
msgstr "unknown"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:288
#, python-format
msgid "unsaved changes will be lost."
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "web_editor.converter.test"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "web_editor.converter.test.sub"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:220
#: code:addons/web_editor/static/src/xml/editor.xml:345
#, python-format
msgid "— or —"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:160
#, python-format
msgid "← Previous"
msgstr ""

#~ msgid "Danger Color"
#~ msgstr "Danger Colour"

#~ msgid "Foreground Color"
#~ msgstr "Foreground Colour"

#~ msgid "Info Color"
#~ msgstr "Info Colour"

#~ msgid "Primary Color"
#~ msgstr "Primary Colour"

#~ msgid "Success Color"
#~ msgstr "Success Colour"

#~ msgid "Warning Color"
#~ msgstr "Warning Colour"

#~ msgid "or"
#~ msgstr "or"
