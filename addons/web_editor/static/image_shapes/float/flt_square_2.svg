<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="shape" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M1 .7642.0749.95.0749.095 1 .095Z">
            <animate dur="6s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M1 .7642.0749.95.0749.095 1 .095Z;
            M1 .7842.0749.935.0749.095 1 .095Z;
            M1 .7642.0749.95.0749.095 1 .095Z"
            calcMode="spline"
            keySplines=".56 .37 .43 .58; .56 .37 .43 .58"/>
            <animateTransform attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 -.02;0 0;0 -.02" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
        </path>
        <g id="animation">
            <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform xlink:href="#dots_grp_1" attributeName="transform" attributeType="XML" type="scale" dur="6s" values="1;.9;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform xlink:href="#dots_grp_2" attributeName="transform" attributeType="XML" type="scale" dur="6s" begin="0.3s" values="1;.9;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform xlink:href="#dots_grp_3" attributeName="transform" attributeType="XML" type="scale" dur="6s" begin="0.6s" values="1;.9;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
        </g>
    </defs>
    <svg id="background" viewBox="0 0 28.35 28.35" preserveAspectRatio="none" width="90%" height="65%">
        <rect width="28.35" height="28.35" fill="#383E45"/>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
    <svg id="dots" viewBox="0 0 31.35 18.81" width="15%" height="30%" x="70%" y="70%">
        <g id="dots_grp_1" transform="scale(.5)">
            <rect x="29.73" width="1.62" height="1.62" transform="translate(31.35 -29.73) rotate(90)" fill="#7C6576"/>
            <rect x="19.82" width="1.62" height="1.62" transform="translate(21.44 -19.82) rotate(90)" fill="#7C6576"/>
            <rect x="9.91" width="1.62" height="1.62" transform="translate(11.53 -9.91) rotate(90)" fill="#7C6576"/>
            <rect width="1.62" height="1.62" transform="translate(1.62 0) rotate(90)" fill="#7C6576"/>
        </g>
        <g id="dots_grp_2">
            <rect x="29.73" y="8.59" width="1.62" height="1.62" transform="translate(39.94 -21.14) rotate(90)" fill="#7C6576"/>
            <rect x="19.82" y="8.59" width="1.62" height="1.62" transform="translate(30.03 -11.23) rotate(90)" fill="#7C6576"/>
            <rect x="9.91" y="8.59" width="1.62" height="1.62" transform="translate(20.12 -1.32) rotate(90)" fill="#7C6576"/>
            <rect y="8.59" width="1.62" height="1.62" transform="translate(10.21 8.59) rotate(90)" fill="#7C6576"/>
        </g>
        <g id="dots_grp_3">
            <rect x="29.73" y="17.18" width="1.62" height="1.62" transform="translate(48.54 -12.55) rotate(90)" fill="#7C6576"/>
            <rect x="19.82" y="17.18" width="1.62" height="1.62" transform="translate(38.63 -2.64) rotate(90)" fill="#7C6576"/>
            <rect x="9.91" y="17.18" width="1.62" height="1.62" transform="translate(28.72 7.27) rotate(90)" fill="#7C6576"/>
            <rect y="17.18" width="1.62" height="1.62" transform="translate(18.81 17.18) rotate(90)" fill="#7C6576"/>
        </g>
    </svg>
</svg>
