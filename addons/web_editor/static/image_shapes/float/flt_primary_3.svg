<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.7.8H.2V0H.7Z">
            <animate class="animation" dur="30s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M.7.8H.2V0H.7Z;
            M.8 1H.2V.3H.8Z;
            M.7.8H.2V0H.7Z"
            calcMode="spline"
            keySplines=".56 .37 .43 .58;.56 .37 .43 .58"/>
        </path>
        <g id="animation">
            <animateTransform xlink:href="#pill" attributeName="transform" attributeType="XML" type="translate" dur="30s" values="0 0;0 -400;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".56 .37 .43 .58;.56 .37 .43 .58"/>
            <animateTransform xlink:href="#pill" attributeName="transform" attributeType="XML" type="rotate" dur="30s" values="0;-45;0" repeatCount="indefinite" calcMode="spline" keySplines=".56 .37 .43 .58;.56 .37 .43 .58" additive="sum"/>
            <animateTransform xlink:href="#circles" attributeName="transform" attributeType="XML" type="translate" dur="30s" values="0 0;0 -70;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".56 .37 .43 .58;.56 .37 .43 .58"/>
            <animateMotion xlink:href="#little_circle" dur="30s" repeatCount="indefinite" path="M389,194.52C389,302,302,389,194.52,389S0,302,0,194.52,87.09,0,194.52,0,389,87.09,389,194.52Z"/>
            <animateTransform xlink:href="#triangle" attributeName="transform" attributeType="XML" type="rotate" dur="30s" values="0;-30;0" repeatCount="indefinite" calcMode="spline" keySplines=".56 .37 .43 .58;.56 .37 .43 .58"/>
            <animateTransform xlink:href="#triangle" attributeName="transform" attributeType="XML" type="translate" dur="30s" values="0 -35;-220 -200;0 -35" repeatCount="indefinite" calcMode="spline" keySplines=".56 .37 .43 .58;.56 .37 .43 .58" additive="sum"/>
        </g>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
    <svg class="pill" viewBox="0 0 572.73 850.39" preserveAspectRatio="xMinYMid meet">
        <path id="pill" d="M411.81,839.82,79.49,653.56A83.51,83.51,0,0,1,47.56,540.2h0a83.51,83.51,0,0,1,113.36-31.93L493.24,694.53a83.51,83.51,0,0,1,31.93,113.36h0A83.51,83.51,0,0,1,411.81,839.82Z" fill="#3AADAA" opacity=".2" transform-origin="center" style="transform-box: fill-box"/>
    </svg>
    <svg viewBox="0 0 666.32 671.29" preserveAspectRatio="xMidYMin meet" width="70%" x="34%">
        <g id="circles">
            <circle cx="344.84" cy="354.58" r="194.52" fill="#7C6576" opacity=".2"/>
            <circle id="little_circle" cx="217.45" cy="212.68" r="90.5" fill="#3AADAA" opacity=".2" transform="translate(-70 -70)"/>
        </g>
    </svg>
    <svg viewBox="0 0 666.32 671.29">
        <polygon id="triangle" points="379.36 384.33 545.03 218.66 605.67 444.97 666.32 671.29 440 610.65 213.68 550 379.36 384.33" fill="none" stroke="#F6F6F6" stroke-width="50" transform-origin="center" style="transform-box: fill-box" opacity=".2"/>
    </svg>
</svg>
