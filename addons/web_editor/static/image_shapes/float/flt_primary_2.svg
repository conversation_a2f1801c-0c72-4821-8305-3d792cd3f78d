<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="shape" width="800" height="600">
  <style>
    @keyframes rotate {
      from {transform: rotate(0deg);}
      to   {transform: rotate(360deg);}
    }
    @keyframes rotate_lines {
      from {transform: rotate(0deg) translate(0, 0) scale(.75);}
      to   {transform: rotate(360deg) translate(-10%, 10%) scale(.75);}
    }
    #lines {
      transform-box: fill-box;
      transform-origin: center;
      animation: rotate_lines 65s linear infinite;
    }
    #circle_1 {animation: rotate 120s linear infinite;}
    #circle_2 {animation: rotate 90s linear infinite;}
    #circle_3 {animation: rotate 60s linear infinite;}
  </style>
  <defs>
      <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
          <use xlink:href="#filterPath" fill="none"/>
      </clipPath>
      <path id="filterPath" preserveAspectRatio="none" d="M.8845.2598c.0824.1416.0888.3504.0095.4884c-.0793.1381-.2442.2018-.4091.2018C.3199.95.1549.8863.0883.7553C.0186.6279.0503.4332.1328.2917c.0825-.1416.2156-.2336.3615-.2407C.6434.0404.802.1182.8845.2598z">
          <animate dur="12s" repeatCount="indefinite" attributeName="d" attributeType="XML"
          values="
          M.8845.2598c.0824.1416.0888.3504.0095.4884c-.0793.1381-.2442.2018-.4091.2018C.3199.95.1549.8863.0883.7553C.0186.6279.0503.4332.1328.2917c.0825-.1416.2156-.2336.3615-.2407C.6434.0404.802.1182.8845.2598z;
          M.8356.2785c.0889.135.1492.3268.0921.4618c-.0603.135-.2381.2131-.4127.2096c-.1746-.0035-.3397-.0888-.4159-.2345c-.0761-.1456-.0603-.3516.0254-.4866C.2101.1009.3593.0406.4959.0512C.6324.0618.7467.1436.8356.2785z;
          M.8825.3393c.0833.1495.0908.3021.0151.4166c-.0757.1176-.2385.1972-.3974.194C.3412.9468.1898.864.1141.7464c-.0795-.1176-.087-.2671-.0113-.4134C.1784.1868.3375.05.4926.05C.6478.05.7993.1931.8825.3393z;
          M.8845.2598c.0824.1416.0888.3504.0095.4884c-.0793.1381-.2442.2018-.4091.2018C.3199.95.1549.8863.0883.7553C.0186.6279.0503.4332.1328.2917c.0825-.1416.2156-.2336.3615-.2407C.6434.0404.802.1182.8845.2598z"
          calcMode="spline"
          keySplines=".45 .05 .55 .95;.45 .05 .55 .95;.45 .05 .55 .95"/>
      </path>
      <g id="animation">
          <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
      </g>
  </defs>
  <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
      <use xlink:href="#filterPath" fill="darkgrey"/>
  </svg>
  <image xlink:href="" clip-path="url(#clip-path)"/>
  <svg class="lines" viewBox="0 0 57.65 57.65" width="35%" height="35%" x="70%" y="15%" opacity=".5">
      <path id="lines" d="M16.44,22.68,11.22,6.6M21.73,17,26,.61M29.27,15,41.72,3.58m-5,13.86,16.65-2.88M41.57,23.49l15.58,6.58M52,45.18,42.43,31.23m-3.46,7,.49,16.89m-15.9,1.58L32.29,42.2M9.34,49.42,24.5,42m-6.41-4.42L1.3,35.61m13.79-5.26L2,19.65" fill="none" stroke="#383E45" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1.5" transform-origin="center" transform="scale(.75)"/>
  </svg>
  <svg class="donut" viewBox="0 0 39.7 39.7" width="30%" height="30%" x="60%" y="70%" opacity=".5">
      <path id="donut" d="M19.8,0C8.9,0,0,8.9,0,19.8c0,11,8.9,19.8,19.8,19.8c11,0,19.8-8.9,19.8-19.8C39.7,8.9,30.8,0,19.8,0z M19.8,36.5 c-9.2,0-16.7-7.5-16.7-16.7c0-9.2,7.5-16.7,16.7-16.7c9.2,0,16.7,7.5,16.7,16.7C36.6,29,29.1,36.5,19.8,36.5z" fill="#3AADAA" transform="scale(.75)" transform-origin="center">
        <animate xlink:href="#donut" dur="30s" repeatCount="indefinite" attributeName="d" attributeType="XML"
          values="
          M19.8,0C8.9,0,0,8.9,0,19.8c0,11,8.9,19.8,19.8,19.8c11,0,19.8-8.9,19.8-19.8C39.7,8.9,30.8,0,19.8,0z M19.8,36.5 c-9.2,0-16.7-7.5-16.7-16.7c0-9.2,7.5-16.7,16.7-16.7c9.2,0,16.7,7.5,16.7,16.7C36.6,29,29.1,36.5,19.8,36.5z;
          M19.8,0C8.9,0,0,8.9,0,19.8c0,11,8.9,19.8,19.8,19.8c11,0,19.8-8.9,19.8-19.8C39.7,8.9,30.8,0,19.8,0z M19.8,29.7 c-5.4,0-9.9-4.4-9.9-9.9c0-5.4,4.4-9.9,9.9-9.9c5.4,0,9.9,4.4,9.9,9.9C29.7,25.3,25.3,29.7,19.8,29.7z;
          M19.8,0C8.9,0,0,8.9,0,19.8c0,11,8.9,19.8,19.8,19.8c11,0,19.8-8.9,19.8-19.8C39.7,8.9,30.8,0,19.8,0z M19.8,36.5 c-9.2,0-16.7-7.5-16.7-16.7c0-9.2,7.5-16.7,16.7-16.7c9.2,0,16.7,7.5,16.7,16.7C36.6,29,29.1,36.5,19.8,36.5z"
          calcMode="spline"
          keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
          <animateTransform attributeName="transform" attributeType="XML" type="scale" dur="30s" values="1;.75;1" repeatCount="indefinite" additive="sum"/>
          <animateTransform attributeName="transform" attributeType="XML" type="translate" dur="30s" values="0 0;15 0;0 0" repeatCount="indefinite" additive="sum"/>
      </path>
  </svg>
  <svg class="circles" viewBox="0 0 28.68 28.68" width="45%" height="45%" y="50%" opacity=".75">
      <path id="circle_1" d="M13.53,28.18A13.86,13.86,0,1,1,28.18,13.53" fill="none" stroke="#7C6576" stroke-linecap="round" stroke-miterlimit="10" transform-origin="center"/>        
      <path id="circle_2" d="M21.72,22.9A11.28,11.28,0,0,1,3.07,14.36,11.27,11.27,0,0,1,21.72,5.82" fill="none" stroke="#7C6576" stroke-linecap="round" stroke-miterlimit="10" transform-origin="center"/>
      <path id="circle_3" d="M23,15.19a8.71,8.71,0,1,1-9.5-9.5" fill="none" stroke="#7C6576" stroke-linecap="round" stroke-miterlimit="10" transform-origin="center"/>
  </svg>
</svg>
