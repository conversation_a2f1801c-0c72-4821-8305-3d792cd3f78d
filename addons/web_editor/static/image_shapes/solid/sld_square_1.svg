<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600" id="shape">
  <defs>
      <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
          <use xlink:href="#filterPath" fill="none"/>
      </clipPath>
      <path id="filterPath" d="M.856.9025.094.7888 0 .1101l.95-.1101L.856.9025z"/>
      <g id="animation">
          <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
      </g>
  </defs>
  <svg viewBox="0 0 28.3 28.3" preserveAspectRatio="none">
      <path id="shadow" d="M25.5,28.3L2.8,25.1L0,6l28.3-3.1L25.5,28.3z" fill="#7C6576" transform="scale(.97)">
        <animateTransform attributeName="transform" attributeType="XML" type="translate" dur="6s" values=".8 0;.5 -.3;.8 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95" additive="sum"/>
      </path>
  </svg>
  <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
      <use xlink:href="#filterPath" fill="darkgrey"/>
  </svg>
  <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
