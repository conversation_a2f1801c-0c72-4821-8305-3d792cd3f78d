<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="60" id="shape">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.14.1882.99.1l-.0421.8L.188.8574.14.1882z"/>
        <g id="animation">
            <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>    
        </g>
    </defs>
    <svg preserveAspectRatio="none" viewBox="0 0 1600 1201" width="96%" height="96%" x="2%" y="2%">
        <path id="background_1" d="M1287.58.7c82.33-12.93 80.89 155.17 144.44 337.64c62.1 182.47 189.2 379.3 164.65 554.59c-23.11 175.28-197.87 327.58-368.3 306c-169-20.12-332.2-214.08-564.74-274.42c-232.54-60.34-534.4 12.93-628.28-70.4c-93.88-84.77 18.78-324.71 122.77-508.62c104-182.47 199.32-307.46 316.31-287.35c117 18.68 257.09 181 410.19 165.23C1037.72 207.6 1205.26 13.63 1287.58.7Z" fill="#7C6576">
            <animate xlink:href="#background_1" dur="30s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M1287.58.7c82.33-12.93 80.89 155.17 144.44 337.64c62.1 182.47 189.2 379.3 164.65 554.59c-23.11 175.28-197.87 327.58-368.3 306c-169-20.12-332.2-214.08-564.74-274.42c-232.54-60.34-534.4 12.93-628.28-70.4c-93.88-84.77 18.78-324.71 122.77-508.62c104-182.47 199.32-307.46 316.31-287.35c117 18.68 257.09 181 410.19 165.23C1037.72 207.6 1205.26 13.63 1287.58.7Z;
            M1127.84 155.25c11 93-99.32 200.55 9.65 327.47c109 125.95 437.3 270.3 460.75 339.09c24.83 69.76-255.2 63-506.27 139.51C842.29 1037.86 618.81 1195.78 429.83 1190c-189-4.85-343.49-174.39-401.43-338.12C-30.92 688.11 7.71 530.19 133.24 444C258.77 357.81 469.83 343.21 605 265.7c133.81-77.51 191.75-218 285.55-249C985.75-15.26 1116.8 62.24 1127.84 155.25Z;
            M1321.79,69.6c150.55,66.91,275,224.78,278.1,382.66,4.18,158.91-112.91,318.88-220.6,465.25-106.64,146.37-203.88,279.15-304.25,250.92-101.41-29.27-206-221.65-385.79-262.42-179.83-41.82-434.93,68-570.85,18.82C-18.56,874.64-35.29,666.59,60.9,539,156,411.48,364.1,365.48,509.42,297.52c144.28-68,225.83-157.87,355.48-214.33C993.49,25.69,1170.19,2.69,1321.79,69.6Z;
            M1287.58.7c82.33-12.93 80.89 155.17 144.44 337.64c62.1 182.47 189.2 379.3 164.65 554.59c-23.11 175.28-197.87 327.58-368.3 306c-169-20.12-332.2-214.08-564.74-274.42c-232.54-60.34-534.4 12.93-628.28-70.4c-93.88-84.77 18.78-324.71 122.77-508.62c104-182.47 199.32-307.46 316.31-287.35c117 18.68 257.09 181 410.19 165.23C1037.72 207.6 1205.26 13.63 1287.58.7Z"/>
        </path>
    </svg>
    <svg viewBox="0 0 28.3 28.3" width="45%" height="45%" y="50%">
        <path id="background_2" d="M22.6 9c1.6 4.2 1.2 8.1-.6 11.7c-1.8 3.6-4.9 6.9-9.2 7.6C8.5 29 3.1 27 1 23.2C-1.1 19.4.2 13.8 2.9 8.9C5.6 4.1 9.8-.2 13.6 0C17.4.2 21 4.8 22.6 9z" fill="#3AADAA">
            <animate xlink:href="#background_2" dur="18s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M25 9c1.6 4.2 1.2 8.1-.6 11.7c-1.8 3.6-4.9 6.9-9.2 7.6C10.9 29 5.5 27 3.4 23.2C1.3 19.4 2.6 13.8 5.3 8.9C8 4.1 12.2-.2 16 0C19.8.2 23.4 4.8 25 9z;
            M25.1,7.9c2.8,4.2,4.3,9.7,2.3,13.7c-2,4.1-7.5,6.7-12.5,6.4c-4.9-0.3-9.3-3.6-12.1-8.2 C0.1,15.3-1.1,9.6,1.1,5.7c2.2-3.9,7.7-5.8,12.5-5.4C18.4,0.8,22.4,3.7,25.1,7.9z;
            M26.9 6.6c2.1 3.6 1.9 8.5-.3 12.9c-2.2 4.3-6.3 8.2-11.3 8.6c-5 .5-10.8-2.4-13.5-7C-.9 16.5-.5 9.9 2.4 5.8c2.9-4.1 8.3-5.8 13.2-5.5C20.5.6 24.8 2.9 26.9 6.6z;
            M25 9c1.6 4.2 1.2 8.1-.6 11.7c-1.8 3.6-4.9 6.9-9.2 7.6C10.9 29 5.5 27 3.4 23.2C1.3 19.4 2.6 13.8 5.3 8.9C8 4.1 12.2-.2 16 0C19.8.2 23.4 4.8 25 9z"
            calcMode="spline"
            keySplines=".45 .05 .55 .95; .45 .05 .55 .95; .45 .05 .55 .95"/>
        </path>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
