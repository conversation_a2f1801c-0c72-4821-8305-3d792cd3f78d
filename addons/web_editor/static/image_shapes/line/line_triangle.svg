<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <style>
        @keyframes rotate {
            0%, 100% {transform: rotate(45deg) scale(.8);}
            50%      {transform: rotate(225deg) scale(.8);}
        }
        #triangles path {
            transform-box: fill-box;
            transform-origin: center;
            transform: rotate(45deg) scale(.8);
        }
        #line_1 {animation: rotate 15s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_2 {animation: rotate 15s .1s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_3 {animation: rotate 15s .2s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_4 {animation: rotate 15s .3s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_5 {animation: rotate 15s .4s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_6 {animation: rotate 15s .5s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_7 {animation: rotate 15s .6s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_8 {animation: rotate 15s .7s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_9 {animation: rotate 15s .8s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_10 {animation: rotate 15s .9s cubic-bezier(.45, .05, .55, .95) infinite;}
        #line_11 {animation: rotate 15s 1s cubic-bezier(.45, .05, .55, .95) infinite;}
    </style>
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z">
            <animate dur="12s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z;
            M.8462.216c.0583.047.0185.2117-.0344.3265c-.0557.1177-.1272.1853-.2067.2236c-.0795.0382-.1696.047-.2491.0118c-.0795-.0323-.1537-.1088-.1961-.2206c-.0425-.1118-.053-.253.008-.3029c.0557-.0441.1855.0059.3259-.0029c.1405-.0118.2942-.0824.3525-.0353z;
            M.7205.3445c.0955.0828.1825.1804.1459.2377c-.0365.0594-.1937.0806-.3285.1231c-.1348.0446-.2471.1103-.3144.0913c-.0702-.0191-.0955-.121-.0983-.208c-.0028-.0849.0112-.155.0618-.2293c.0505-.0743.1404-.1528.2358-.1592c.0983-.0043.2021.0616.2976.1444z;
            M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z;
            M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z"
            calcMode="spline" keySplines=".56 .37 .43 .58;.56 .37 .43 .58;.56 .37 .43 .58;.56 .37 .43 .58"/>
        </path>
    </defs>
    <svg viewBox="0 0 28.35 28.35" preserveAspectRatio="none">
        <g id="triangles">
            <path id="line_1" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_2" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_3" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_4" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_5" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_6" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_7" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_8" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_9" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_10" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
            <path id="line_11" d="M6.58,12.42,11.6,4A3.22,3.22,0,0,1,17.18,4L22,12.6l4.82,8.58A3.21,3.21,0,0,1,24,26l-9.84-.11-9.85-.11a3.22,3.22,0,0,1-2.73-4.86Z" fill="none" stroke="#3AADAA" stroke-width="1" stroke-miterlimit="10" opacity=".2"/>
        </g>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
