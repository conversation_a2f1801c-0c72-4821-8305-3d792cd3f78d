<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="60" id="shape">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M0.947,0.9468L0.0495,1L0,0l1,0.1065L0.947,0.9468z">
            <animateTransform attributeName="transform" attributeType="XML" type="scale" dur="12s" values="1;.95;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform attributeName="transform" attributeType="XML" type="rotate" dur="12s" values="0; -6;0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95" additive="sum"/>
        </path>
        <g id="animation">
            <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
        </g>
    </defs>
    <svg viewBox="0 0 28.3 28.3" preserveAspectRatio="none">
        <path id="background_1" d="M0.3,0.3l27.8,2.8l-1.5,21.6L1.7,26L0.3,0.3 M0,0l1.4,26.3l25.3-1.4l1.6-22.1L0,0L0,0z" fill="#7C6576">
            <animateTransform attributeName="transform" attributeType="XML" begin="1.5s" type="scale" dur="12s" values="1;.95;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform attributeName="transform" attributeType="XML" begin="1.5s" type="rotate" dur="12s" values="0; -6;0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95" additive="sum"/>
        </path>
        <path id="background_2" d="M0.3,0.3l27.8,2.8l-1.5,21.6L1.7,26L0.3,0.3 M0,0l1.4,26.3l25.3-1.4l1.6-22.1L0,0L0,0z" fill="#7C6576" opacity=".75">
            <animateTransform attributeName="transform" attributeType="XML" begin="2.5s" type="scale" dur="12s" values="1;.95;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform attributeName="transform" attributeType="XML" begin="2.5s" type="rotate" dur="12s" values="0; -6;0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95" additive="sum"/>
        </path>
        <path id="background_3" d="M0.3,0.3l27.8,2.8l-1.5,21.6L1.7,26L0.3,0.3 M0,0l1.4,26.3l25.3-1.4l1.6-22.1L0,0L0,0z" fill="#7C6576" opacity=".5">
            <animateTransform attributeName="transform" attributeType="XML" begin="3.5s" type="scale" dur="12s" values="1;.95;1" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
            <animateTransform attributeName="transform" attributeType="XML" begin="3.5s" type="rotate" dur="12s" values="0; -6;0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95" additive="sum"/>
        </path>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
