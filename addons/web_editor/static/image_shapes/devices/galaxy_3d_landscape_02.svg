<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 3000 1819" data-forced-size="true" width="3000" height="1819" data-img-aspect-ratio="20:9" data-img-perspective="[[0.75, 64.74], [66.8, -0.22], [99.24, 24.08], [33.73, 93.17]]">
    <defs>
        <mask id="mask" x="0" y="405.11" width="3000" height="1414.39" maskUnits="userSpaceOnUse">
            <path d="M1146.36,1760.17c-46.32,29.86-75.6,55-138.36,58.76-45.84,2.75-82-3-175.92-53.33-174-93.34-577.18-308.67-767.43-410.89-46.95-25.23-103.28-124-18.29-206.93-39,44,0,74.08,16.8,83.46l844,441c29.16,15.8,122.06,43.21,239.17-32.1l24.11-15.5c197-126.69,1245-800.51,1770.18-1138.66,22.24-14.32,49.42-39.9,2.47-80.89,43,27.65,104.15,121.39-2.47,193.49C2822.05,678.81,1695,1406.39,1146.36,1760.17Z" fill="#1f1f1f"/>
        </mask>
        <mask id="mask-2" x="1027.77" y="481.56" width="1925.26" height="1247.99" maskUnits="userSpaceOnUse">
            <path d="M1128.57,1687.57c-11.36,7.3-37.06,16.79-56.82,0,40.32-16.2,59.79-30.13,66.21-34.57L2913,512.67c13.83-9.38,36.57-24.69,38.05-31.11,4.45,13.33,4,33.08-28.66,53.83C2888.76,556.72,1712.5,1312.4,1128.57,1687.57Z" fill="url(#gradient_01)"/>
        </mask>
        <mask id="mask-3" x="396.8" y="1475.5" width="130.2" height="101.21" maskUnits="userSpaceOnUse">
            <path d="M505.86,1574.08l-86.53-46.45c-12.05-6.47-22.13-22.79-22.52-36.46s9.08-19.5,21.13-13l86.54,46.44c12,6.47,22.13,22.8,22.51,36.46S517.91,1580.54,505.86,1574.08Z"/>
        </mask>
        <linearGradient id="gradient_01" x1="780.07" y1="-5623.42" x2="1134.84" y2="-5068.77" gradientTransform="translate(3035.45 -4157.39) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.63" stop-color="#949494"/>
            <stop offset="0.74" stop-color="#e0e0e0"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="-5249.67" cy="-1759.76" r="0.99" gradientTransform="matrix(350, 699.58, -1313.26, 656.41, -472182.75, 4828539.53)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#696969"/>
            <stop offset="0.18" stop-color="#707072"/>
            <stop offset="0.19" stop-color="#b9b9b9"/>
            <stop offset="0.19" stop-color="#5d5d5d"/>
            <stop offset="0.2"/>
            <stop offset="0.2"/>
            <stop offset="0.21" stop-color="#717171"/>
            <stop offset="0.24" stop-color="#7e7e7e"/>
            <stop offset="0.37" stop-color="#848484"/>
            <stop offset="0.39"/>
            <stop offset="0.4" stop-color="#41413f"/>
            <stop offset="0.66" stop-color="#676767"/>
            <stop offset="0.68" stop-color="#c7c7c7"/>
            <stop offset="0.68"/>
            <stop offset="0.68"/>
            <stop offset="0.69" stop-color="#525252"/>
            <stop offset="0.73" stop-color="#737373"/>
            <stop offset="0.87" stop-color="#747474"/>
            <stop offset="0.88" stop-color="#434343"/>
            <stop offset="0.89"/>
            <stop offset="0.9"/>
            <stop offset="0.91" stop-color="#585858"/>
            <stop offset="0.92" stop-color="#6c6c6c"/>
        </radialGradient>
        <radialGradient id="gradient_03" cx="-5248.93" cy="-1760.44" r="0.99" gradientTransform="matrix(300.26, 801.3, -1657.21, 620.41, -1340166.07, 5299057.82)" gradientUnits="userSpaceOnUse">
            <stop offset="0.21" stop-color="#363636"/>
            <stop offset="0.21" stop-color="#f5f5f5"/>
            <stop offset="0.21" stop-color="silver"/>
            <stop offset="0.22"/>
            <stop offset="0.24" stop-color="#3c3c3c"/>
        </radialGradient>
        <linearGradient id="gradient_04" x1="255.53" y1="-4439.05" x2="960.41" y2="-5411.79" gradientTransform="translate(3035.45 -4157.39) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.02"/>
            <stop offset="0.04" stop-color="#c3c3c3"/>
            <stop offset="0.05" stop-color="#7b7b7b"/>
            <stop offset="0.49" stop-color="#4a4a4a"/>
            <stop offset="1" stop-color="#c4c4c4" stop-opacity="0"/>
            <stop offset="1" stop-color="#403b3b" stop-opacity="0"/>
        </linearGradient>
        <radialGradient id="gradient_05" cx="-5255.66" cy="-1757.04" r="0.99" gradientTransform="matrix(284, 249.85, -522.53, 593.4, 575573.86, 2356651.76)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#515151"/>
            <stop offset="0.18" stop-color="#676767"/>
            <stop offset="0.8" stop-color="#5a5a5a"/>
            <stop offset="0.87" stop-color="#a7a7a7"/>
            <stop offset="0.89" stop-color="#a5a5a5"/>
            <stop offset="0.9" stop-color="#8e8e8e"/>
            <stop offset="0.92" stop-color="#949494"/>
            <stop offset="0.92" stop-color="#929292"/>
            <stop offset="0.95" stop-color="#b9b9b9"/>
        </radialGradient>
        <linearGradient id="gradient_06" x1="780.07" y1="-5623.42" x2="1134.84" y2="-5068.77" gradientTransform="translate(3035.45 -4157.39) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.61" stop-color="#4f4f4f"/>
            <stop offset="0.71" stop-color="#e0e0e0"/>
            <stop offset="0.91" stop-color="#fff"/>
        </linearGradient>
        <linearGradient id="gradient_07" x1="2596.11" y1="-5724.5" x2="2532.94" y2="-5603.57" gradientTransform="translate(3035.45 -4157.39) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.53" stop-color="#313131"/>
            <stop offset="0.7" stop-color="#7b7b7b"/>
            <stop offset="0.82" stop-color="#9d9d9d"/>
        </linearGradient>
        <linearGradient id="gradient_08" x1="407.29" y1="-3553.99" x2="422.35" y2="-3557.85" gradientTransform="matrix(-1, 0, 0, -1, 752.56, -2082.85)" gradientUnits="userSpaceOnUse">
            <stop offset="0.08" stop-color="#2f2f2f"/>
            <stop offset="0.47" stop-color="#f0f0f0"/>
            <stop offset="0.62" stop-color="#9a9a9a"/>
        </linearGradient>
        <linearGradient id="gradient_09" x1="1643.24" y1="-7295.54" x2="1656.9" y2="-7302.97" gradientTransform="matrix(0, -1, 1, 0, 7644.64, 3106.04)" gradientUnits="userSpaceOnUse">
            <stop offset="0.08" stop-color="#2f2f2f"/>
            <stop offset="0.47" stop-color="#979797"/>
            <stop offset="0.62" stop-color="#474747"/>
        </linearGradient>
        <radialGradient id="gradient_10" cx="-4987.46" cy="-1996.31" r="0.99" gradientTransform="matrix(-20.7, 1.48, -0.62, -8.59, -102020.72, -9519.63)" gradientUnits="userSpaceOnUse">
            <stop offset="0.21" stop-color="#191919"/>
            <stop offset="0.27" stop-color="#365153"/>
            <stop offset="0.4" stop-color="#1f5558"/>
            <stop offset="0.52" stop-color="#60acba"/>
            <stop offset="0.71" stop-color="#416961"/>
            <stop offset="0.92" stop-color="#191919"/>
        </radialGradient>
        <radialGradient id="gradient_11" cx="-4987.46" cy="-1996.31" r="0.99" gradientTransform="matrix(-20.7, 1.48, -0.62, -8.59, -102020.72, -9519.63)" gradientUnits="userSpaceOnUse">
            <stop offset="0.41" stop-opacity="0"/>
            <stop offset="0.51"/>
            <stop offset="0.82" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="gradient_12" cx="-4986.6" cy="-1995" r="0.99" gradientTransform="matrix(-20.7, 1.48, -0.62, -8.59, -102020.72, -9519.63)" gradientUnits="userSpaceOnUse">
            <stop offset="0.09"/>
            <stop offset="0.17" stop-opacity="0.13"/>
            <stop offset="0.3" stop-opacity="0.13"/>
            <stop offset="0.46"/>
            <stop offset="0.63"/>
            <stop offset="0.68" stop-opacity="0"/>
            <stop offset="0.88" stop-opacity="0"/>
            <stop offset="0.95"/>
        </radialGradient>
        <radialGradient id="gradient_13" cx="-4480.42" cy="-2820.61" r="0.99" gradientTransform="matrix(-5.88, -1.31, 0.72, -3.21, -21852.81, -14710.17)" gradientUnits="userSpaceOnUse">
            <stop offset="0.13" stop-color="#58a9d7"/>
            <stop offset="1" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="gradient_14" x1="2607.34" y1="-5669.05" x2="2530.4" y2="-5696.22" gradientTransform="translate(3035.45 -4157.39) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0.27" stop-color="#a8a8a8"/>
            <stop offset="0.8" stop-color="#ababab"/>
            <stop offset="0.9" stop-color="#afafaf"/>
            <stop offset="0.96" stop-color="#404040"/>
            <stop offset="0.98" stop-color="#404040"/>
            <stop offset="1" stop-color="#404040" stop-opacity="0.69"/>
        </linearGradient>
        <linearGradient id="gradient_15" x1="2302.34" y1="-5025.68" x2="2299.14" y2="-5175.96" gradientTransform="matrix(0.54, 0.84, 0.84, -0.54, 5270.21, -3700.97)" gradientUnits="userSpaceOnUse">
            <stop offset="0.02" stop-color="#6e6e6e"/>
            <stop offset="0.02" stop-color="#1f1f1f"/>
            <stop offset="0.02" stop-color="#2c2c2c"/>
            <stop offset="0.03" stop-color="#646464"/>
            <stop offset="0.94" stop-color="#3e3e3e"/>
            <stop offset="0.95" stop-color="#2c2c2c"/>
        </linearGradient>
        <linearGradient id="gradient_16" x1="2306.64" y1="-4545.06" x2="2295.01" y2="-4831.34" gradientTransform="matrix(0.54, 0.84, 0.84, -0.54, 5270.21, -3700.97)" gradientUnits="userSpaceOnUse">
            <stop offset="0.02" stop-color="#6e6e6e"/>
            <stop offset="0.02" stop-color="#1f1f1f"/>
            <stop offset="0.02" stop-color="#2c2c2c"/>
            <stop offset="0.03" stop-color="#646464"/>
            <stop offset="0.97" stop-color="#3e3e3e"/>
            <stop offset="0.98" stop-color="#2c2c2c"/>
        </linearGradient>
        <linearGradient id="gradient_17" x1="1431.34" y1="818.64" x2="3778.83" y2="-125.56" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="2921.14 399.82 2950.74 445.93 2947.74 501.51 1020.79 1734.75 37.47 1216.43 63.03 1143.37 1951.57 19.84 2018.86 8.26 2099.78 27.19 2921.14 399.82"/>
        </clipPath>
        <path id="filterPath" d="M0.9745,0.2198l0.0099,0.0253-0.001,0.0306L0.3405,0.9537,0.0125,0.6687,0.021,0.6286,0.6511,0.0109l0.0224-0.0064,0.027,0.0104Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1146.36,1760.17c-87.46,56.39-153.95,91.46-314.28,5.43-174-93.34-577.18-308.67-767.43-410.89-46.95-25.23-103.28-124-18.29-206.93-39,44,0,74.08,16.8,83.46l844,441c29.16,15.8,122.06,43.21,239.17-32.1l24.13-15.51c197.07-126.72,1245-800.51,1770.16-1138.65,22.24-14.32,49.42-39.9,2.47-80.89,43,27.65,104.15,121.39-2.47,193.49C2822.05,678.81,1695,1406.39,1146.36,1760.17Z" fill="#1a1a1a"/>
        <path d="M2967.4,433.67c-5.07-14.33-19.31-26.83-39.67-36.47C2681.64,280.65,2159.29,43.81,2086.62,14.44c-70.75-28.6-137.1-5.28-165.93,11.92C1637.34,195.42,1213.21,448.75,842.26,670.31c-395.88,236.46-731.2,436.75-770.21,459.83-18.86,11.16-30.65,22.28-37.18,33s-7.81,21.08-5.65,30.51c4.3,18.77,22,33.77,37.44,41.91,236.2,124.78,732.05,386.5,825.75,435.1h0c96.07,48,187.07,14.51,219.39-6.48,241.19-156.65,587.13-376.85,914.16-585,71.64-45.6,142.37-90.63,210.9-134.28,190.68-121.47,364.28-232.32,492.82-315.44C2794,587.9,2847,553.27,2885.19,527.71c19.11-12.78,34.54-23.29,45.85-31.27s18.51-13.45,21.19-16.16C2968.21,464.05,2972.51,448.1,2967.4,433.67ZM2916,477.27,1088.22,1646a156.16,156.16,0,0,1-156.51,6.83L96.25,1216.19c-32.19-16.83-33.47-62.41-2.27-81L1932.67,38.57a159.18,159.18,0,0,1,147.4-8.2L2911,408C2939.55,421,2942.36,460.39,2916,477.27Z" stroke-width="1.98" fill="url(#gradient_02)" stroke="url(#gradient_03)"/>
        <path d="M2921.14,399.82c-245.56-115.3-764.38-352.95-836.86-382C2014.06-10.32,1945.77,16,1917.17,32.9,1332.67,378.65,151.46,1085.53,76.51,1130.49c-38.55,23.12-58.31,71.42-9.49,97,235.7,123.44,730.5,382.36,824,430.44,112,57.61,188.71,16.21,220.76-4.43,586.8-377.83,1782.92-1143.29,1827.4-1174.9C2987.2,444.46,2947.28,412.09,2921.14,399.82ZM2916,477.27,1088.22,1646a156.16,156.16,0,0,1-156.51,6.83L96.25,1216.19c-32.19-16.83-33.47-62.41-2.27-81L1932.67,38.57a159.18,159.18,0,0,1,147.4-8.2L2911,408C2939.55,421,2942.36,460.39,2916,477.27Z"/>
        <path d="M1817.42,1192.72,2918.91,482.54c26.68-18.27,33.6-27.16,33.6-47.41,5.43,17.78,10.38,22.72-26.69,47.41C2891.41,505.47,2173.39,965.38,1817.42,1192.72Z" fill="url(#gradient_04)"/>
        <path d="M109.62,1125.55c-68.2,42.47-26.19,83.47,4.94,100.26C45.38,1200.62,35,1160.62,109.62,1125.55Z"/>
        <path d="M2911,480.57c44.48-34.57,16.8-62.72-12.85-78.53C2929.28,410.44,2985.12,439.58,2911,480.57Z"/>
        <g mask="url(#mask)">
            <path d="M906.2,1763.13c35.09,18.39,126.51,34.12,200.63-13.34,136.39-87.31,1285.47-829.85,1815-1172.42,40.52-26.22,79.07-59.76,73.14-107.67,18.28,73.59-16.31,105.69-71.66,140.75l-885,579.3L1139,1765.11c-58.31,40-138.62,90.38-280.19,14.81s-518.87-278-792.63-424.23C46.36,1345.13-19.85,1295,5.84,1209c-7.94,28.63.94,78,69.18,115.06C149.64,1364.59,709.53,1660,906.2,1763.13Z" fill="url(#gradient_05)"/>
        </g>
        <path d="M1128.57,1687.57c-11.36,7.3-37.06,16.79-56.82,0,40.32-16.2,59.79-30.13,66.21-34.57L2913,512.67c13.83-9.38,36.57-24.69,38.05-31.11,4.45,13.33,4,33.08-28.66,53.83C2888.76,556.72,1712.5,1312.4,1128.57,1687.57Z" fill="url(#gradient_06)"/>
        <g mask="url(#mask-2)">
            <ellipse cx="1067.79" cy="1689.54" rx="40.03" ry="40" fill="#454545"/>
        </g>
        <path d="M74,1281.61c-15.82-8.07-45.66-24.89-36.57-65.18,12.85,16.3,40.52,29.13,52.87,35.55l811.91,428.18c3.46,2,40,15.8,54.85,15.8-10.05,11.86-38.44,29-77.58,8.9S326.23,1414.29,74,1281.61Z" fill="url(#gradient_07)"/>
        <path d="M106.65,1377.25c-10.87-17-25.2-62.65,4.45-114.4-3-1.49-15.81-8.4-18.78-9.88-14.16,23.71-34.49,77.46-4.45,114.2Z" fill="#383838" fill-opacity="0.81"/>
        <path d="M860.29,1780.27l9.38,4.63c-10.81-17.78-25-65.28,4.45-119.57l-18.78-9.91C841.26,1680.3,821,1737,850.9,1775.51Z" fill="#383838" fill-opacity="0.81"/>
        <path d="M2373.8,965.35c2.47-1.66,16.81-10.84,18.29-11.83,2-21.23-1.68-72.4-22.24-92.94L2353.54,871C2362.44,880.33,2378.94,920.3,2373.8,965.35Z" fill="#2b2b2b" fill-opacity="0.83"/>
        <g>
            <path d="M701.91,1640v26.22c0,5.16-3.83,7.67-8.57,5.61h0a15.43,15.43,0,0,1-8.57-13.06v-26.22c0-5.16,3.84-7.67,8.57-5.62h0A15.44,15.44,0,0,1,701.91,1640Z"/>
            <path d="M725.63,1652.84v26.22c0,5.16-3.83,7.67-8.57,5.62h0a15.46,15.46,0,0,1-8.57-13.07v-26.22c0-5.16,3.84-7.67,8.57-5.61h0A15.41,15.41,0,0,1,725.63,1652.84Z"/>
            <path d="M748.37,1664.69v26.22c0,5.16-3.84,7.68-8.57,5.62h0a15.43,15.43,0,0,1-8.58-13.06v-26.22c0-5.16,3.84-7.68,8.58-5.62h0A15.46,15.46,0,0,1,748.37,1664.69Z"/>
            <path d="M771.1,1676.55v26.22c0,5.16-3.84,7.67-8.57,5.62h0a15.44,15.44,0,0,1-8.57-13.07V1669.1c0-5.16,3.83-7.67,8.57-5.62h0A15.46,15.46,0,0,1,771.1,1676.55Z"/>
            <path d="M793.83,1689.38v26.22c0,5.16-3.84,7.67-8.57,5.62h0a15.44,15.44,0,0,1-8.57-13.07v-26.22c0-5.16,3.83-7.67,8.57-5.61h0A15.43,15.43,0,0,1,793.83,1689.38Z"/>
            <path d="M818.54,1702.68V1728c0,5.41-4.06,8.13-9.07,6.07h0a15.8,15.8,0,0,1-9.06-13.52v-25.31c0-5.41,4.06-8.13,9.06-6.07h0A15.79,15.79,0,0,1,818.54,1702.68Z"/>
        </g>
        <g>
            <ellipse cx="347.82" cy="1469.86" rx="8.16" ry="9.03" transform="translate(-120.97 34) rotate(-4.77)" fill="url(#gradient_08)"/>
            <ellipse cx="346.04" cy="1464.21" rx="9.72" ry="7.85" transform="translate(-1150.52 1309.41) rotate(-70.8)" fill="url(#gradient_09)"/>
            <ellipse cx="347.25" cy="1466.67" rx="8.61" ry="10.42" fill="#0b0b0b"/>
        </g>
        <ellipse cx="2439.63" cy="236.41" rx="29.65" ry="15.31" fill="#0c0c0c"/>
        <path d="M2460.73,234.61c.82,4.65-7.67,9.07-19,9.88s-21.12-2.3-21.94-7,7.66-9.08,19-9.89S2459.91,230,2460.73,234.61Z" fill="url(#gradient_10)"/>
        <path d="M2460.73,234.61c.82,4.65-7.67,9.07-19,9.88s-21.12-2.3-21.94-7,7.66-9.08,19-9.89S2459.91,230,2460.73,234.61Z" fill="url(#gradient_11)"/>
        <path d="M2460.73,234.61c.82,4.65-7.67,9.07-19,9.88s-21.12-2.3-21.94-7,7.66-9.08,19-9.89S2459.91,230,2460.73,234.61Z" fill="url(#gradient_12)"/>
        <path d="M2454.56,237.45c-2.15,1.36-6.49,1.88-9.7,1.16s-4.06-2.4-1.91-3.76,6.49-1.88,9.7-1.17S2456.72,236.08,2454.56,237.45Z" fill="url(#gradient_13)"/>
        <path d="M504.19,1580.75l-82.59-44.34c-16.18-8.68-29.71-30.59-30.22-48.92s12.17-26.18,28.35-17.5l82.61,44.33c16.17,8.68,29.7,30.59,30.22,48.94S520.37,1589.43,504.19,1580.75Z" fill="#353535"/>
        <path d="M505.86,1574.08l-86.53-46.45c-12.05-6.47-22.13-22.79-22.52-36.46s9.08-19.5,21.13-13l86.54,46.44c12,6.47,22.13,22.8,22.51,36.46S517.91,1580.54,505.86,1574.08Z"/>
        <g mask="url(#mask-3)">
            <path d="M431.17,1509.19s-3.27-2.08-1.78-4.54c1-1.64,3.46-.5,3.46-.5l67.2,36.55,20.95-1.56c1.33.15,2.49.47,3,1.94s1,4.43-1.42,4.46l-23.72.31Z" fill="url(#gradient_14)"/>
        </g>
        <g>
            <path d="M2297.38,959.66h0a8.54,8.54,0,0,1-2.54,11.82l-111.57,72.07a8.55,8.55,0,0,1-11.82-2.54h0a8.54,8.54,0,0,1,2.54-11.82l111.57-72.07A8.55,8.55,0,0,1,2297.38,959.66Z" fill="#1e1e1e"/>
            <path d="M2287.8,956.88a7.54,7.54,0,1,1,8.19,12.66l-113,73a8.76,8.76,0,0,1-12.11-2.61,6.31,6.31,0,0,1,1.88-8.74Z" fill="url(#gradient_15)"/>
            <path d="M2299.59,961.23h0a5,5,0,0,1-1.49,6.95l-118.29,76.41a5,5,0,0,1-7-1.49h0a5,5,0,0,1,1.5-6.95l118.29-76.42A5,5,0,0,1,2299.59,961.23Z" fill="#2d2d2d"/>
        </g>
        <g>
            <path d="M2701.39,698.69h0a8.55,8.55,0,0,1-2.54,11.82L2471.48,857.38a8.56,8.56,0,0,1-11.82-2.54h0A8.55,8.55,0,0,1,2462.2,843l227.37-146.87A8.56,8.56,0,0,1,2701.39,698.69Z" fill="#1e1e1e"/>
            <path d="M2691.8,695.9a7.54,7.54,0,0,1,8.19,12.67l-228,147.3a8.76,8.76,0,0,1-12.11-2.6,6.32,6.32,0,0,1,1.88-8.75Z" fill="url(#gradient_16)"/>
            <path d="M2703.6,700.26h0a5,5,0,0,1-1.49,7L2468.83,857.9a5,5,0,0,1-7-1.5h0a5,5,0,0,1,1.5-6.95l233.27-150.69A5,5,0,0,1,2703.6,700.26Z" fill="#2d2d2d"/>
        </g>
        <path d="M2933.68,450.3a40.49,40.49,0,0,0,.39-9.33c-2.06-15.93-17.79-29-34.52-38.17L2080.07,30.37a159.18,159.18,0,0,0-147.4,8.2L1723.19,163.51c-53.5,360.5-114.34,771.61-176.06,1189.05l1364.7-872.65C2925.13,469.38,2931.75,459.45,2933.68,450.3Z" opacity="0.4" fill="url(#gradient_17)"/>
        <path d="M2956.83,415.7c-7-7.16-16.68-13.71-28.68-19.39C2686.36,281.79,2162.44,44,2087,13.52c-64.55-26.1-130.2-9.85-166.8,12C1640,192.71,1222.43,442.09,854.05,662.12L830.8,676C444.13,907,110.19,1106.42,71.55,1129.28c-12.5,7.4-22.42,15.11-29.67,23-78.45,82-23.32,177.62,22.77,202.39,190.25,102.22,593.48,317.55,767.43,410.89,160.33,86,226.82,51,314.28-5.43C1695,1406.39,2822.05,678.81,2940.65,598.6,3036.25,534,2997,451.91,2956.83,415.7Zm-38,63.87-1825.79,1172c-47.17,30.17-117.1,33.57-166.73,7.63L91.72,1221.11c-32.19-16.84-35-70.4-3.76-89L1930.18,35.91c44.81-26.73,104-30,151.48-8.45l831.62,377.65C2941.81,418.07,2945.26,462.69,2918.86,479.57Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
    </g>
</svg>
