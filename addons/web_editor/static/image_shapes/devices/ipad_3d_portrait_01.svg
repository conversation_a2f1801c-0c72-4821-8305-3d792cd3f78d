<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1290 1364" data-forced-size="true" width="1290" height="1364" data-img-aspect-ratio="3:4" data-img-perspective="[[2.94, 2.28], [70.61, 3.63], [97.87, 88.23], [20.92, 96.29]]">
    <defs>
        <linearGradient id="gradient_01" x1="563.48" y1="-86.23" x2="735.07" y2="1419.18" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.2" stop-color="#8a8a8a"/>
            <stop offset="0.39" stop-color="#a1a1a1"/>
            <stop offset="0.48" stop-color="#8a8a8a"/>
            <stop offset="0.54" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="gradient_02" x1="1749.58" y1="-1024.44" x2="1752" y2="-1030.71" gradientTransform="matrix(1.46, 0.6, 0.79, -1.92, -950.06, -1713.99)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <linearGradient id="gradient_03" x1="77.15" y1="584.07" x2="1250.54" y2="112.11" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
       <radialGradient id="gradient_04" cx="483.61" cy="25.66" r="3.91" gradientTransform="translate(687.21 -418.63) rotate(110.94)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#6d7f86"/>
            <stop offset="0.05" stop-color="#5c6c76"/>
            <stop offset="0.15" stop-color="#414e5b"/>
            <stop offset="0.26" stop-color="#2b3445"/>
            <stop offset="0.37" stop-color="#1a2134"/>
            <stop offset="0.51" stop-color="#0e1328"/>
            <stop offset="0.68" stop-color="#070b21"/>
            <stop offset="1" stop-color="#05091f"/>
        </radialGradient>
        <clipPath id="screen_path">
            <polygon points="48.41 19.87 882.07 34.97 926.75 59.49 1270.57 1186.29 1243.32 1228.24 301.78 1333.06 242.39 1290.63 27.16 27.61 48.41 19.87"/>
        </clipPath>
        <path id="filterPath" d="M0.0375,0.0146,0.6838,0.0257l0.0346,0.018,0.2665,0.8261-0.0211,0.0308L0.2339,0.9773l-0.046-0.0311L0.0211,0.0202Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1287.72,1180.13C1281.54,1159.43,948.33,79.37,943.24,64.3s-30-42.87-62.68-43.41c0,0-817-20.7-829.95-20.89S24.26,6.36,15.72,17.08-3,51,.83,71.38,209,1263.47,216.67,1306.35c5.91,32.83,44.88,61.57,83.21,57.21,16.91-1.92,918.44-105.88,934.43-108.79s25.43-8.89,25.43-8.89C1288.45,1229.71,1293.9,1200.83,1287.72,1180.13Zm-55.41,26.33c-22.9,3.09-895,99.53-915.71,101.89s-48-1.82-55.05-40.86S46.91,79.65,44.07,64.66C40.16,44.05,49.7,31.6,66.6,31.79S884.37,49,884.37,49c12.9.36,29.44,9.08,36.34,32.51s328.49,1076.61,331.4,1087.68S1255.2,1203.38,1232.31,1206.46Z" fill="url(#gradient_01)"/>
        <path d="M1287.72,1180.13C1281.54,1159.43,948.33,79.37,943.24,64.3s-30-42.87-62.68-43.41c0,0-817-20.7-829.95-20.89S24.26,6.36,15.72,17.08h0A68.07,68.07,0,0,0,8.23,29.25c-1.9,5.55-3,13.1-1.5,22.88C10.55,76.64,222.46,1275.7,225.85,1294.64c6.81,38,42.42,65.2,85.66,60.75,50.18-5.16,935.15-108.43,942.6-109.33,3.78-.46,9.85-2.75,15.87-7.27A55.71,55.71,0,0,0,1287.72,1180.13ZM1248,1239.88c-35.7,4.91-918.43,106.06-936.42,108-47.29,5.13-74.54-27-79.26-52.26C224,1251.34,15.1,70.12,12.73,55.12,8.91,31,24.17,5,50.61,4.86,88.9,4.66,887.46,25,887.46,25c12.63.36,43.79,16.61,51.6,39S1278.37,1163.6,1283.68,1180.9,1283.68,1235,1248,1239.88Z" fill="#fff" opacity="0.5"/>
        <path d="M1283.68,1180.9C1278.37,1163.6,946.88,86.45,939.06,64s-39-38.68-51.6-39c0,0-798.56-20.32-836.85-20.12C24.17,5,8.91,31,12.73,55.12,15.1,70.12,224,1251.34,232.3,1295.64c4.72,25.24,32,57.39,79.26,52.26,18-2,900.72-103.11,936.42-108S1289,1198.2,1283.68,1180.9Zm-51.37,25.56c-22.9,3.09-895,99.53-915.71,101.89s-48-1.82-55.05-40.86S46.91,79.65,44.07,64.66C40.16,44.05,49.7,31.6,66.6,31.79S884.37,49,884.37,49c12.9.36,29.44,9.08,36.34,32.51s328.49,1076.61,331.4,1087.68S1255.2,1203.38,1232.31,1206.46Z"/>
        <g id="details">
            <g>
                <path d="M208.34,1224.54c.54.07,1,1.52,1,3.32s-.47,3.18-1,3.11-1-1.56-1-3.32S207.8,1224.47,208.34,1224.54Z"/>
                <path d="M208.3,1231c.54.07,1-1.31,1-3.11L208,1230.7A.62.62,0,0,0,208.3,1231Z" fill="#656565" fill-rule="evenodd"/>
                <path d="M207.58,1229.84c.18.67.42,1.09.72,1.13l.81-3.27-.24-.46Z" fill="#fff" fill-rule="evenodd"/>
                <path d="M208.75,1225.87c.32,0,.59.93.58,2s-.27,1.92-.62,1.89-.59-.93-.58-2S208.4,1225.83,208.75,1225.87Z" fill="#0d0d0d"/>
                <path d="M210.3,1223.77c.47,2.58,1.22,10.17-1.07,10.56-2,.34-3.11-5.76-3.53-8.42-2.94-18.66-14.2-80.81-14.7-84.49s-.86-7.52,1.08-8.68,2.78,3.8,3.28,6.23S207.05,1205.27,210.3,1223.77Z" fill="none" stroke="#0d0d0d" stroke-miterlimit="10" stroke-width="0.29"/>
                <path d="M208.8,1234.33c.39.27.94,0,1.19-.56" fill="none" stroke="#ededed" stroke-linecap="round" stroke-width="0.47"/>
            </g>
            <g>
                <path d="M389.07,1348.72a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a12,12,0,0,0,5.19-1.71C391.84,1349,391,1348.55,389.07,1348.72Z" fill="#000102"/>
                <path d="M385.74,1351.4c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,385.74,1351.4Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M389,1350.67a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M388,1349.06a11,11,0,0,0-4.59,1.43c-.75.64.07,1,1.86.78a10.31,10.31,0,0,0,4.33-1.39C390.31,1349.27,389.59,1348.9,388,1349.06Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M401,1347.34a12.48,12.48,0,0,0-5.56,1.75c-1,.77.07,1.18,2.22,1a11.75,11.75,0,0,0,5.2-1.71C403.77,1347.62,402.94,1347.18,401,1347.34Z" fill="#000102"/>
                <path d="M397.66,1350c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,397.66,1350Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M400.93,1349.29a15.25,15.25,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M399.88,1347.69a10.86,10.86,0,0,0-4.59,1.42c-.76.64.07,1,1.86.79a10.26,10.26,0,0,0,4.33-1.4C402.24,1347.89,401.51,1347.52,399.88,1347.69Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M412.92,1346a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.89,11.89,0,0,0,5.19-1.7C415.69,1346.25,414.87,1345.8,412.92,1346Z" fill="#000102"/>
                <path d="M409.59,1348.65c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,409.59,1348.65Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M412.85,1347.92a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M411.8,1346.31a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C414.16,1346.52,413.44,1346.15,411.8,1346.31Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M424.85,1344.59a12.61,12.61,0,0,0-5.56,1.74c-1,.78.07,1.19,2.22,1a11.88,11.88,0,0,0,5.2-1.71C427.62,1344.87,426.79,1344.42,424.85,1344.59Z" fill="#000102"/>
                <path d="M421.51,1347.27c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,421.51,1347.27Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M424.78,1346.54a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M423.73,1344.93a11,11,0,0,0-4.59,1.43c-.76.64.07,1,1.86.78a10.27,10.27,0,0,0,4.33-1.39C426.09,1345.14,425.36,1344.77,423.73,1344.93Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M436.77,1343.22a12.36,12.36,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.75,11.75,0,0,0,5.19-1.71C439.54,1343.5,438.72,1343.05,436.77,1343.22Z" fill="#000102"/>
                <path d="M433.44,1345.89c-2.15.26-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,433.44,1345.89Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M436.7,1345.16a14.57,14.57,0,0,1-3.21.73l-1.35-1.08,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M435.65,1343.56a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C438,1343.76,437.29,1343.4,435.65,1343.56Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M448.7,1341.84a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a12,12,0,0,0,5.2-1.71C451.47,1342.12,450.64,1341.67,448.7,1341.84Z" fill="#000102"/>
                <path d="M445.36,1344.52c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,445.36,1344.52Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M448.63,1343.79a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M447.58,1342.18a11,11,0,0,0-4.59,1.43c-.76.64.07,1,1.86.78a10.41,10.41,0,0,0,4.33-1.39C449.94,1342.39,449.21,1342,447.58,1342.18Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M460.62,1340.46a12.61,12.61,0,0,0-5.55,1.74c-1,.78.07,1.19,2.22,1a11.75,11.75,0,0,0,5.19-1.71C463.39,1340.74,462.57,1340.3,460.62,1340.46Z" fill="#000102"/>
                <path d="M457.29,1343.14c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,457.29,1343.14Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M460.55,1342.41a15.16,15.16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M459.5,1340.81a10.86,10.86,0,0,0-4.59,1.42c-.75.64.07,1,1.86.79a10.16,10.16,0,0,0,4.33-1.4C461.86,1341,461.14,1340.64,459.5,1340.81Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M472.55,1339.09a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.89,11.89,0,0,0,5.2-1.7C475.32,1339.37,474.49,1338.92,472.55,1339.09Z" fill="#000102"/>
                <path d="M469.21,1341.77c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,469.21,1341.77Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M472.48,1341a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M471.43,1339.43a10.71,10.71,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C473.79,1339.64,473.06,1339.27,471.43,1339.43Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M484.47,1337.71a12.61,12.61,0,0,0-5.55,1.74c-1,.78.07,1.19,2.22,1a11.83,11.83,0,0,0,5.19-1.71C487.24,1338,486.42,1337.54,484.47,1337.71Z" fill="#000102"/>
                <path d="M481.14,1340.39c-2.15.25-3.18-.16-2.22-1l3.65.72A8.05,8.05,0,0,1,481.14,1340.39Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M484.4,1339.66a15.8,15.8,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M483.35,1338.05a11,11,0,0,0-4.59,1.43c-.75.64.07,1,1.86.78a10.17,10.17,0,0,0,4.33-1.39C485.71,1338.26,485,1337.89,483.35,1338.05Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M496.4,1336.34a12.36,12.36,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.75,11.75,0,0,0,5.2-1.71C499.17,1336.61,498.34,1336.17,496.4,1336.34Z" fill="#000102"/>
                <path d="M493.06,1339c-2.15.26-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,493.06,1339Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M496.33,1338.28a15.25,15.25,0,0,1-3.22.73l-1.35-1.08,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M495.28,1336.68a10.71,10.71,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C497.64,1336.88,496.91,1336.52,495.28,1336.68Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M508.32,1335a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a12,12,0,0,0,5.19-1.71C511.09,1335.24,510.27,1334.79,508.32,1335Z" fill="#000102"/>
                <path d="M505,1337.64c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,505,1337.64Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M508.25,1336.91a15.8,15.8,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M507.2,1335.3a10.85,10.85,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C509.56,1335.51,508.84,1335.14,507.2,1335.3Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M520.25,1333.58a12.61,12.61,0,0,0-5.56,1.74c-1,.78.07,1.19,2.22,1a11.75,11.75,0,0,0,5.2-1.71C523,1333.86,522.19,1333.42,520.25,1333.58Z" fill="#000102"/>
                <path d="M516.91,1336.26c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,516.91,1336.26Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M520.18,1335.53a15.25,15.25,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M519.13,1333.93a10.86,10.86,0,0,0-4.59,1.42c-.76.64.07,1,1.86.79a10.26,10.26,0,0,0,4.33-1.4C521.49,1334.13,520.76,1333.76,519.13,1333.93Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M532.17,1332.21a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.7,11.7,0,0,0,5.19-1.71C534.94,1332.49,534.12,1332,532.17,1332.21Z" fill="#000102"/>
                <path d="M528.84,1334.89c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,528.84,1334.89Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M532.1,1334.16a15.8,15.8,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M531.05,1332.55a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C533.41,1332.76,532.69,1332.39,531.05,1332.55Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M544.1,1330.83a12.61,12.61,0,0,0-5.56,1.74c-1,.78.07,1.19,2.22,1a11.88,11.88,0,0,0,5.2-1.71C546.87,1331.11,546,1330.66,544.1,1330.83Z" fill="#000102"/>
                <path d="M540.76,1333.51c-2.15.25-3.18-.16-2.22-1l3.65.72A8.05,8.05,0,0,1,540.76,1333.51Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M544,1332.78a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M543,1331.17a11,11,0,0,0-4.59,1.43c-.76.64.07,1,1.86.78a10.27,10.27,0,0,0,4.33-1.39C545.34,1331.38,544.61,1331,543,1331.17Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M556,1329.46a12.36,12.36,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.7,11.7,0,0,0,5.19-1.71C558.79,1329.73,558,1329.29,556,1329.46Z" fill="#000102"/>
                <path d="M552.69,1332.13c-2.15.26-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,552.69,1332.13Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M556,1331.4a15.16,15.16,0,0,1-3.21.73l-1.35-1.08,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M554.9,1329.8a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C557.26,1330,556.54,1329.64,554.9,1329.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M568,1328.08a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.89,11.89,0,0,0,5.2-1.7C570.72,1328.36,569.89,1327.91,568,1328.08Z" fill="#000102"/>
                <path d="M564.61,1330.76c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,564.61,1330.76Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M567.88,1330a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M566.83,1328.42a10.85,10.85,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C569.19,1328.63,568.46,1328.26,566.83,1328.42Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M579.87,1326.7a12.61,12.61,0,0,0-5.55,1.74c-1,.78.07,1.19,2.22,1a11.7,11.7,0,0,0,5.19-1.71C582.64,1327,581.82,1326.54,579.87,1326.7Z" fill="#000102"/>
                <path d="M576.54,1329.38c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,576.54,1329.38Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M579.8,1328.65a15.16,15.16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M578.75,1327.05a10.86,10.86,0,0,0-4.59,1.42c-.75.64.07,1,1.86.78a10,10,0,0,0,4.33-1.39C581.11,1327.25,580.39,1326.88,578.75,1327.05Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M772.68,1304.87a43,43,0,0,1,11.49-2.91l30.57-3.45c4.16-.47,6.93.26,5.29,1.49s-5.82,2.42-10.29,3l-34.06,3.9C771,1307.37,769.53,1306.31,772.68,1304.87Z" fill="#3d3d3d"/>
                <path d="M771,1305.05a41.22,41.22,0,0,1,11.25-2.82l32.19-3.63c4.05-.47,6.7.26,5.12,1.42s-5.64,2.37-10,2.83l-35.75,4.07C769.28,1307.48,767.9,1306.45,771,1305.05Z" fill="url(#gradient_02)"/>
                <path d="M772.2,1305a37.15,37.15,0,0,1,10-2.55l30.32-3.48c3.68-.41,6.09.22,4.51,1.29s-5.09,2.11-8.89,2.55l-33.34,3.87C770.73,1307.18,769.47,1306.27,772.2,1305Z" fill="#131313"/>
            </g>
            <g>
                <path d="M996.3,1278.42a12.48,12.48,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.75,11.75,0,0,0,5.19-1.71C999.07,1278.7,998.25,1278.25,996.3,1278.42Z" fill="#000102"/>
                <path d="M993,1281.09c-2.15.26-3.18-.16-2.22-1l3.65.73A8.06,8.06,0,0,1,993,1281.09Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M996.23,1280.37a15.29,15.29,0,0,1-3.21.72l-1.35-1.08,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M995.18,1278.76a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C997.54,1279,996.82,1278.6,995.18,1278.76Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1008.23,1277a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.88,11.88,0,0,0,5.2-1.71C1011,1277.32,1010.17,1276.87,1008.23,1277Z" fill="#000102"/>
                <path d="M1004.89,1279.72c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1004.89,1279.72Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1008.16,1279a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1007.11,1277.38a11,11,0,0,0-4.59,1.43c-.76.64.07,1,1.86.78a10.27,10.27,0,0,0,4.33-1.39C1009.47,1277.59,1008.74,1277.22,1007.11,1277.38Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1020.15,1275.66a12.48,12.48,0,0,0-5.55,1.75c-1,.77.07,1.18,2.22,1a11.75,11.75,0,0,0,5.19-1.71C1022.92,1275.94,1022.1,1275.5,1020.15,1275.66Z" fill="#000102"/>
                <path d="M1016.82,1278.34c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1016.82,1278.34Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1020.08,1277.61a15.3,15.3,0,0,1-3.21.73l-1.35-1.08,1.22-.3Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1019,1276a10.86,10.86,0,0,0-4.59,1.42c-.75.64.07,1,1.86.79a10.16,10.16,0,0,0,4.33-1.4C1021.39,1276.21,1020.67,1275.85,1019,1276Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1032.08,1274.29a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.89,11.89,0,0,0,5.2-1.7C1034.85,1274.57,1034,1274.12,1032.08,1274.29Z" fill="#000102"/>
                <path d="M1028.74,1277c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1028.74,1277Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1032,1276.24a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1031,1274.63a10.71,10.71,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C1033.32,1274.84,1032.59,1274.47,1031,1274.63Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1044,1272.91a12.61,12.61,0,0,0-5.55,1.74c-1,.78.07,1.19,2.22,1a11.88,11.88,0,0,0,5.19-1.71C1046.77,1273.19,1046,1272.75,1044,1272.91Z" fill="#000102"/>
                <path d="M1040.67,1275.59c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1040.67,1275.59Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1043.93,1274.86a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1042.88,1273.26a10.86,10.86,0,0,0-4.59,1.42c-.75.64.07,1,1.86.78a10.17,10.17,0,0,0,4.33-1.39C1045.24,1273.46,1044.52,1273.09,1042.88,1273.26Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1055.93,1271.54a12.36,12.36,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.75,11.75,0,0,0,5.2-1.71C1058.7,1271.82,1057.87,1271.37,1055.93,1271.54Z" fill="#000102"/>
                <path d="M1052.59,1274.21c-2.15.26-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1052.59,1274.21Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1055.86,1273.48a14.66,14.66,0,0,1-3.22.73l-1.35-1.08,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1054.81,1271.88a10.71,10.71,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C1057.17,1272.08,1056.44,1271.72,1054.81,1271.88Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1067.85,1270.16a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a12,12,0,0,0,5.19-1.71C1070.62,1270.44,1069.8,1270,1067.85,1270.16Z" fill="#000102"/>
                <path d="M1064.52,1272.84c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1064.52,1272.84Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1067.78,1272.11a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1066.73,1270.5a11,11,0,0,0-4.59,1.43c-.75.64.07,1,1.86.78a10.31,10.31,0,0,0,4.33-1.39C1069.09,1270.71,1068.37,1270.34,1066.73,1270.5Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1079.78,1268.78a12.48,12.48,0,0,0-5.56,1.75c-1,.77.07,1.18,2.22,1a11.75,11.75,0,0,0,5.2-1.71C1082.55,1269.06,1081.72,1268.62,1079.78,1268.78Z" fill="#000102"/>
                <path d="M1076.44,1271.46c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1076.44,1271.46Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1079.71,1270.73a15.25,15.25,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1078.66,1269.13a10.86,10.86,0,0,0-4.59,1.42c-.76.64.07,1,1.86.79a10.26,10.26,0,0,0,4.33-1.4C1081,1269.33,1080.29,1269,1078.66,1269.13Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1091.7,1267.41a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.89,11.89,0,0,0,5.19-1.7C1094.47,1267.69,1093.65,1267.24,1091.7,1267.41Z" fill="#000102"/>
                <path d="M1088.37,1270.09c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1088.37,1270.09Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1091.63,1269.36a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1090.58,1267.75a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C1092.94,1268,1092.22,1267.59,1090.58,1267.75Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1103.63,1266a12.61,12.61,0,0,0-5.56,1.74c-1,.78.07,1.19,2.22,1a11.88,11.88,0,0,0,5.2-1.71C1106.4,1266.31,1105.57,1265.86,1103.63,1266Z" fill="#000102"/>
                <path d="M1100.29,1268.71c-2.15.25-3.18-.16-2.22-1l3.65.72A8.05,8.05,0,0,1,1100.29,1268.71Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1103.56,1268a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1102.51,1266.37a11,11,0,0,0-4.59,1.43c-.76.64.07,1,1.86.78a10.27,10.27,0,0,0,4.33-1.39C1104.87,1266.58,1104.14,1266.21,1102.51,1266.37Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1115.55,1264.66a12.36,12.36,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a11.75,11.75,0,0,0,5.19-1.71C1118.32,1264.94,1117.5,1264.49,1115.55,1264.66Z" fill="#000102"/>
                <path d="M1112.22,1267.33c-2.15.26-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1112.22,1267.33Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1115.48,1266.6a14.7,14.7,0,0,1-3.21.73l-1.35-1.08,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1114.43,1265a10.71,10.71,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C1116.79,1265.2,1116.07,1264.84,1114.43,1265Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1127.48,1263.28a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a12,12,0,0,0,5.2-1.71C1130.25,1263.56,1129.42,1263.11,1127.48,1263.28Z" fill="#000102"/>
                <path d="M1124.14,1266c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1124.14,1266Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1127.41,1265.23a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1126.36,1263.62a11,11,0,0,0-4.59,1.43c-.76.64.07,1,1.86.78a10.41,10.41,0,0,0,4.33-1.39C1128.72,1263.83,1128,1263.46,1126.36,1263.62Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1139.4,1261.9a12.61,12.61,0,0,0-5.55,1.74c-1,.78.07,1.19,2.22,1a11.75,11.75,0,0,0,5.19-1.71C1142.17,1262.18,1141.35,1261.74,1139.4,1261.9Z" fill="#000102"/>
                <path d="M1136.07,1264.58c-2.15.25-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1136.07,1264.58Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1139.33,1263.85a15.3,15.3,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1138.28,1262.25a10.86,10.86,0,0,0-4.59,1.42c-.75.64.07,1,1.86.79a10.16,10.16,0,0,0,4.33-1.4C1140.64,1262.45,1139.92,1262.08,1138.28,1262.25Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1151.33,1260.53a12.61,12.61,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.89,11.89,0,0,0,5.2-1.7C1154.1,1260.81,1153.27,1260.36,1151.33,1260.53Z" fill="#000102"/>
                <path d="M1148,1263.21c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1148,1263.21Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1151.26,1262.48a15.9,15.9,0,0,1-3.22.73l-1.35-1.09,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1150.21,1260.87a10.71,10.71,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C1152.57,1261.08,1151.84,1260.71,1150.21,1260.87Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1163.25,1259.15a12.61,12.61,0,0,0-5.55,1.74c-1,.78.07,1.19,2.22,1a11.88,11.88,0,0,0,5.19-1.71C1166,1259.43,1165.2,1259,1163.25,1259.15Z" fill="#000102"/>
                <path d="M1159.92,1261.83c-2.15.25-3.18-.16-2.22-1l3.65.72A8.05,8.05,0,0,1,1159.92,1261.83Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1163.18,1261.1a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1162.13,1259.49a11,11,0,0,0-4.59,1.43c-.75.64.07,1,1.86.78a10.17,10.17,0,0,0,4.33-1.39C1164.49,1259.7,1163.77,1259.33,1162.13,1259.49Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1175.18,1257.78a12.36,12.36,0,0,0-5.56,1.74c-1,.77.07,1.19,2.22,1a11.75,11.75,0,0,0,5.2-1.71C1178,1258.05,1177.12,1257.61,1175.18,1257.78Z" fill="#000102"/>
                <path d="M1171.84,1260.45c-2.15.26-3.18-.16-2.22-1l3.65.72A7.24,7.24,0,0,1,1171.84,1260.45Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1175.11,1259.72a15.25,15.25,0,0,1-3.22.73l-1.35-1.08,1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1174.06,1258.12a10.71,10.71,0,0,0-4.59,1.43c-.76.63.07,1,1.86.78a10.26,10.26,0,0,0,4.33-1.4C1176.42,1258.32,1175.69,1258,1174.06,1258.12Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1187.1,1256.4a12.61,12.61,0,0,0-5.55,1.74c-1,.77.07,1.19,2.22,1a12,12,0,0,0,5.19-1.71C1189.87,1256.68,1189.05,1256.23,1187.1,1256.4Z" fill="#000102"/>
                <path d="M1183.77,1259.08c-2.15.25-3.18-.17-2.22-1l3.65.73A9.08,9.08,0,0,1,1183.77,1259.08Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1187,1258.35a16,16,0,0,1-3.21.73l-1.35-1.09,1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1186,1256.74a10.85,10.85,0,0,0-4.59,1.43c-.75.63.07,1,1.86.78a10.16,10.16,0,0,0,4.33-1.4C1188.34,1257,1187.62,1256.58,1186,1256.74Z" fill="#0a0e0e"/>
            </g>
            <g>
                <ellipse cx="490.39" cy="23.85" rx="4.95" ry="6.85" transform="translate(82.84 297.63) rotate(-36.68)" fill="#1a1c1c"/>
                <ellipse cx="490.4" cy="23.86" rx="3.25" ry="4.47" transform="translate(82.83 297.63) rotate(-36.67)" fill="url(#gradient_04)"/>
            </g>
        </g>
        <path d="M1287.72,1180.13c-6.18-20.7-339.39-1100.76-344.48-1115.84s-30-42.86-62.68-43.4c0,0-817-20.7-829.95-20.89S24.26,6.36,15.72,17.08-3,51,.83,71.38,209,1263.47,216.67,1306.35c5.91,32.83,44.88,61.57,83.21,57.21,16.91-1.92,918.44-105.88,934.43-108.79s25.43-8.9,25.43-8.9C1288.45,1229.71,1293.9,1200.83,1287.72,1180.13ZM486.29,18.36c2.19-1.64,5.8-.51,8.06,2.52s2.33,6.82.14,8.46-5.8.5-8.06-2.53S484.1,20,486.29,18.36Zm760.93,1193.72c-22.89,3.09-916.19,108.16-936.91,110.52s-48-1.82-55.05-40.86S37.09,71.66,34.24,56.67c-3.9-20.61,5.64-33,22.53-32.87S890.69,39,890.69,39c12.9.37,29.43,9.09,36.33,32.51s337.1,1092.3,340,1103.38S1270.12,1209,1247.22,1212.08Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
        <path d="M687.84,44.86C449.54,39.79,78.05,31.91,66.6,31.79,49.7,31.6,40.16,44.05,44.07,64.66c2.16,11.43,123.43,682.51,184,1017.61Z" opacity="0.4" fill="url(#gradient_03)"/>
    </g>
</svg>
