<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 800 2479" data-forced-size="true" width="800" height="2481" data-img-aspect-ratio="9:20" data-img-perspective="[[3.41, 7.92], [86.52, 0.71], [87.31, 97.57], [1.76, 97.16]]">
    <defs>
        <mask id="mask" x="0.88" y="0.17" width="799.04" height="2476.96" maskUnits="userSpaceOnUse">
            <path d="M736.81,9.79c-41.14-19.52-75.83-4-104.34.55,47.66,0,79.91,14.72,79.91,86.12,0,723.85,10.89,2188.51,9.67,2256.42s-46.82,97.61-70.56,98.1l-567.66-8.64c-31,0-72.78-18.84-82.95-105.39,4.07,109.46,67.18,123.72,85,124.2,182.25,4.91,555.11,14.94,588.51,15.76,42.05,1,66,0,92.65-33,23.41-29,23.41-67.16,23.42-90.95v0c.17-736.38-8.47-2207-8.87-2244.83C781.1,62.1,774,27.42,736.81,9.79Z" fill="#242424"/>
        </mask>
        <mask id="mask-2" x="0" y="0.17" width="790.47" height="2478.83" maskUnits="userSpaceOnUse">
            <path d="M736.81,9.79c-41.14-19.52-75.83-4-104.34.55,47.66,0,79.91,14.72,79.91,86.12,0,723.85,10.89,2188.51,9.67,2256.42s-46.82,97.61-70.56,98.1l-567.66-8.64c-31,0-72.78-18.84-82.95-105.39,4.07,109.46,67.18,123.72,85,124.2,182.25,4.91,555.11,14.94,588.51,15.76,42.05,1,66,0,92.65-33,23.41-29,23.41-67.16,23.42-90.95v0c.17-736.38-8.47-2207-8.87-2244.83C781.1,62.1,774,27.42,736.81,9.79Z" fill="#242424"/>
        </mask>
        <mask id="mask-3" x="313.37" y="161.5" width="23.41" height="43.78" maskUnits="userSpaceOnUse">
            <path d="M313.47,182.52c-.87,12.08,3.62,22.27,10,22.75s12.3-8.92,13.17-21-3.62-22.26-10-22.75S314.34,170.44,313.47,182.52Z" fill="#121212"/>
        </mask>
        <mask id="mask-4" x="321.51" y="166.6" width="15.27" height="32.58" maskUnits="userSpaceOnUse">
            <path d="M321.58,182.24c-.53,9,2.2,16.57,6.1,16.93s7.48-6.64,8-15.63-2.2-16.57-6.1-16.93S322.1,173.25,321.58,182.24Z" fill="#121212"/>
        </mask>
        <mask id="mask-5" x="321.51" y="166.6" width="14.25" height="32.58" maskUnits="userSpaceOnUse">
            <path d="M321.58,182.24c-.53,9,2.2,16.57,6.1,16.93s7.48-6.64,8-15.63-2.2-16.57-6.1-16.93S322.1,173.25,321.58,182.24Z" fill="#141517"/>
        </mask>
        <linearGradient id="gradient_01" x1="722.56" y1="-1235.89" x2="760.22" y2="-1235.89" gradientTransform="matrix(1, 0, 0, -1, 0, -7.36)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#cacaca"/>
            <stop offset="0.21" stop-color="#d6d6d6"/>
            <stop offset="0.36" stop-color="#bebebe"/>
            <stop offset="1" stop-color="#4e4e4e"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="1765.55" cy="308.59" r="0.99" gradientTransform="matrix(0, 35.91, 644.7, 0, -198554.86, -60960.49)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#878787"/>
            <stop offset="0.8" stop-color="#444"/>
        </radialGradient>
        <radialGradient id="gradient_03" cx="363.24" cy="1231.66" r="901.88" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#c4c4c4"/>
            <stop offset="0.1" stop-color="#8c8c8c"/>
            <stop offset="0.12" stop-color="#fff"/>
            <stop offset="0.13" stop-color="#777"/>
            <stop offset="0.37" stop-color="#5f5f5f"/>
            <stop offset="0.38" stop-color="#fff"/>
            <stop offset="0.43" stop-color="#434343"/>
            <stop offset="0.49" stop-color="#292929"/>
            <stop offset="0.58" stop-color="#656565"/>
            <stop offset="0.62" stop-color="#515151"/>
            <stop offset="0.94" stop-color="#696969"/>
            <stop offset="1" stop-color="#c4c4c4"/>
        </radialGradient>
        <linearGradient id="gradient_04" x1="750.55" y1="-964.5" x2="750.55" y2="-1118.25" gradientTransform="matrix(1, 0, 0, -1, 0, -7.36)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#727272"/>
            <stop offset="1" stop-color="#727272"/>
        </linearGradient>
        <radialGradient id="gradient_05" cx="1761.2" cy="130.48" r="0.99" gradientTransform="matrix(0, 77.46, 9.74, 0, -520.78, -135392.12)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#292929"/>
            <stop offset="0.14" stop-color="#929292"/>
            <stop offset="0.89" stop-color="#929292"/>
        </radialGradient>
        <linearGradient id="gradient_06" x1="752.16" y1="-964.7" x2="748.92" y2="-1116.77" gradientTransform="matrix(1, 0, 0, -1, 0, -7.36)" gradientUnits="userSpaceOnUse">
            <stop offset="0.02" stop-color="#6e6e6e"/>
            <stop offset="0.02" stop-color="#1f1f1f"/>
            <stop offset="0.02" stop-color="#2c2c2c"/>
            <stop offset="0.03" stop-color="#646464"/>
            <stop offset="0.94" stop-color="#3e3e3e"/>
            <stop offset="0.95" stop-color="#2c2c2c"/>
        </linearGradient>
        <linearGradient id="gradient_04-2" x1="750.55" y1="-477.79" x2="750.55" y2="-771.04" xlink:href="#gradient_04"/>
        <radialGradient id="gradient_05-2" cx="1759.32" cy="130.48" r="0.99" gradientTransform="matrix(0, 147.74, 9.74, 0, -520.77, -259307.8)" xlink:href="#gradient_05"/>
        <linearGradient id="gradient_07" x1="756.42" y1="-478.34" x2="744.65" y2="-768.03" gradientTransform="matrix(1, 0, 0, -1, 0, -7.36)" gradientUnits="userSpaceOnUse">
            <stop offset="0.02" stop-color="#6e6e6e"/>
            <stop offset="0.02" stop-color="#1f1f1f"/>
            <stop offset="0.02" stop-color="#2c2c2c"/>
            <stop offset="0.03" stop-color="#646464"/>
            <stop offset="0.97" stop-color="#3e3e3e"/>
            <stop offset="0.98" stop-color="#2c2c2c"/>
        </linearGradient>
        <linearGradient id="gradient_08" x1="-265.55" y1="1347.5" x2="1788.41" y2="521.36" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="706.08 8.3 76.75 171.12 43.24 195.84 27.94 227.61 22.53 261.39 10 2356.44 48.74 2430.69 724.43 2435.45 737.42 37.45 706.08 8.3"/>
        </clipPath>
        <path id="filterPath" d="M0.9218,0.0151l-0.0163,0.9665-0.8446-0.0019L0.0125,0.9498l0.0157-0.8444,0.0068-0.0136,0.0191-0.0128,0.0419-0.01L0.8826,0.0033Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M736.81,9.79c-41.14-19.52-80.41-5.09-114,2.54,47.65,0,89.57,12.73,89.57,84.13,0,723.85,10.89,2188.51,9.67,2256.41s-46.82,97.62-70.56,98.11l-567.66-8.64c-31,0-72.78-18.84-82.95-105.39,4.07,109.46,67.18,123.72,85,124.2,182.25,4.91,555.11,14.94,588.51,15.76,42.05,1,66,0,92.65-33,23.41-29,23.41-67.16,23.42-90.95v-.05c.09-404.63-2.47-1031-4.87-1519.45,9.23-8.14,9.23-1.53,9.23-48.36-1.87-374.15-5.09-661.87-5.09-678.15C789.74,56,774,27.42,736.81,9.79Z" fill="#1a1a1a"/>
        <g mask="url(#mask)">
            <path d="M769.38,91.25c0-55.5-12.72-68.73-12.72-68.73,23.61,0,37,47.85,37.15,68.73,2.55,741.1,6.11,2170.15,6.11,2218.21,0,85-12.22,88.08-12.22,88.08s-9.16,5.09-9.16-52.95C778.54,1592,769.38,114.06,769.38,91.25Z" fill="#797979"/>
        </g>
        <path d="M749.53,98.37v71.79l10.18,2183.09c.17,11-2.85,33.09-16.29,33.09-8.14.4-10.51-10-10.68-15.28L722.56,94.81c0-8.15,1-22.91,7.63-23.93C745.48,68.53,749.53,90.23,749.53,98.37Z" fill="url(#gradient_01)"/>
        <g mask="url(#mask-2)">
            <path d="M664,2458.12c70.84.81,98.43-5.6,122.15-50.4-19.34,57.53-57.51,71.28-77.87,71.28L82.82,2465.25c-33.09,0-90.6-48.87-81.94-129.83,2,94.2,56,112,81.94,112C247,2450.66,593.19,2457.31,664,2458.12Z" fill="url(#gradient_02)"/>
        </g>
        <path d="M715.32,105.37c-.18-42.92-9.05-115.67-96.7-92S229.43,118.83,85.75,157.48C46.55,168,16.41,214,16.83,268.35S6.54,1663.66.88,2332.45c-.34,40.36,16.46,112.91,82.3,112.91,69.54,0,526.39,9.2,562,9.2,45.29,0,80.41-36.59,80.41-109.34S718.41,823,715.32,105.37ZM627.63,2417.75l-544.9-8.11A69.24,69.24,0,0,1,14.52,2340L27,260.14A83.5,83.5,0,0,1,88.78,180L619.13,37.41A58.06,58.06,0,0,1,692.25,93.3l6.68,2254A70.27,70.27,0,0,1,627.63,2417.75Z" fill="url(#gradient_03)"/>
        <path d="M712.89,110.08c-.18-42.76-10.48-113.22-97.21-89.6S224.47,125.53,82.31,164.05c-34.1,9.24-64,53.76-63.62,107.93S8.51,1662.38,2.91,2328.8c-.34,40.23,17.31,113.54,80.42,113.54,68.8,0,544.57,9.68,558.31,9.17,47.81-1.78,81.43-38.5,81.43-111S715.94,825.22,712.89,110.08ZM627.63,2417.75l-544.9-8.11A69.24,69.24,0,0,1,14.52,2340L27,260.14A83.5,83.5,0,0,1,88.78,180L619.13,37.41A58.06,58.06,0,0,1,692.25,93.3l6.68,2254A70.27,70.27,0,0,1,627.63,2417.75Z"/>
        <path d="M89.09,179.92c-41.22,10.18-61.24,56.93-62.26,89C20.92,230.88,48,184.4,89.09,179.92Z"/>
        <path d="M622.91,36.51c22.39-4.59,62.24,1.28,69.37,70.52C697.36,78.52,680.72,7.59,622.91,36.51Z"/>
        <path d="M117.93,2445.4c.82,13.11,15.62,13.85,23.41,17.24l-18.07-.49c-11.38,0-14.84-7.89-17.55-16.75Z" fill="#2f2f2f" fill-opacity="0.7"/>
        <path d="M607.77,2454.07c.83,14.06,16.53,18,24.43,21.64l-18.32-.51c-11.55,0-16.08-11.83-18.83-21.34Z" fill="#2f2f2f" fill-opacity="0.7"/>
        <path d="M306.29,181.18c-1.22,16.55,5.06,30.49,14,31.16s17.23-12.22,18.45-28.76-5.07-30.5-14-31.16S307.5,164.64,306.29,181.18Z" fill="#0c0c0c"/>
        <path d="M313.47,182.52c-.87,12.08,3.62,22.27,10,22.75s12.3-8.92,13.17-21-3.62-22.26-10-22.75S314.34,170.44,313.47,182.52Z" fill="#121212"/>
        <g mask="url(#mask-3)">
            <g>
                <path d="M321.58,182.24c-.53,9,2.2,16.57,6.1,16.93s7.48-6.64,8-15.63-2.2-16.57-6.1-16.93S322.1,173.25,321.58,182.24Z" fill="#141517"/>
                <path d="M330.14,190c-.81,1.19-3.06,3.42-5.58,2.76C326.49,193.71,330.3,194.44,330.14,190Z" fill="#3f3f3f"/>
                <g mask="url(#mask-4)">
                    <path d="M334.65,169.65c0,13.24.6,27.49-12.12,27.87C329.51,201.31,341.71,201.05,334.65,169.65Z" fill="#1b191a"/>
                </g>
            </g>
        </g>
        <g mask="url(#mask-5)">
            <g>
                <path d="M322.06,176.14c-.3,2.25.95,4.18,2.81,4.31s3.6-1.58,3.9-3.82-.95-4.18-2.81-4.31S322.36,173.9,322.06,176.14Z" fill="#1a2224"/>
                <path d="M324.56,175.65a1.53,1.53,0,1,0,1.53-1.53A1.53,1.53,0,0,0,324.56,175.65Z" fill="#566464"/>
            </g>
        </g>
        <g>
            <path d="M750.55,958.16h0a8.65,8.65,0,0,1,8.65,8.65v134.41a8.66,8.66,0,0,1-8.65,8.66h0a8.66,8.66,0,0,1-8.65-8.66V966.81A8.65,8.65,0,0,1,750.55,958.16Z" stroke-width="2.04" fill="url(#gradient_04)" stroke="url(#gradient_05)"/>
            <path d="M742.92,964.77a7.63,7.63,0,0,1,15.26,0V1101a8.86,8.86,0,0,1-8.86,8.86,6.4,6.4,0,0,1-6.4-6.4Z" fill="url(#gradient_06)"/>
            <path d="M753.1,957.14h0a5.08,5.08,0,0,1,5.08,5.09v142.51a5.08,5.08,0,0,1-5.08,5.09h0a5.09,5.09,0,0,1-5.09-5.09V962.23A5.09,5.09,0,0,1,753.1,957.14Z" fill="#929292"/>
        </g>
        <g>
            <rect x="741.9" y="471.44" width="17.3" height="291.21" rx="8.65" stroke-width="2.04" fill="url(#gradient_04-2)" stroke="url(#gradient_05-2)"/>
            <path d="M742.92,478.06a7.63,7.63,0,0,1,15.26,0V752.77a8.86,8.86,0,0,1-8.86,8.87,6.4,6.4,0,0,1-6.4-6.4Z" fill="url(#gradient_07)"/>
            <path d="M753.1,470.43h0a5.07,5.07,0,0,1,5.08,5.08v281a5.08,5.08,0,0,1-5.08,5.09h0a5.09,5.09,0,0,1-5.09-5.09v-281A5.08,5.08,0,0,1,753.1,470.43Z" fill="#929292"/>
        </g>
        <path d="M718.87,848.2c36.8-.41,59.94,7.39,66.85,11.19l.1,20.36c-18.2-7.86-52.2-9.48-66.86-9.31Z" fill="#505050" fill-opacity="0.7"/>
        <path d="M619.13,37.41l-530,142.51h0l-.73.2A83.18,83.18,0,0,0,80,182.9c-34.41,14-51.57,54.78-53.1,84.29L15,2260.28,657.24,40.2A57.59,57.59,0,0,0,619.13,37.41Z" opacity="0.4" fill="url(#gradient_08)"/>
        <path d="M783.62,2412.38c6.83-21.56,6.85-43.68,6.85-59.46v-.05c.09-404.63-2.47-1031-4.87-1519.45,9.23-8.14,9.23-1.53,9.23-48.36-1.87-374.15-5.09-661.87-5.09-678.15C789.74,56,774,27.42,736.81,9.79c-41.14-19.52-80.41-5.09-114,2.54h0c-1.38.34-2.78.69-4.2,1.08C531,37.11,229.43,118.83,85.75,157.48,46.55,168,16.41,214,16.83,268.35S6.54,1663.66.88,2332.45c0,1.19,0,2.42,0,3.67,0-.24,0-.46,0-.7-.06.55-.11,1.09-.16,1.63l.15-.1c.11,2.93.27,5.77.46,8.57h0c.07,1.05.15,2.09.23,3.12l0,.39q.39,4.69.93,9.14c-.1-.76-.19-1.54-.28-2.32,10.2,92.23,66.89,104.85,83.64,105.3,182.25,4.91,555.11,14.94,588.51,15.76,42.05,1,66,0,92.65-33a144.12,144.12,0,0,0,9.43-14.06C779.82,2423.75,783.62,2412.38,783.62,2412.38ZM27,260.14A83.5,83.5,0,0,1,88.78,180L619.13,37.41A58.06,58.06,0,0,1,692.25,93.3l6.68,2254a70.27,70.27,0,0,1-71.3,70.48l-544.9-8.11A69.24,69.24,0,0,1,14.52,2340Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
    </g>
</svg>
