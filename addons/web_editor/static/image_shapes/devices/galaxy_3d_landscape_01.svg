<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 3000 1819" data-forced-size="true" width="3000" height="1819" data-img-aspect-ratio="20:9" data-img-perspective="[[33.19, -0.22], [99.24, 64.74], [66.26, 93.17], [0.75, 24.08]]">
    <defs>
        <mask id="mask" x="0" y="405.11" width="3000" height="1414.39" maskUnits="userSpaceOnUse">
            <path d="M59.35,598.6c-106.62-72.1-45.46-165.84-2.47-193.49-46.95,41-19.77,66.57,2.47,80.89,525.2,338.15,1573.14,1012,1770.18,1138.66l24.11,15.5c117.11,75.31,210,47.9,239.17,32.1l844-441c16.8-9.38,55.84-39.51,16.8-83.46,85,83,28.66,181.7-18.29,206.93-190.25,102.22-593.48,317.55-767.43,410.89C2074,1816,2037.84,1821.68,1992,1818.93c-62.76-3.75-92-28.9-138.36-58.76C1305,1406.39,178,678.81,59.35,598.6Z" fill="#1f1f1f"/>
        </mask>
         <mask id="mask-2" x="46.97" y="481.56" width="1925.26" height="1247.99" maskUnits="userSpaceOnUse">
            <path d="M77.63,535.39C45,514.64,44.52,494.89,49,481.56,50.45,488,73.19,503.29,87,512.67L1862,1653c6.42,4.44,25.89,18.37,66.21,34.57-19.76,16.79-45.46,7.3-56.82,0C1287.5,1312.4,111.24,556.72,77.63,535.39Z" fill="url(#gradient_01)"/>
        </mask>
        <mask id="mask-3" x="2472.99" y="1475.5" width="130.2" height="101.21" maskUnits="userSpaceOnUse">
            <path d="M2473,1561c.38-13.66,10.46-30,22.51-36.46l86.54-46.44c12.05-6.47,21.51-.63,21.13,13s-10.47,30-22.52,36.46l-86.53,46.45C2482.09,1580.54,2472.62,1574.71,2473,1561Z"/>
        </mask>
        <linearGradient id="gradient_01" x1="744.62" y1="-5623.42" x2="1099.39" y2="-5068.77" gradientTransform="matrix(1, 0, 0, -1, 0, -4157.39)" gradientUnits="userSpaceOnUse">
            <stop offset="0.63" stop-color="#949494"/>
            <stop offset="0.74" stop-color="#e0e0e0"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="1492.06" cy="866.52" r="1376.29" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#696969"/>
            <stop offset="0.18" stop-color="#707072"/>
            <stop offset="0.19" stop-color="#b9b9b9"/>
            <stop offset="0.19" stop-color="#5d5d5d"/>
            <stop offset="0.2"/>
            <stop offset="0.2"/>
            <stop offset="0.21" stop-color="#717171"/>
            <stop offset="0.24" stop-color="#7e7e7e"/>
            <stop offset="0.37" stop-color="#848484"/>
            <stop offset="0.39"/>
            <stop offset="0.4" stop-color="#41413f"/>
            <stop offset="0.66" stop-color="#676767"/>
            <stop offset="0.68" stop-color="#c7c7c7"/>
            <stop offset="0.68"/>
            <stop offset="0.68"/>
            <stop offset="0.69" stop-color="#525252"/>
            <stop offset="0.73" stop-color="#737373"/>
            <stop offset="0.87" stop-color="#747474"/>
            <stop offset="0.88" stop-color="#434343"/>
            <stop offset="0.89"/>
            <stop offset="0.9"/>
            <stop offset="0.91" stop-color="#585858"/>
            <stop offset="0.92" stop-color="#6c6c6c"/>
        </radialGradient>
        <radialGradient id="gradient_03" cx="1444.21" cy="928.23" r="691.34" gradientUnits="userSpaceOnUse">
            <stop offset="0.21" stop-color="#363636"/>
            <stop offset="0.21" stop-color="#f5f5f5"/>
            <stop offset="0.21" stop-color="silver"/>
            <stop offset="0.22"/>
            <stop offset="0.24" stop-color="#3c3c3c"/>
        </radialGradient>
        <linearGradient id="gradient_04" x1="220.08" y1="-4439.05" x2="924.96" y2="-5411.79" gradientTransform="matrix(1, 0, 0, -1, 0, -4157.39)" gradientUnits="userSpaceOnUse">
            <stop offset="0.02"/>
            <stop offset="0.04" stop-color="#c3c3c3"/>
            <stop offset="0.05" stop-color="#7b7b7b"/>
            <stop offset="0.49" stop-color="#4a4a4a"/>
            <stop offset="1" stop-color="#c4c4c4" stop-opacity="0"/>
            <stop offset="1" stop-color="#403b3b" stop-opacity="0"/>
        </linearGradient>
        <radialGradient id="gradient_05" cx="-1357.46" cy="-1760.32" r="0.99" gradientTransform="matrix(-284, 249.85, 522.53, 593.4, 536223.7, 1384635.08)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#515151"/>
            <stop offset="0.18" stop-color="#676767"/>
            <stop offset="0.8" stop-color="#5a5a5a"/>
            <stop offset="0.87" stop-color="#a7a7a7"/>
            <stop offset="0.89" stop-color="#a5a5a5"/>
            <stop offset="0.9" stop-color="#8e8e8e"/>
            <stop offset="0.92" stop-color="#949494"/>
            <stop offset="0.92" stop-color="#929292"/>
            <stop offset="0.95" stop-color="#b9b9b9"/>
        </radialGradient>
        <linearGradient id="gradient_06" x1="744.62" y1="-5623.42" x2="1099.39" y2="-5068.77" gradientTransform="matrix(1, 0, 0, -1, 0, -4157.39)" gradientUnits="userSpaceOnUse">
            <stop offset="0.61" stop-color="#4f4f4f"/>
            <stop offset="0.71" stop-color="#e0e0e0"/>
            <stop offset="0.91" stop-color="#fff"/>
        </linearGradient>
        <linearGradient id="gradient_07" x1="2560.66" y1="-5724.5" x2="2497.49" y2="-5603.57" gradientTransform="matrix(1, 0, 0, -1, 0, -4157.39)" gradientUnits="userSpaceOnUse">
            <stop offset="0.53" stop-color="#313131"/>
            <stop offset="0.7" stop-color="#7b7b7b"/>
            <stop offset="0.82" stop-color="#9d9d9d"/>
        </linearGradient>
        <linearGradient id="gradient_08" x1="166.92" y1="-4016.76" x2="181.97" y2="-4020.62" gradientTransform="matrix(0, 1, 1, 0, 6438.73, 1425.12)" gradientUnits="userSpaceOnUse">
            <stop offset="0.08" stop-color="#2f2f2f"/>
            <stop offset="0.47" stop-color="#f0f0f0"/>
            <stop offset="0.62" stop-color="#9a9a9a"/>
        </linearGradient>
        <linearGradient id="gradient_09" x1="1569.63" y1="-6044.86" x2="1583.29" y2="-6052.29" gradientTransform="matrix(1, 0, 0, -1, 855.83, -4463.81)" gradientUnits="userSpaceOnUse">
            <stop offset="0.08" stop-color="#2f2f2f"/>
            <stop offset="0.47" stop-color="#979797"/>
            <stop offset="0.62" stop-color="#474747"/>
        </linearGradient>
        <radialGradient id="gradient_10" cx="-1285.74" cy="-2028.86" r="0.99" gradientTransform="matrix(20.7, 1.48, 0.62, -8.59, 28420.78, -15282.63)" gradientUnits="userSpaceOnUse">
            <stop offset="0.21" stop-color="#191919"/>
            <stop offset="0.27" stop-color="#365153"/>
            <stop offset="0.4" stop-color="#1f5558"/>
            <stop offset="0.52" stop-color="#60acba"/>
            <stop offset="0.71" stop-color="#416961"/>
            <stop offset="0.92" stop-color="#191919"/>
        </radialGradient>
        <radialGradient id="gradient_11" cx="-1285.74" cy="-2028.86" r="0.99" gradientTransform="matrix(20.7, 1.48, 0.62, -8.59, 28420.78, -15282.63)" gradientUnits="userSpaceOnUse">
            <stop offset="0.41" stop-opacity="0"/>
            <stop offset="0.51"/>
            <stop offset="0.82" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="gradient_12" cx="-1285.14" cy="-2028" r="0.99" gradientTransform="matrix(20.7, 1.48, 0.62, -8.59, 28420.78, -15282.63)" gradientUnits="userSpaceOnUse">
            <stop offset="0.09"/>
            <stop offset="0.17" stop-opacity="0.13"/>
            <stop offset="0.3" stop-opacity="0.13"/>
            <stop offset="0.46"/>
            <stop offset="0.63"/>
            <stop offset="0.68" stop-opacity="0"/>
            <stop offset="0.88" stop-opacity="0"/>
            <stop offset="0.95"/>
        </radialGradient>
        <radialGradient id="gradient_13" cx="-1226.26" cy="-2560.5" r="0.99" gradientTransform="matrix(5.88, -1.31, -0.72, -3.21, 5917.02, -9600.59)" gradientUnits="userSpaceOnUse">
            <stop offset="0.13" stop-color="#58a9d7"/>
            <stop offset="1" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="gradient_14" x1="2571.89" y1="-5669.05" x2="2494.95" y2="-5696.22" gradientTransform="matrix(1, 0, 0, -1, 0, -4157.39)" gradientUnits="userSpaceOnUse">
            <stop offset="0.27" stop-color="#a8a8a8"/>
            <stop offset="0.8" stop-color="#ababab"/>
            <stop offset="0.9" stop-color="#afafaf"/>
            <stop offset="0.96" stop-color="#404040"/>
            <stop offset="0.98" stop-color="#404040"/>
            <stop offset="1" stop-color="#404040" stop-opacity="0.69"/>
        </linearGradient>
        <linearGradient id="gradient_15" x1="340.89" y1="910.27" x2="2477.28" y2="50.97" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="987.61 8.67 1045.15 16.11 2941.99 1148.82 2941.99 1260.79 1988.21 1757.72 34.02 490.88 65.81 407.09 939.6 16.11 987.61 8.67"/>
        </clipPath>
        <path id="filterPath" d="M0.3295,0.0048l0.0192,0.0041L0.9815,0.6316v0.0616l-0.3182,0.2732L0.0113,0.2699l0.0106-0.0461,0.2915-0.215Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M59.35,598.6c-106.62-72.1-45.46-165.84-2.47-193.49-46.95,41-19.77,66.57,2.47,80.89C584.53,824.14,1632.44,1497.93,1829.51,1624.65l24.13,15.51c117.11,75.31,210,47.9,239.17,32.1l844-441c16.8-9.38,55.84-39.51,16.8-83.46,85,83,28.66,181.7-18.29,206.93-190.25,102.22-593.48,317.55-767.43,410.89-160.33,86-226.82,51-314.28-5.43C1305,1406.39,178,678.81,59.35,598.6Z" fill="#1a1a1a"/>
        <path d="M2965.13,1163.15c-6.53-10.73-18.32-21.85-37.18-33-39-23.08-374.33-223.37-770.21-459.83C1786.79,448.75,1362.66,195.42,1079.31,26.36c-28.83-17.2-95.18-40.52-165.93-11.92-72.67,29.37-595,266.21-841.11,382.76-20.36,9.64-34.6,22.14-39.67,36.47s-.81,30.38,15.17,46.61c2.68,2.71,9.93,8.2,21.19,16.16s26.74,18.49,45.85,31.27c38.24,25.56,91.24,60.19,155.5,101.75,128.54,83.12,302.14,194,492.82,315.44,68.53,43.65,139.26,88.68,210.9,134.28,327,208.17,673,428.37,914.16,585,32.31,21,123.32,54.49,219.39,6.48h0c93.7-48.6,589.55-310.32,825.75-435.1,15.4-8.14,33.14-23.14,37.44-41.91C2972.94,1184.23,2971.68,1173.91,2965.13,1163.15Zm-61.38,53-835.46,436.65a156.16,156.16,0,0,1-156.51-6.83L84,477.27C57.64,460.39,60.45,421,89,408l831-377.64a159.17,159.17,0,0,1,147.39,8.2L2906,1135.17C2937.22,1153.78,2935.94,1199.36,2903.75,1216.19Z" stroke-width="1.98" fill="url(#gradient_02)" stroke="url(#gradient_03)"/>
        <path d="M2923.49,1130.49c-74.95-45-1256.16-751.84-1840.66-1097.59C1054.23,16,985.94-10.32,915.72,17.82c-72.48,29.05-591.3,266.7-836.86,382-26.14,12.27-66.06,44.64-18,78.77,44.48,31.61,1240.6,797.07,1827.4,1174.9,32,20.64,108.71,62,220.76,4.43,93.49-48.08,588.29-307,824-430.44C2981.8,1201.91,2962,1153.61,2923.49,1130.49Zm-19.74,85.7-835.46,436.65a156.16,156.16,0,0,1-156.51-6.83L84,477.27C57.64,460.39,60.45,421,89,408l831-377.64a159.17,159.17,0,0,1,147.39,8.2L2906,1135.17C2937.22,1153.78,2935.94,1199.36,2903.75,1216.19Z"/>
        <path d="M74.18,482.54c-37.07-24.69-32.12-29.63-26.69-47.41,0,20.25,6.92,29.14,33.6,47.41l1101.49,710.18C826.61,965.38,108.59,505.47,74.18,482.54Z" fill="url(#gradient_04)"/>
        <path d="M2885.44,1225.81c31.13-16.79,73.14-57.79,4.94-100.26C2965,1160.62,2954.62,1200.62,2885.44,1225.81Z"/>
        <path d="M101.85,402C72.2,417.85,44.52,446,89,480.57,14.88,439.58,70.72,410.44,101.85,402Z"/>
        <g mask="url(#mask)">
            <path d="M2925,1324.08c68.24-37,77.12-86.43,69.18-115.06,25.69,85.93-40.52,136.11-60.29,146.67-273.76,146.19-651.07,348.67-792.63,424.23s-221.88,25.18-280.19-14.81L961.68,1189.75l-885-579.3C21.3,575.39-13.29,543.29,5,469.7c-5.93,47.91,32.62,81.45,73.14,107.67,529.57,342.57,1678.65,1085.11,1815,1172.42,74.12,47.46,165.54,31.73,200.63,13.34C2290.47,1660,2850.36,1364.59,2925,1324.08Z" fill="url(#gradient_05)"/>
        </g>
        <path d="M77.63,535.39C45,514.64,44.52,494.89,49,481.56,50.45,488,73.19,503.29,87,512.67L1862,1653c6.42,4.44,25.89,18.37,66.21,34.57-19.76,16.79-45.46,7.3-56.82,0C1287.5,1312.4,111.24,556.72,77.63,535.39Z" fill="url(#gradient_06)"/>
        <g mask="url(#mask-2)">
            <ellipse cx="1932.21" cy="1689.54" rx="40.03" ry="40" fill="#454545"/>
        </g>
        <path d="M2120.48,1704.86c-39.14,20.14-67.53,3-77.58-8.9,14.82,0,51.39-13.83,54.85-15.8L2909.66,1252c12.35-6.42,40-19.25,52.87-35.55,9.09,40.29-20.75,57.11-36.57,65.18C2673.77,1414.29,2159.62,1684.7,2120.48,1704.86Z" fill="url(#gradient_07)"/>
        <path d="M2912.13,1367.17c30-36.74,9.71-90.49-4.45-114.2-3,1.48-15.81,8.39-18.78,9.88,29.65,51.75,15.32,97.45,4.45,114.4Z" fill="#383838" fill-opacity="0.81"/>
        <path d="M2149.1,1775.51c29.88-38.55,9.64-95.21-4.44-120.09l-18.78,9.91c29.48,54.29,15.26,101.79,4.45,119.57l9.38-4.63Z" fill="#383838" fill-opacity="0.81"/>
        <path d="M168,564.63,151.71,554C131.15,574.51,129,624.64,131,645.87c1.49,1,15.82,10.29,18.29,12C144.1,612.78,159.12,574,168,564.63Z" fill="#2b2b2b" fill-opacity="0.83"/>
        <path d="M441.28,740.15,425,729.78c-20.56,20.54-24.22,71.71-22.24,92.94,1.48,1,15.81,10.17,18.28,11.83C415.88,789.51,432.39,749.53,441.28,740.15Z" fill="#2b2b2b" fill-opacity="0.83"/>
        <path d="M2879.63,1321.2h0c4.74-2.06,8.57.46,8.57,5.62V1353a15.44,15.44,0,0,1-8.57,13.06h0c-4.73,2.06-8.57-.46-8.57-5.62v-26.22A15.46,15.46,0,0,1,2879.63,1321.2Z"/>
        <path d="M2855.91,1334h0c4.74-2.06,8.57.46,8.57,5.62v26.22a15.41,15.41,0,0,1-8.57,13.06h0c-4.73,2.06-8.57-.46-8.57-5.61v-26.22A15.46,15.46,0,0,1,2855.91,1334Z"/>
        <path d="M2833.18,1345.9h0c4.74-2.06,8.57.45,8.57,5.61v26.22a15.44,15.44,0,0,1-8.57,13.07h0c-4.73,2-8.57-.46-8.57-5.62V1359A15.43,15.43,0,0,1,2833.18,1345.9Z"/>
        <path d="M2810.45,1357.75h0c4.73-2.06,8.57.46,8.57,5.62v26.22a15.46,15.46,0,0,1-8.57,13.06h0c-4.73,2.06-8.57-.46-8.57-5.62v-26.22A15.46,15.46,0,0,1,2810.45,1357.75Z"/>
        <path d="M2787.72,1370.58h0c4.73-2,8.57.46,8.57,5.62v26.22a15.43,15.43,0,0,1-8.57,13.06h0c-4.73,2.06-8.57-.45-8.57-5.61v-26.22A15.46,15.46,0,0,1,2787.72,1370.58Z"/>
        <path d="M2763.51,1383.42h0c5-2.05,9.06.67,9.06,6.08v25.31a15.8,15.8,0,0,1-9.06,13.52h0c-5,2.05-9.07-.67-9.07-6.08v-25.31A15.79,15.79,0,0,1,2763.51,1383.42Z"/>
        <ellipse cx="2423.14" cy="1589.71" rx="9.03" ry="8.16" transform="translate(637.51 3872.32) rotate(-85.23)" fill="url(#gradient_08)"/>
        <ellipse cx="2424.91" cy="1584.06" rx="7.85" ry="9.72" transform="translate(-386.09 885.69) rotate(-19.2)" fill="url(#gradient_09)"/>
        <ellipse cx="2423.7" cy="1586.52" rx="8.61" ry="10.42" fill="#0b0b0b"/>
        <ellipse cx="560.37" cy="236.41" rx="29.65" ry="15.31" fill="#0c0c0c"/>
        <path d="M561.22,227.64c11.3.81,19.79,5.24,19,9.89s-10.65,7.77-21.94,7-19.79-5.23-19-9.88S549.92,226.84,561.22,227.64Z" fill="url(#gradient_10)"/>
        <path d="M561.22,227.64c11.3.81,19.79,5.24,19,9.89s-10.65,7.77-21.94,7-19.79-5.23-19-9.88S549.92,226.84,561.22,227.64Z" fill="url(#gradient_11)"/>
        <path d="M561.22,227.64c11.3.81,19.79,5.24,19,9.89s-10.65,7.77-21.94,7-19.79-5.23-19-9.88S549.92,226.84,561.22,227.64Z" fill="url(#gradient_12)"/>
        <path d="M547.35,233.68c3.21-.71,7.55-.19,9.7,1.17s1.3,3-1.91,3.76-7.55.2-9.7-1.16S544.14,234.4,547.35,233.68Z" fill="url(#gradient_13)"/>
        <path d="M2467.44,1563.26c.52-18.35,14-40.26,30.22-48.94l82.61-44.33c16.18-8.68,28.87-.85,28.35,17.5s-14,40.24-30.22,48.92l-82.59,44.34C2479.63,1589.43,2466.93,1581.6,2467.44,1563.26Z" fill="#353535"/>
        <path d="M2473,1561c.38-13.66,10.46-30,22.51-36.46l86.54-46.44c12.05-6.47,21.51-.63,21.13,13s-10.47,30-22.52,36.46l-86.53,46.45C2482.09,1580.54,2472.62,1574.71,2473,1561Z"/>
        <g mask="url(#mask-3)">
            <path d="M2501.14,1545.85l-23.72-.31c-2.47,0-1.9-3.06-1.42-4.46s1.67-1.79,3-1.94l20.95,1.56,67.2-36.55s2.47-1.14,3.46.5c1.49,2.46-1.78,4.54-1.78,4.54Z" fill="url(#gradient_14)"/>
        </g>
        <path d="M1067.33,38.57a159.17,159.17,0,0,0-147.39-8.2L100.45,402.8C83.73,412,68,425,65.93,441a40.64,40.64,0,0,0,.39,9.35c1.93,9.16,8.55,19.08,21.85,29.61l1132,723.86,412.46-828Z" opacity="0.4" fill="url(#gradient_15)"/>
        <path d="M59.35,598.6C178,678.81,1305,1406.39,1853.64,1760.17c87.46,56.39,153.95,91.46,314.28,5.43,173.95-93.34,577.18-308.67,767.43-410.89,46.09-24.77,101.22-120.4,22.77-202.39-7.25-7.93-17.17-15.64-29.67-23C2889.81,1106.42,2555.87,907,2169.2,676L2146,662.12C1777.57,442.09,1360,192.71,1079.81,25.51c-36.6-21.84-102.25-38.09-166.8-12C837.56,44,313.64,281.79,71.85,396.31c-12,5.68-21.64,12.23-28.68,19.39C3,451.91-36.25,534,59.35,598.6ZM86.72,405.11,918.34,27.46C965.84,5.87,1025,9.18,1069.82,35.91L2912,1132.11c31.2,18.6,28.43,72.16-3.76,89l-834.62,438.08c-49.63,25.94-119.56,22.54-166.73-7.63L81.14,479.57C54.74,462.69,58.19,418.07,86.72,405.11Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
    </g>
</svg>
