<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1400 1080" data-forced-size="true" width="1400" height="1080" data-img-aspect-ratio="4:3" data-img-perspective="[[2.64, 26], [71.56, 2.43], [98.01, 64.24], [27.65, 96.1]]">
    <defs>
        <linearGradient id="gradient_01" x1="-6.08" y1="525.96" x2="1391.69" y2="554.15" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.2" stop-color="#8a8a8a"/>
            <stop offset="0.32" stop-color="#2d2d2d"/>
            <stop offset="0.39" stop-color="#a1a1a1"/>
            <stop offset="0.48" stop-color="#8a8a8a"/>
            <stop offset="0.54" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="light_adjust" x1="-6.08" y1="525.96" x2="1391.69" y2="554.15" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="0.5" stop-color="#fff" stop-opacity=".5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <linearGradient id="gradient_02" x1="232.79" y1="652.95" x2="1306.75" y2="220.99" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <radialGradient id="gradient_03" cx="-2440.53" cy="583.66" r="2.66" gradientTransform="matrix(0.36, 0.93, 0.93, -0.36, 1527.68, 2834.08)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#6d7f86"/>
            <stop offset="0.05" stop-color="#5c6c76"/>
            <stop offset="0.15" stop-color="#414e5b"/>
            <stop offset="0.26" stop-color="#2b3445"/>
            <stop offset="0.37" stop-color="#1a2134"/>
            <stop offset="0.51" stop-color="#0e1328"/>
            <stop offset="0.68" stop-color="#070b21"/>
            <stop offset="1" stop-color="#05091f"/>
        </radialGradient>
        <linearGradient id="gradient_04" x1="-993.67" y1="767.54" x2="-985.64" y2="767.54" gradientTransform="matrix(-0.56, -0.83, 0.83, -0.56, 132.78, 366.72)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#141414"/>
            <stop offset="0.35" stop-color="#343434"/>
            <stop offset="0.5" stop-color="#424242"/>
            <stop offset="0.65" stop-color="#343434"/>
            <stop offset="1" stop-color="#141414"/>
        </linearGradient>
        <linearGradient id="gradient_04-2" x1="-986" y1="738.42" x2="-977.97" y2="738.42" xlink:href="#gradient_04"/>
        <linearGradient id="gradient_05" x1="-1116.03" y1="-444.25" x2="-1114.19" y2="-449.01" gradientTransform="matrix(-1.6, -0.66, 2.07, -2.4, -699.52, -1140.29)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="1010.2 17.48 1384.53 696.67 380.87 1053.02 22.89 274.12 1010.2 17.48"/>
        </clipPath>
        <path id="filterPath" d="M0.7216,0.0162l0.2674,0.6289L0.2721,0.975l-0.2557-0.7212Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1393.79,669.35c-11-19.29-347.65-625.14-356.8-640.77C1026.73,11.05,993.47-4.3,972.64,1.1c0,0-888.53,231.46-931.91,242.8S-2.94,304.75,2.32,316.37s312.54,682.05,332.16,724.78,67.71,41.9,84.7,35.81,941.1-332.86,947.32-335.63S1386.26,725,1386.26,725C1400.52,710.11,1404.77,688.64,1393.79,669.35Zm-33.47,28.42c-8,3.22-950.19,332.11-956.2,334.23s-17.2,3.79-25.39-13S47.12,302.81,43.7,295.34,42,279.68,54,276.47,985.07,30.76,985.07,30.76c13.68-3.42,19.59,1.14,23,6.85s353,636.2,356.07,642.11S1368.3,694.56,1360.32,697.77Z" fill="url(#gradient_01)"/>
        <path d="M1393.79,669.35c-11-19.29-347.65-625.14-356.8-640.77C1026.73,11.05,993.47-4.3,972.64,1.1c0,0-888.53,231.46-931.91,242.8S-2.94,304.75,2.32,316.36s312.54,682.06,332.16,724.79,67.71,41.9,84.7,35.81,941.1-332.86,947.32-335.63S1386.26,725,1386.26,725C1400.53,710.11,1404.77,688.64,1393.79,669.35ZM1197.24,342.89c1.3-1.3,3.8-.9,5.59.89s2.19,4.3.9,5.59-3.8.9-5.59-.89S1196,344.19,1197.24,342.89Zm174.19,356.84c-8,3.22-962.6,341.63-968.61,343.76s-17.2,3.78-25.39-13S35.31,297.93,31.89,290.47s-1.66-15.66,10.26-18.88S988.83,22.74,988.83,22.74c13.68-3.43,19.58,1.14,23,6.84s360.33,646.2,363.44,652.11S1379.41,696.52,1371.43,699.73Z" fill="url(#light_adjust)" opacity="0.2" style="mix-blend-mode: difference"/>
        <path d="M1392.34,673.19c-8.71-16.18-342.92-616.67-357-641.29-13-22.82-46-31.74-60.83-28.42L40.59,247.5C13.09,254.69,2,284.42,9.5,300.74s323.74,705.83,338.25,734.6,54.16,34.85,71.4,29c14-4.76,933.5-329.2,955-336.67S1401,689.37,1392.34,673.19Zm-32,24.58c-8,3.22-950.19,332.11-956.2,334.23s-17.2,3.79-25.39-13S47.12,302.81,43.7,295.34,42,279.68,54,276.47,985.07,30.76,985.07,30.76c13.68-3.42,19.59,1.14,23,6.85s353,636.2,356.07,642.11S1368.3,694.56,1360.32,697.77Z"/>
        <path d="M1393.79,669.35c-11-19.29-347.65-625.14-356.8-640.77C1026.73,11.05,993.47-4.3,972.64,1.1c0,0-888.53,231.46-931.91,242.8-13.74,3.59-23.15,11-29.44,19.85-8.44,14.73-8.5,33.51-2.76,45.42s328.91,715.43,334.8,727.41c12.43,25.31,42.55,43.43,80.83,31.12S1360,737.18,1374.31,732.1c6.51-2.31,12-7.23,16.22-12.18C1401.24,705.46,1403.58,686.55,1393.79,669.35Zm-19.69,58.29c-21.45,7.47-941,331.91-954.95,336.67-17.24,5.88-56.89-.21-71.4-29S17,317.06,9.5,300.74s3.59-46.05,31.09-53.24L974.5,3.48c14.82-3.32,47.78,5.6,60.83,28.42,14.09,24.62,348.3,625.11,357,641.29S1395.55,720.17,1374.1,727.64Z" fill="#fff" opacity="0.5"/>
        <path d="M54,276.47C42,279.68,40.28,287.88,43.7,295.34s326.85,706.84,335,723.65,19.38,15.14,25.39,13l12.62-4.41L763.49,89.22C490.68,161.19,62.07,274.28,54,276.47Z" opacity="0.4" fill="url(#gradient_02)"/>
        <g id="details">
            <g>
                <ellipse cx="1200.49" cy="346.13" rx="3.37" ry="4.66" transform="translate(115.88 966.29) rotate(-45.84)" fill="#1a1c1c"/>
                <ellipse cx="1200.49" cy="346.14" rx="2.21" ry="3.04" transform="translate(115.89 966.33) rotate(-45.85)" fill="url(#gradient_03)"/>
            </g>
            <g>
                <path d="M1313.58,758.5l19.84-6.94h0a3.78,3.78,0,0,0,.92-.57,4.94,4.94,0,0,0,.85-.86,4,4,0,0,0,.57-.88c.11-.26.15-.22.08-.35a4.25,4.25,0,0,0-.4-.59,2.62,2.62,0,0,0-.21-.17,2.51,2.51,0,0,0-.47-.31.85.85,0,0,0-.47-.09,2.67,2.67,0,0,0-.67.15l-17.78,6.26a5.48,5.48,0,0,0-3.25,2.79h0a1.15,1.15,0,0,0-.11.49.6.6,0,0,0,.14.39,1.71,1.71,0,0,0,1,.68" fill="url(#gradient_04)"/>
                <path d="M1333.53,751.55a3.61,3.61,0,0,0,2.37-1.95.78.78,0,0,0-.39-1,1.64,1.64,0,0,0-1.39-.08l-17.4,6.24a5.74,5.74,0,0,0-3.22,2.56.73.73,0,0,0,.32,1.07,2.27,2.27,0,0,0,1.76-.08Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.13"/>
            </g>
            <g>
                <path d="M1285.19,768.55,1305,761.6h0a3.89,3.89,0,0,0,.93-.56,5.82,5.82,0,0,0,.85-.87,4,4,0,0,0,.57-.88c.11-.26.14-.22.07-.35a4,4,0,0,0-.39-.59,2.62,2.62,0,0,0-.21-.17,2.27,2.27,0,0,0-.48-.31,1,1,0,0,0-.47-.09,2.61,2.61,0,0,0-.66.15l-17.79,6.26a5.49,5.49,0,0,0-3.24,2.79h0a1.22,1.22,0,0,0-.11.49.63.63,0,0,0,.14.4,1.76,1.76,0,0,0,1,.68" fill="url(#gradient_04-2)"/>
                <path d="M1305.14,761.6a3.68,3.68,0,0,0,2.37-2,.77.77,0,0,0-.39-.94,1.63,1.63,0,0,0-1.39-.09l-17.41,6.24a5.77,5.77,0,0,0-3.22,2.56.73.73,0,0,0,.33,1.07,2.24,2.24,0,0,0,1.75-.08Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.13"/>
            </g>
            <path d="M174.75,680.79a23.8,23.8,0,0,0-2-9.2l-9.61-21.51c-1.32-2.94-3.22-4.16-3.73-2.29s.07,5.32,1.45,8.48l10.67,24C173,683.59,174.67,683.75,174.75,680.79Z" fill="#3d3d3d"/>
            <path d="M175.29,682a22.48,22.48,0,0,0-2-9l-10.14-22.65c-1.26-2.86-3.11-4-3.59-2.22s.06,5.16,1.46,8.2l11.21,25.19C173.61,684.72,175.22,684.87,175.29,682Z" fill="url(#gradient_05)"/>
            <path d="M174.8,681.2a20.8,20.8,0,0,0-1.76-8l-9.48-21.39c-1.16-2.58-2.81-3.65-3.2-1.93s.07,4.65,1.26,7.33L172,680.71C173.29,683.6,174.73,683.77,174.8,681.2Z" fill="#131313"/>
            <g>
                <path d="M111.57,541.51a11.09,11.09,0,0,1,1.23,5c-.14,1.09-.93.49-1.75-1.24a10.45,10.45,0,0,1-1.09-4.72C110.08,539.54,110.8,540,111.57,541.51Z" fill="#000102"/>
                <path d="M111.07,545.27c.8,1.74,1.59,2.34,1.75,1.24l-2.22-2.43A6.53,6.53,0,0,0,111.07,545.27Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M110.12,542.46a13.89,13.89,0,0,0,.93,2.77l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M111.82,542.52a9.44,9.44,0,0,1,1,4.13c-.13.87-.77.4-1.44-1a9,9,0,0,1-.94-3.92C110.58,540.82,111.19,541.2,111.82,542.52Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M107.12,531.83a11.13,11.13,0,0,1,1.24,5c-.14,1.08-.93.49-1.75-1.24a10.52,10.52,0,0,1-1.1-4.73C105.64,529.86,106.36,530.28,107.12,531.83Z" fill="#000102"/>
                <path d="M106.63,535.6c.8,1.74,1.59,2.33,1.75,1.24l-2.23-2.44A7.9,7.9,0,0,0,106.63,535.6Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M105.68,532.79a13.89,13.89,0,0,0,.93,2.77l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M107.38,532.84a9.65,9.65,0,0,1,1,4.14c-.13.86-.77.39-1.45-1.05A9.15,9.15,0,0,1,106,532C106.14,531.15,106.75,531.53,107.38,532.84Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M102.68,522.16a11.12,11.12,0,0,1,1.24,5c-.15,1.09-.93.5-1.75-1.23a10.55,10.55,0,0,1-1.1-4.73C101.2,520.19,101.91,520.61,102.68,522.16Z" fill="#000102"/>
                <path d="M102.19,525.92c.8,1.75,1.58,2.34,1.75,1.24l-2.23-2.43A7.41,7.41,0,0,0,102.19,525.92Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M101.24,523.11a13.89,13.89,0,0,0,.93,2.77l1.44.53-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M102.94,523.17a9.61,9.61,0,0,1,1,4.13c-.14.87-.77.4-1.45-1a9,9,0,0,1-.93-3.93C101.7,521.47,102.31,521.85,102.94,523.17Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M98.24,512.49a11.25,11.25,0,0,1,1.24,5c-.15,1.09-.94.49-1.76-1.24a10.45,10.45,0,0,1-1.09-4.72C96.75,510.52,97.47,510.94,98.24,512.49Z" fill="#000102"/>
                <path d="M97.75,516.25c.79,1.74,1.58,2.34,1.75,1.24l-2.23-2.43A6.59,6.59,0,0,0,97.75,516.25Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M96.8,513.44a13.24,13.24,0,0,0,.92,2.77l1.45.52-.35-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M98.5,513.5a9.55,9.55,0,0,1,1,4.13c-.13.87-.76.4-1.44-1a9.15,9.15,0,0,1-.94-3.92C97.25,511.8,97.87,512.18,98.5,513.5Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M93.8,502.81a11.1,11.1,0,0,1,1.23,5c-.14,1.08-.93.49-1.75-1.24a10.49,10.49,0,0,1-1.09-4.73C92.31,500.84,93,501.26,93.8,502.81Z" fill="#000102"/>
                <path d="M93.3,506.58c.8,1.74,1.59,2.33,1.75,1.24l-2.22-2.44A6.92,6.92,0,0,0,93.3,506.58Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M92.35,503.76a13.76,13.76,0,0,0,.93,2.78l1.44.52L94.38,506Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M94.05,503.82a9.51,9.51,0,0,1,1,4.14c-.13.86-.77.39-1.44-1A9,9,0,0,1,92.7,503C92.81,502.13,93.42,502.51,94.05,503.82Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M89.35,493.14a11.12,11.12,0,0,1,1.24,5c-.14,1.09-.93.5-1.75-1.23a10.55,10.55,0,0,1-1.1-4.73C87.87,491.17,88.59,491.59,89.35,493.14Z" fill="#000102"/>
                <path d="M88.86,496.9c.8,1.75,1.59,2.34,1.75,1.24l-2.23-2.43A7.41,7.41,0,0,0,88.86,496.9Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M87.91,494.09a13.89,13.89,0,0,0,.93,2.77l1.44.53-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M89.61,494.15a9.61,9.61,0,0,1,1,4.13c-.13.87-.77.4-1.45-1a9.18,9.18,0,0,1-.93-3.93C88.37,492.45,89,492.83,89.61,494.15Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M84.91,483.47a11.12,11.12,0,0,1,1.24,5c-.15,1.08-.93.49-1.75-1.24a10.48,10.48,0,0,1-1.1-4.72C83.43,481.5,84.14,481.92,84.91,483.47Z" fill="#000102"/>
                <path d="M84.42,487.23c.8,1.74,1.58,2.33,1.75,1.24L83.94,486A7.41,7.41,0,0,0,84.42,487.23Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M83.47,484.42a13.89,13.89,0,0,0,.93,2.77l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M85.17,484.48a9.58,9.58,0,0,1,1,4.13c-.14.87-.77.4-1.45-1.05a9,9,0,0,1-.93-3.92C83.93,482.78,84.54,483.16,85.17,484.48Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M80.47,473.79a11.26,11.26,0,0,1,1.24,5c-.15,1.08-.94.49-1.76-1.24a10.49,10.49,0,0,1-1.09-4.73C79,471.82,79.7,472.24,80.47,473.79Z" fill="#000102"/>
                <path d="M80,477.56c.79,1.74,1.58,2.33,1.75,1.24l-2.23-2.44A7,7,0,0,0,80,477.56Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M79,474.74a13.32,13.32,0,0,0,.92,2.78l1.45.52L81.05,477Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M80.73,474.8a9.62,9.62,0,0,1,1,4.14c-.13.86-.76.39-1.44-1.05a9.19,9.19,0,0,1-.94-3.93C79.48,473.11,80.1,473.49,80.73,474.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M76,464.12a11.09,11.09,0,0,1,1.23,5c-.14,1.09-.93.5-1.75-1.23a10.51,10.51,0,0,1-1.09-4.73C74.54,462.15,75.26,462.57,76,464.12Z" fill="#000102"/>
                <path d="M75.53,467.88c.8,1.75,1.59,2.34,1.75,1.24l-2.22-2.43A6.53,6.53,0,0,0,75.53,467.88Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M74.58,465.07a13.89,13.89,0,0,0,.93,2.77l1.44.53-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M76.28,465.13a9.47,9.47,0,0,1,1,4.13c-.13.87-.77.4-1.44-1a9.07,9.07,0,0,1-.94-3.93C75,463.43,75.65,463.81,76.28,465.13Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M71.58,454.45a11.12,11.12,0,0,1,1.24,5c-.14,1.08-.93.49-1.75-1.24A10.48,10.48,0,0,1,70,453.5C70.1,452.48,70.82,452.9,71.58,454.45Z" fill="#000102"/>
                <path d="M71.09,458.21c.8,1.74,1.59,2.33,1.75,1.24L70.61,457A7.41,7.41,0,0,0,71.09,458.21Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M70.14,455.4a13.89,13.89,0,0,0,.93,2.77l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M71.84,455.46a9.58,9.58,0,0,1,1,4.13c-.13.87-.77.4-1.45-1a9,9,0,0,1-.93-3.92C70.6,453.76,71.21,454.14,71.84,455.46Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M67.14,444.77a11.13,11.13,0,0,1,1.24,5c-.15,1.08-.93.49-1.75-1.24a10.52,10.52,0,0,1-1.1-4.73C65.66,442.8,66.37,443.22,67.14,444.77Z" fill="#000102"/>
                <path d="M66.65,448.54c.8,1.74,1.58,2.33,1.75,1.23l-2.23-2.43A7.9,7.9,0,0,0,66.65,448.54Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M65.7,445.72a14,14,0,0,0,.93,2.78l1.44.52L67.73,448Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M67.4,445.78a9.65,9.65,0,0,1,1,4.14c-.14.86-.77.39-1.45-1.05a9,9,0,0,1-.93-3.93C66.16,444.09,66.77,444.47,67.4,445.78Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M62.7,435.1a11.25,11.25,0,0,1,1.24,5c-.15,1.09-.94.5-1.76-1.23a10.51,10.51,0,0,1-1.09-4.73C61.21,433.13,61.93,433.55,62.7,435.1Z" fill="#000102"/>
                <path d="M62.21,438.86c.79,1.75,1.58,2.34,1.75,1.24l-2.23-2.43A6.59,6.59,0,0,0,62.21,438.86Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M61.26,436.05a13.24,13.24,0,0,0,.92,2.77l1.45.53-.35-1.07Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M63,436.11a9.58,9.58,0,0,1,1,4.13c-.13.87-.76.4-1.44-1.05a9.15,9.15,0,0,1-.94-3.92C61.71,434.41,62.33,434.79,63,436.11Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M58.26,425.43a11.09,11.09,0,0,1,1.23,5c-.14,1.08-.93.49-1.75-1.24a10.45,10.45,0,0,1-1.09-4.72C56.77,423.45,57.49,423.88,58.26,425.43Z" fill="#000102"/>
                <path d="M57.76,429.19c.8,1.74,1.59,2.33,1.75,1.24L57.29,428A6.35,6.35,0,0,0,57.76,429.19Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M56.81,426.38a13.89,13.89,0,0,0,.93,2.77l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M58.51,426.43a9.51,9.51,0,0,1,1,4.14c-.13.87-.77.4-1.44-1.05a9,9,0,0,1-.94-3.93C57.27,424.74,57.88,425.12,58.51,426.43Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M53.81,415.75a11.13,11.13,0,0,1,1.24,5c-.14,1.08-.93.49-1.75-1.24a10.52,10.52,0,0,1-1.1-4.73C52.33,413.78,53.05,414.2,53.81,415.75Z" fill="#000102"/>
                <path d="M53.32,419.52c.8,1.74,1.59,2.33,1.75,1.23l-2.23-2.43A7.9,7.9,0,0,0,53.32,419.52Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M52.37,416.7a14,14,0,0,0,.93,2.78l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M54.07,416.76a9.65,9.65,0,0,1,1,4.14c-.13.86-.77.39-1.45-1a9,9,0,0,1-.93-3.93C52.83,415.07,53.44,415.45,54.07,416.76Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M49.37,406.08a11.12,11.12,0,0,1,1.24,5c-.15,1.09-.93.5-1.75-1.23a10.55,10.55,0,0,1-1.1-4.73C47.89,404.11,48.6,404.53,49.37,406.08Z" fill="#000102"/>
                <path d="M48.88,409.84c.8,1.75,1.58,2.34,1.75,1.24l-2.23-2.43A7.41,7.41,0,0,0,48.88,409.84Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M47.93,407a14.52,14.52,0,0,0,.92,2.77l1.45.53L50,409.26Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M49.63,407.09a9.61,9.61,0,0,1,1,4.13c-.14.87-.77.4-1.45-1.05a9,9,0,0,1-.93-3.92C48.39,405.39,49,405.77,49.63,407.09Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M44.93,396.41a11.25,11.25,0,0,1,1.24,5c-.15,1.08-.94.49-1.76-1.24a10.45,10.45,0,0,1-1.09-4.72C43.44,394.43,44.16,394.86,44.93,396.41Z" fill="#000102"/>
                <path d="M44.44,400.17c.79,1.74,1.58,2.33,1.75,1.24L44,399A6.39,6.39,0,0,0,44.44,400.17Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M43.49,397.36a13.24,13.24,0,0,0,.92,2.77l1.45.52-.35-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M45.19,397.41a9.62,9.62,0,0,1,1,4.14c-.13.87-.76.4-1.44-1.05a9.19,9.19,0,0,1-.94-3.93C43.94,395.72,44.56,396.1,45.19,397.41Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M40.49,386.73a11.1,11.1,0,0,1,1.23,5c-.14,1.08-.93.49-1.75-1.24a10.51,10.51,0,0,1-1.09-4.73C39,384.76,39.72,385.18,40.49,386.73Z" fill="#000102"/>
                <path d="M40,390.5c.8,1.74,1.59,2.33,1.75,1.23l-2.22-2.43A6.92,6.92,0,0,0,40,390.5Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M39,387.68a14.19,14.19,0,0,0,.93,2.78l1.44.52-.34-1.06Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M40.74,387.74a9.51,9.51,0,0,1,1,4.14c-.13.86-.77.39-1.44-1.05a9.07,9.07,0,0,1-.94-3.93C39.5,386.05,40.11,386.43,40.74,387.74Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M299.73,951.33a11.57,11.57,0,0,1,1.28,5.2c-.15,1.12-1,.51-1.82-1.29a10.9,10.9,0,0,1-1.13-4.9C298.19,949.28,298.94,949.72,299.73,951.33Z" fill="#000102"/>
                <path d="M299.21,955.23c.83,1.81,1.65,2.42,1.82,1.29L298.72,954A8,8,0,0,0,299.21,955.23Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M298.23,952.31a14.54,14.54,0,0,0,1,2.88l1.5.54-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M300,952.37a10,10,0,0,1,1.07,4.29c-.14.9-.8.41-1.5-1.09a9.3,9.3,0,0,1-1-4.07C298.71,950.61,299.34,951,300,952.37Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M295.14,941.29a11.57,11.57,0,0,1,1.27,5.2c-.15,1.12-1,.51-1.81-1.29a10.76,10.76,0,0,1-1.13-4.9C293.6,939.24,294.34,939.68,295.14,941.29Z" fill="#000102"/>
                <path d="M294.62,945.19c.82,1.81,1.64,2.42,1.81,1.29l-2.3-2.53A6.39,6.39,0,0,0,294.62,945.19Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M293.64,942.27a13.53,13.53,0,0,0,1,2.88l1.49.54-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M295.4,942.34a9.78,9.78,0,0,1,1.06,4.28c-.14.9-.79.42-1.5-1.08a9.48,9.48,0,0,1-1-4.08C294.12,940.58,294.75,941,295.4,942.34Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M290.54,931.25a11.6,11.6,0,0,1,1.28,5.2c-.15,1.13-1,.51-1.82-1.28a11.1,11.1,0,0,1-1.13-4.91C289,929.2,289.75,929.64,290.54,931.25Z" fill="#000102"/>
                <path d="M290,935.15c.82,1.81,1.64,2.43,1.81,1.29l-2.31-2.53A7.15,7.15,0,0,0,290,935.15Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M289,932.23a14.76,14.76,0,0,0,1,2.88l1.5.55-.36-1.11Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M290.81,932.3a10.07,10.07,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.45,9.45,0,0,1-1-4.08C289.52,930.54,290.16,930.93,290.81,932.3Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M286,921.21a11.44,11.44,0,0,1,1.27,5.2c-.15,1.13-1,.51-1.81-1.28a10.83,10.83,0,0,1-1.13-4.91C284.41,919.17,285.16,919.6,286,921.21Z" fill="#000102"/>
                <path d="M285.43,925.11c.83,1.81,1.64,2.43,1.82,1.29l-2.31-2.53A7.6,7.6,0,0,0,285.43,925.11Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M284.45,922.2a14.24,14.24,0,0,0,1,2.87l1.5.55-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M286.21,922.26a9.78,9.78,0,0,1,1.06,4.29c-.13.9-.79.41-1.49-1.09a9.33,9.33,0,0,1-1-4.07C284.93,920.5,285.56,920.89,286.21,922.26Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M281.36,911.17a11.61,11.61,0,0,1,1.27,5.21c-.15,1.12-1,.51-1.81-1.29a10.82,10.82,0,0,1-1.13-4.9C279.82,909.13,280.56,909.57,281.36,911.17Z" fill="#000102"/>
                <path d="M280.84,915.08c.82,1.81,1.64,2.42,1.81,1.28l-2.3-2.52A6.65,6.65,0,0,0,280.84,915.08Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M279.86,912.16a14.49,14.49,0,0,0,.95,2.88l1.5.54-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M281.62,912.22a9.81,9.81,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.44,9.44,0,0,1-1-4.07C280.34,910.46,281,910.86,281.62,912.22Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M276.76,901.14a11.57,11.57,0,0,1,1.28,5.2c-.15,1.12-1,.51-1.82-1.29a11,11,0,0,1-1.13-4.9C275.22,899.09,276,899.53,276.76,901.14Z" fill="#000102"/>
                <path d="M276.25,905c.82,1.81,1.64,2.42,1.81,1.28l-2.31-2.52A7.48,7.48,0,0,0,276.25,905Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M275.26,902.12a15.21,15.21,0,0,0,1,2.88l1.5.54-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M277,902.18a10.1,10.1,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.44,9.44,0,0,1-1-4.07C275.74,900.42,276.37,900.82,277,902.18Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M272.17,891.1a11.54,11.54,0,0,1,1.27,5.2c-.15,1.12-1,.51-1.81-1.29a10.76,10.76,0,0,1-1.13-4.9C270.63,889.05,271.37,889.49,272.17,891.1Z" fill="#000102"/>
                <path d="M271.65,895c.83,1.81,1.64,2.42,1.82,1.29l-2.31-2.53A8,8,0,0,0,271.65,895Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M270.67,892.08a14.32,14.32,0,0,0,1,2.88l1.49.54-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M272.43,892.14a9.81,9.81,0,0,1,1.06,4.29c-.13.9-.79.41-1.49-1.09a9.3,9.3,0,0,1-1-4.07C271.15,890.38,271.78,890.78,272.43,892.14Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M267.58,881.06a11.57,11.57,0,0,1,1.27,5.2c-.15,1.13-1,.51-1.81-1.29a10.76,10.76,0,0,1-1.13-4.9C266,879,266.78,879.45,267.58,881.06Z" fill="#000102"/>
                <path d="M267.06,885c.82,1.81,1.64,2.43,1.81,1.29l-2.3-2.53A6.39,6.39,0,0,0,267.06,885Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M266.08,882a14.08,14.08,0,0,0,.95,2.88l1.5.55-.35-1.11Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M267.84,882.11a10.07,10.07,0,0,1,1.06,4.29c-.14.89-.8.41-1.5-1.09a9.45,9.45,0,0,1-1-4.08C266.56,880.35,267.19,880.74,267.84,882.11Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M263,871a11.6,11.6,0,0,1,1.28,5.2c-.15,1.13-1,.51-1.82-1.28a11,11,0,0,1-1.13-4.91C261.44,869,262.19,869.41,263,871Z" fill="#000102"/>
                <path d="M262.46,874.92c.83,1.81,1.65,2.43,1.82,1.29L262,873.68A8,8,0,0,0,262.46,874.92Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M261.48,872a14.89,14.89,0,0,0,1,2.87l1.5.55-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M263.25,872.07a10.07,10.07,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.48,9.48,0,0,1-1-4.08C262,870.31,262.59,870.7,263.25,872.07Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M258.39,861a11.57,11.57,0,0,1,1.27,5.2c-.15,1.13-1,.52-1.81-1.28a10.83,10.83,0,0,1-1.13-4.91C256.85,858.94,257.59,859.37,258.39,861Z" fill="#000102"/>
                <path d="M257.87,864.89c.82,1.81,1.64,2.42,1.81,1.28l-2.3-2.52A7.42,7.42,0,0,0,257.87,864.89Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M256.89,862a13.64,13.64,0,0,0,1,2.87l1.49.55-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M258.65,862a9.81,9.81,0,0,1,1.06,4.29c-.14.9-.79.41-1.49-1.09a9.33,9.33,0,0,1-1-4.07C257.37,860.27,258,860.66,258.65,862Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M253.79,851a11.44,11.44,0,0,1,1.28,5.2c-.15,1.12-1,.51-1.81-1.29a11,11,0,0,1-1.14-4.9C252.26,848.9,253,849.34,253.79,851Z" fill="#000102"/>
                <path d="M253.28,854.85c.82,1.81,1.64,2.42,1.81,1.28l-2.31-2.52A7.48,7.48,0,0,0,253.28,854.85Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M252.3,851.93a14.49,14.49,0,0,0,.95,2.88l1.5.54-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M254.06,852a10.1,10.1,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.44,9.44,0,0,1-1-4.07C252.77,850.23,253.41,850.63,254.06,852Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M249.2,840.91a11.57,11.57,0,0,1,1.28,5.2c-.15,1.12-1,.51-1.82-1.29a10.9,10.9,0,0,1-1.13-4.9C247.66,838.86,248.41,839.3,249.2,840.91Z" fill="#000102"/>
                <path d="M248.68,844.81c.83,1.81,1.65,2.42,1.82,1.29l-2.31-2.53A8,8,0,0,0,248.68,844.81Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M247.7,841.89a15.21,15.21,0,0,0,1,2.88l1.5.54-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M249.46,842a10,10,0,0,1,1.07,4.29c-.14.9-.8.41-1.5-1.09a9.45,9.45,0,0,1-1-4.07C248.18,840.19,248.81,840.59,249.46,842Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M244.61,830.87a11.57,11.57,0,0,1,1.27,5.2c-.15,1.12-1,.51-1.81-1.29a10.76,10.76,0,0,1-1.13-4.9C243.07,828.82,243.81,829.26,244.61,830.87Z" fill="#000102"/>
                <path d="M244.09,834.77c.82,1.81,1.64,2.42,1.81,1.29l-2.3-2.53A7.09,7.09,0,0,0,244.09,834.77Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M243.11,831.85a13.53,13.53,0,0,0,1,2.88l1.49.54-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M244.87,831.92a9.78,9.78,0,0,1,1.06,4.28c-.14.9-.79.42-1.5-1.08a9.48,9.48,0,0,1-1-4.08C243.59,830.16,244.22,830.55,244.87,831.92Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M240,820.83a11.6,11.6,0,0,1,1.28,5.2c-.15,1.13-1,.51-1.82-1.28a11.1,11.1,0,0,1-1.13-4.91C238.48,818.78,239.22,819.22,240,820.83Z" fill="#000102"/>
                <path d="M239.5,824.73c.82,1.81,1.64,2.43,1.81,1.29L239,823.49A7.15,7.15,0,0,0,239.5,824.73Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M238.52,821.81a14.08,14.08,0,0,0,.95,2.88l1.5.55-.36-1.11Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M240.28,821.88a10.07,10.07,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.45,9.45,0,0,1-1-4.08C239,820.12,239.63,820.51,240.28,821.88Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M235.42,810.79a11.6,11.6,0,0,1,1.28,5.2c-.16,1.13-1,.51-1.82-1.28a11,11,0,0,1-1.13-4.91C233.88,808.75,234.63,809.18,235.42,810.79Z" fill="#000102"/>
                <path d="M234.9,814.69c.83,1.81,1.64,2.43,1.82,1.29l-2.31-2.53A7.6,7.6,0,0,0,234.9,814.69Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M233.92,811.78a14.24,14.24,0,0,0,1,2.87l1.5.55-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M235.68,811.84a10,10,0,0,1,1.07,4.29c-.14.9-.8.41-1.5-1.09a9.33,9.33,0,0,1-1-4.07C234.4,810.08,235,810.47,235.68,811.84Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M230.83,800.75A11.61,11.61,0,0,1,232.1,806c-.15,1.12-1,.51-1.81-1.29a10.82,10.82,0,0,1-1.13-4.9C229.29,798.71,230,799.15,230.83,800.75Z" fill="#000102"/>
                <path d="M230.31,804.66c.82,1.81,1.64,2.42,1.81,1.28l-2.3-2.52A6.65,6.65,0,0,0,230.31,804.66Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M229.33,801.74a13.92,13.92,0,0,0,1,2.88l1.49.54-.35-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M231.09,801.8a9.81,9.81,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.44,9.44,0,0,1-1-4.07C229.81,800,230.44,800.44,231.09,801.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M226.23,790.72a11.57,11.57,0,0,1,1.28,5.2c-.15,1.12-1,.51-1.82-1.29a11,11,0,0,1-1.13-4.9C224.69,788.67,225.44,789.11,226.23,790.72Z" fill="#000102"/>
                <path d="M225.72,794.62c.82,1.81,1.64,2.42,1.81,1.28l-2.31-2.52A7.48,7.48,0,0,0,225.72,794.62Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M224.73,791.7a15.21,15.21,0,0,0,1,2.88l1.5.54-.36-1.1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M226.5,791.76a10.1,10.1,0,0,1,1.06,4.29c-.14.9-.8.41-1.5-1.09a9.44,9.44,0,0,1-1-4.07C225.21,790,225.85,790.4,226.5,791.76Z" fill="#0a0e0e"/>
            </g>
        </g>
        <path d="M1393.79,669.35c-11-19.29-347.65-625.14-356.8-640.77C1026.73,11.05,993.47-4.3,972.64,1.1c0,0-888.53,231.46-931.91,242.8S-2.94,304.75,2.32,316.36s312.54,682.06,332.16,724.79,67.71,41.9,84.7,35.81,941.1-332.86,947.32-335.63S1386.26,725,1386.26,725C1400.53,710.11,1404.77,688.64,1393.79,669.35ZM1197.24,342.89c1.3-1.3,3.8-.9,5.59.89s2.19,4.3.9,5.59-3.8.9-5.59-.89S1196,344.19,1197.24,342.89Zm174.19,356.84c-8,3.22-962.6,341.63-968.61,343.76s-17.2,3.78-25.39-13S35.31,297.93,31.89,290.47s-1.66-15.66,10.26-18.88S988.83,22.74,988.83,22.74c13.68-3.43,19.58,1.14,23,6.84s360.33,646.2,363.44,652.11S1379.41,696.52,1371.43,699.73Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
    </g>
</svg>
