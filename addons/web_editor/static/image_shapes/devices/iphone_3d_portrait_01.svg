<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1370 1960" data-forced-size="true" width="1370" height="1960" data-img-aspect-ratio="9:19.5" data-img-perspective="[[4.06, 1.13], [59.56, 4.14], [99.96, 88.08], [44.62, 96.89]]">
    <defs>
        <linearGradient id="gradient_01" x1="247.44" y1="0.35" x2="1023.29" y2="1790.67" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.46" stop-color="#8a8a8a"/>
            <stop offset="0.48" stop-color="#a1a1a1"/>
            <stop offset="0.51" stop-color="#8a8a8a"/>
            <stop offset="0.56" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="light_adjust" x1="247.44" y1="0.35" x2="1023.29" y2="1790.67" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="0.5" stop-color="#fff" stop-opacity=".5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="381.18" cy="69.77" r="8.05" gradientTransform="translate(120.12 -216.61) rotate(37.27)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#658088"/>
            <stop offset="0.07" stop-color="#4f6571"/>
            <stop offset="0.16" stop-color="#374756"/>
            <stop offset="0.27" stop-color="#232d40"/>
            <stop offset="0.39" stop-color="#131a2f"/>
            <stop offset="0.52" stop-color="#080c23"/>
            <stop offset="0.69" stop-color="#02041c"/>
            <stop offset="1" stop-color="#00021a"/>
        </radialGradient>
        <linearGradient id="gradient_03" x1="160.68" y1="13462.25" x2="216.54" y2="13462.25" gradientTransform="matrix(1, 0, 0, -1, 0, 14165.22)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#141414"/>
            <stop offset="0.35" stop-color="#343434"/>
            <stop offset="0.5" stop-color="#424242"/>
            <stop offset="0.65" stop-color="#343434"/>
            <stop offset="1" stop-color="#141414"/>
        </linearGradient>
        <linearGradient id="gradient_03-2" x1="111.68" y1="13627.35" x2="167.54" y2="13627.35" xlink:href="#gradient_03"/>
        <linearGradient id="gradient_03-3" x1="78.04" y1="13785.98" x2="105.4" y2="13785.98" xlink:href="#gradient_03"/>
        <linearGradient id="gradient_04" x1="2936.16" y1="6803.17" x2="2946.64" y2="6776.09" gradientTransform="matrix(1.46, 0.58, 0.77, -1.93, -8513.37, 13245.96)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <linearGradient id="gradient_05" x1="16.35" y1="13969.92" x2="79.56" y2="13986.27" gradientTransform="matrix(1, 0, 0, -1, 0, 14165.22)" gradientUnits="userSpaceOnUse">
            <stop offset="0.65" stop-color="#0d0d0d"/>
            <stop offset="1" stop-color="#5c5c5c"/>
        </linearGradient>
        <linearGradient id="gradient_06" x1="478.24" y1="12397.06" x2="538.25" y2="12418.72" gradientTransform="matrix(1, 0, 0, -1, 0, 14165.22)" gradientUnits="userSpaceOnUse">
            <stop offset="0.62" stop-color="#0d0d0d"/>
            <stop offset="1" stop-color="#5c5c5c"/>
        </linearGradient>
        <linearGradient id="gradient_07" x1="756.39" y1="12226.89" x2="756.39" y2="12265.12" gradientTransform="matrix(1, 0, 0, -1, 0, 14165.22)" gradientUnits="userSpaceOnUse">
            <stop offset="0.62" stop-color="#1f1f1f"/>
            <stop offset="1" stop-color="#c4c4c4"/>
        </linearGradient>
        <linearGradient id="gradient_08" x1="-24.47" y1="13700.71" x2="1154.09" y2="13226.66" gradientTransform="matrix(1, 0, 0, -1, 0, 14165.22)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="587.41 52.83 754.14 69.77 812.17 105.14 849.76 155.81 1344.78 1625.62 1351.32 1695.09 1304.46 1760.2 704.07 1893.42 598.03 1854.74 553.9 1758.25 69.23 105.96 91.69 25.7 160.35 19.32 587.41 52.83"/>
        </clipPath>
        <path id="filterPath" d="M0.4324,0.027,0.5541,0.0356l0.0423,0.018,0.0274,0.0259,0.3613,0.7499,0.0048,0.0354-0.0342,0.0332L0.5176,0.966l-0.0774-0.0197-0.0322-0.0492L0.0542,0.0541,0.0706,0.0131l0.0501-0.0033Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1358.7,1614.2c-9.1-27.6-458.6-1355-492.4-1450.3C835.3,76.7,761.4,50.4,697.6,45.4,563.7,34.9,198.8,4.3,148.8.4S67.4,17.3,67.4,17.3l-39,31.3C-5.4,74-2.1,126,3.6,148.9S416.9,1574.4,478.9,1790.7c62.1,216.5,202.8,165.8,230,160s547.7-132.9,554.6-134.7,19.3-3.6,71.6-51.2S1367.8,1641.8,1358.7,1614.2Zm-73.3,131.3c-65.4,15.8-510.8,117.6-540.8,123.6S613.9,1910.5,572,1766.8,93.5,153.7,86.4,123.2,71.7,25.1,158.3,31.1,301,42.2,307,42.7s13.1.7,18.2,13.4,20.7,46.7,63.6,48.5,155.9,9.5,171.4,10.4c13.6.7,33.6-9.3,27.4-36.9-3.5-15.4,7.3-13.3,15.8-12.7,8.4.4,84.7,7.2,113,8.6,40,1.9,103.7,22.3,128.8,95.1s478.4,1419,489.8,1455.5S1350.8,1729.7,1285.4,1745.5Z" fill="url(#gradient_01)"/>
        <path d="M1358.7,1614.3c-9.1-27.6-458.6-1355-492.4-1450.3C835.3,76.7,761.4,50.5,697.6,45.5,563.7,34.9,198.8,4.3,148.8.5S67.4,17.4,67.4,17.4l-39,31.3C-5.4,74-2.1,126,3.6,148.9S416.8,1574.4,478.9,1790.8s202.8,165.8,230,160,547.8-132.9,554.6-134.7,19.3-3.6,71.6-51.2S1367.8,1641.8,1358.7,1614.3ZM372.65,58.56c4.48-3.42,11.93-1.16,16.65,5s4.91,14,.42,17.39-11.94,1.16-16.65-5S368.16,62,372.65,58.56Zm916.28,1689.11c-65.4,15.8-518.27,119.81-548.27,125.81s-130.7,41.4-172.6-102.31S84.25,146.51,77.15,116s-14.7-98.1,71.9-92.1S305.21,39.48,311.21,40s13.1.7,18.2,13.4S346,97.3,388.9,99.1s155.9,9.5,171.4,10.4c13.6.7,26-3.76,19.75-31.36-3.5-15.4,15.4-16.94,23.9-16.34,8.5.4,84.7,7.2,113,8.6C757,72.3,823.57,95.18,848.67,168s478.46,1422.29,489.86,1458.79S1354.33,1731.87,1288.93,1747.67Z" fill="url(#light_adjust)" opacity="0.2"/>
        <path d="M1358.8,1614.3C1349.7,1586.7,900.2,259.4,866.4,164,835.4,76.6,761.5,50.5,697.7,45.5,563.7,34.9,198.9,4.3,148.8.5S67.4,17.4,67.4,17.4l-9.7,7.8c-31.6,34-16.7,96-11.4,116.3,7.6,29.8,444.6,1514.3,485.1,1651,38,127.9,130,140.4,202.5,128.6,18.9-3.1,458.9-110.6,559.2-135.4,18.1-4.5,32.1-11.9,42.1-20.7C1387.4,1717.4,1367.9,1641.9,1358.8,1614.3ZM1292,1769.4c-99.1,24.5-534.6,127.6-553.2,130.7-71.6,11.6-155-2.5-192.6-128.9C506.1,1636.1,74.4,169.3,66.8,139.9S28.1-1.1,152.8,8.7,700.6,52.8,720.2,54.2c27,1.9,112.2,22.9,143.8,113.5,33.9,97.4,467.2,1385.5,481.9,1427.4S1391,1744.8,1292,1769.4Z" fill="#fff" opacity="0.5"/>
        <path d="M1296.6,1784.7s25-5.2,43.3-24.3h0l-4.9,3.5c-9.3,7.6-21.8,12.7-36.7,16.4C1190,1806.8,748,1915,729.7,1918c-42.5,6.9-82.1,1.8-112.4-14.2-39-20.4-67.4-59.8-84.3-116.9C502.4,1684.3,58.4,176.8,50.4,146.1l-.5-1.9s-9.9-43.5-9.8-61c.2-41.1,16.8-57.5,16.8-57.5S38.1,44.2,38.1,83c0,26.9,8,62.2,8,62.2l.5,1.9c8,30.7,452,1538.3,482.5,1640.9,17.3,58.3,46.4,98.5,86.2,119.3,21.8,11.4,46.9,17.2,74.8,17.2a297.42,297.42,0,0,0,42.3-3.2c19-3.1,472.4-114,564.2-136.6Z" fill="#515151"/>
        <path d="M1345.9,1595.1c-14.7-41.9-448-1330-481.9-1427.4C832.4,77,747.1,56,720.2,54.1,700.6,52.7,277.5,18.5,152.8,8.7s-93.6,101.8-86,131.2S506.1,1636.1,546.2,1771.2c37.6,126.4,121,140.5,192.6,128.9,18.6-3.1,454.1-106.2,553.2-130.7S1360.6,1637,1345.9,1595.1Zm-60.5,150.4c-65.4,15.8-510.8,117.6-540.8,123.6S613.9,1910.5,572,1766.8,93.5,153.7,86.4,123.2,71.7,25.1,158.3,31.1,301,42.2,307,42.7s13.1.7,18.2,13.4,20.7,46.7,63.6,48.5,155.9,9.5,171.4,10.4c13.6.7,33.6-9.3,27.4-36.9-3.5-15.4,7.3-13.3,15.8-12.7,8.4.4,84.7,7.2,113,8.6,40,1.9,103.7,22.3,128.8,95.1s478.4,1419,489.8,1455.5S1350.8,1729.7,1285.4,1745.5Z"/>
        <g id="details">
            <g>
                <ellipse cx="381.18" cy="69.77" rx="10.2" ry="14.1" transform="translate(35.6 245.11) rotate(-37.27)" fill="#131516"/>
                <ellipse cx="381.18" cy="69.77" rx="6.7" ry="9.2" transform="translate(35.6 245.11) rotate(-37.27)" fill="url(#gradient_02)"/>
            </g>
            <g>
                <path d="M440.9,1569.3c2.5.2,4.4,3.9,4.3,8.5s-2.4,8.1-4.9,7.9-4.4-4-4.2-8.5S438.4,1569.1,440.9,1569.3Z"/>
                <path d="M440.3,1585.7c2.5.2,4.7-3.3,4.9-7.9l-6.5,7.2A3.29,3.29,0,0,0,440.3,1585.7Z" fill="#656565" fill-rule="evenodd"/>
                <path d="M437,1582.8c.8,1.7,1.9,2.8,3.3,2.9l4-8.3-1.1-1.2Z" fill="#fff" fill-rule="evenodd"/>
                <path d="M442.7,1572.7c1.5.1,2.7,2.4,2.6,5.2s-1.4,4.9-3,4.8-2.7-2.4-2.6-5.2S441.1,1572.6,442.7,1572.7Z" fill="#0d0d0d"/>
                <path d="M450.1,1567.4c2,6.6,5,26-5.7,26.9-9.4.8-14.1-14.8-15.9-21.6-12.5-47.7-51.6-178.8-53.7-188.2s-3.5-19.2,5.6-22.1,12.7,9.8,14.9,16S436.1,1520.1,450.1,1567.4Z" fill="none" stroke="#0d0d0d" stroke-miterlimit="10" stroke-width="1"/>
                <path d="M442.4,1594.3a5.5,5.5,0,0,0,5.6-1.4" fill="none" stroke="#ededed" stroke-linecap="round" stroke-width="1.63"/>
            </g>
            <g>
                <path d="M197.8,769.2,161.2,658.7h0a19,19,0,0,1-.4-6.9,31.39,31.39,0,0,1,1.9-7.9,25.42,25.42,0,0,1,3.3-6.6c1.2-1.7.8-1.7,2-1.9,1.9-.1,4.5-.6,5.7-.4.8.1,1.5.3,2.1.4a17.73,17.73,0,0,1,4.2,1.2,6.21,6.21,0,0,1,2.7,2.1,15.81,15.81,0,0,1,1.9,4.1l31,104.6a21.38,21.38,0,0,1-5,21.2h0a7.38,7.38,0,0,1-2.5,1.8,6.09,6.09,0,0,1-2.8.6c-2,0-3.5,0-7.5-1.8" fill="url(#gradient_03)"/>
                <path d="M160.7,658.1c-2.3-7.6-2.1-15.2,2.3-20.1a7.39,7.39,0,0,1,8.1-1.9,10.29,10.29,0,0,1,6.7,6.9l30.5,102.8c2.2,7.6.9,15.5-3.5,20.5a6.55,6.55,0,0,1-7.5,1.8,10.42,10.42,0,0,1-6-7.1Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.71"/>
            </g>
            <g>
                <path d="M148.8,604.1,112.2,493.6h0a19,19,0,0,1-.4-6.9,31.39,31.39,0,0,1,1.9-7.9,25.42,25.42,0,0,1,3.3-6.6c1.2-1.7.8-1.7,2-1.9,1.9-.1,4.5-.6,5.7-.4.8.1,1.5.3,2.1.4a17.73,17.73,0,0,1,4.2,1.2,6.21,6.21,0,0,1,2.7,2.1,15.81,15.81,0,0,1,1.9,4.1l31,104.6a21.38,21.38,0,0,1-5,21.2h0a7.38,7.38,0,0,1-2.5,1.8,6.09,6.09,0,0,1-2.8.6c-2,0-3.5,0-7.5-1.8" fill="url(#gradient_03-2)"/>
                <path d="M111.7,493c-2.3-7.6-2.1-15.2,2.3-20.1a7.39,7.39,0,0,1,8.1-1.9,10.29,10.29,0,0,1,6.7,6.9l30.5,102.8c2.2,7.6.9,15.5-3.5,20.5a6.55,6.55,0,0,1-7.5,1.8,10.42,10.42,0,0,1-6-7.1Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.71"/>
            </g>
            <g>
                <path d="M96.2,411.7,78.3,357.5h0a9.16,9.16,0,0,1-.2-3.4,13.77,13.77,0,0,1,.9-3.9,12,12,0,0,1,1.6-3.2c.6-.8.4-.8,1-.9.9,0,2.2-.3,2.8-.2a3,3,0,0,1,1,.2,6.46,6.46,0,0,1,2.1.6,2.56,2.56,0,0,1,1.3,1,9.65,9.65,0,0,1,.9,2L104.9,401a10.75,10.75,0,0,1-2.4,10.4h0a4.7,4.7,0,0,1-1.2.9,2.9,2.9,0,0,1-1.4.3,7.3,7.3,0,0,1-3.7-.9" fill="url(#gradient_03-3)"/>
                <path d="M78.1,357.3c-1.1-3.7-1-7.4,1.1-9.8a3.67,3.67,0,0,1,4-.9,5.1,5.1,0,0,1,3.3,3.4l14.9,50.4c1.1,3.7.4,7.6-1.7,10a3.28,3.28,0,0,1-3.7.9,5,5,0,0,1-2.9-3.5Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.35"/>
            </g>
            <g>
                <path d="M816.6,1909c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S820.4,1908.5,816.6,1909Z" fill="#000102"/>
                <path d="M810,1918.7c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A9.85,9.85,0,0,1,810,1918.7Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M816.3,1916.2a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M814.4,1910.2c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S817.6,1909.7,814.4,1910.2Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M840.7,1903.7c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S844.5,1903.2,840.7,1903.7Z" fill="#000102"/>
                <path d="M834.1,1913.4c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A9,9,0,0,1,834.1,1913.4Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M840.5,1910.9a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M838.6,1904.9c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S841.8,1904.4,838.6,1904.9Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M864.9,1898.4c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S868.7,1897.9,864.9,1898.4Z" fill="#000102"/>
                <path d="M858.3,1908.1c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A9.85,9.85,0,0,1,858.3,1908.1Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M864.6,1905.6a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M862.7,1899.6c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S865.9,1899.1,862.7,1899.6Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M936,1879.9c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S939.8,1879.4,936,1879.9Z" fill="#000102"/>
                <path d="M929.4,1889.7c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A22.88,22.88,0,0,1,929.4,1889.7Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M935.7,1887.2a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M933.8,1881.1c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S937,1880.7,933.8,1881.1Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M1084.2,1844.4c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1088,1843.9,1084.2,1844.4Z" fill="#000102"/>
                <path d="M1077.6,1854.2c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9C1079.5,1853.7,1078.5,1854,1077.6,1854.2Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1083.9,1851.7a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1082,1845.6c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1085.2,1845.2,1082,1845.6Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M1148,1829.2c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1151.8,1828.7,1148,1829.2Z" fill="#000102"/>
                <path d="M1141.4,1839c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A22.88,22.88,0,0,1,1141.4,1839Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1147.8,1836.5a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1145.8,1830.4c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1149,1830,1145.8,1830.4Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1170.4,1823.6c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1174.3,1823.1,1170.4,1823.6Z" fill="#000102"/>
                <path d="M1163.9,1833.2c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A17.15,17.15,0,0,1,1163.9,1833.2Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1170.2,1830.7a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1168.3,1824.7c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1171.5,1824.3,1168.3,1824.7Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1192,1818.5c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1195.9,1818,1192,1818.5Z" fill="#000102"/>
                <path d="M1185.5,1828.2c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9C1187.3,1827.7,1186.4,1828,1185.5,1828.2Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1191.8,1825.8a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1189.9,1819.7c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1193.1,1819.3,1189.9,1819.7Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1213.3,1813.2c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1217.1,1812.7,1213.3,1813.2Z" fill="#000102"/>
                <path d="M1206.8,1823c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A22.88,22.88,0,0,1,1206.8,1823Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1213.1,1820.5a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1211.2,1814.4c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1214.3,1814,1211.2,1814.4Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1234.2,1808.2c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1238.1,1807.7,1234.2,1808.2Z" fill="#000102"/>
                <path d="M1227.7,1818c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9C1229.5,1817.5,1228.6,1817.8,1227.7,1818Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1234,1815.5a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1232.1,1809.4c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1235.3,1809,1232.1,1809.4Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1255.1,1803.2c-4,.5-9,3.3-10.9,6.1s.1,4.4,4.3,3.7c4-.7,8.5-3.4,10.2-6S1259,1802.7,1255.1,1803.2Z" fill="#000102"/>
                <path d="M1248.6,1812.9c-4.2.8-6.2-.8-4.3-3.7l7.1,2.9A10.86,10.86,0,0,1,1248.6,1812.9Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1254.9,1810.4a18.89,18.89,0,0,1-6.3,2.5l-2.6-4.1,2.4-1Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1253,1804.4c-3.3.5-7.4,2.7-9,5s.1,3.6,3.6,3c3.3-.6,7.1-2.8,8.5-4.9S1256.2,1803.9,1253,1804.4Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M969.6,1874.7a48,48,0,0,1,19-9.5l50.6-11.8c6.9-1.6,11.5.6,8.8,4.5s-9.6,7.8-17,9.6l-56.4,13.3C966.8,1882.6,964.4,1879.3,969.6,1874.7Z" fill="#3d3d3d"/>
                <path d="M966.8,1875.3a45.82,45.82,0,0,1,18.6-9.2l53.3-12.4c6.7-1.6,11.1.6,8.5,4.3s-9.3,7.6-16.5,9.2l-59.2,13.9C964,1883,961.7,1879.8,966.8,1875.3Z" fill="url(#gradient_04)"/>
                <path d="M968.8,1875.2a41.9,41.9,0,0,1,16.6-8.3l50.2-11.9c6.1-1.4,10.1.5,7.5,3.9s-8.4,6.8-14.7,8.3l-55.2,13.2C966.4,1882,964.3,1879.2,968.8,1875.2Z" fill="#131313"/>
            </g>
            <polygon points="21.5 210.5 57.9 181.9 78.6 179.4 74.8 165.9 54.5 168.9 18.2 198.7 21.5 210.5" fill="url(#gradient_05)"/>
            <polygon points="478.3 1787 519.4 1750.2 538.5 1743.4 534.3 1729.5 515.1 1736 474.7 1774 478.3 1787" fill="url(#gradient_06)"/>
            <path d="M730.3,1945.7l34.6-31.6s8.7-9.7,0-19.7l14.5-3.2s8.7,10.1-3.1,20.3l-33.4,31.4Z" fill="url(#gradient_07)"/>
        </g>
        <path d="M1358.7,1614.3c-9.1-27.6-458.6-1355-492.4-1450.3C835.3,76.7,761.4,50.5,697.6,45.5,563.7,34.9,198.8,4.3,148.8.5S67.4,17.4,67.4,17.4l-39,31.3C-5.4,74-2.1,126,3.6,148.9S416.8,1574.4,478.9,1790.8s202.8,165.8,230,160,547.8-132.9,554.6-134.7,19.3-3.6,71.6-51.2S1367.8,1641.8,1358.7,1614.3ZM372.65,58.56c4.48-3.42,11.93-1.16,16.65,5s4.91,14,.42,17.39-11.94,1.16-16.65-5S368.16,62,372.65,58.56Zm916.28,1689.11c-65.4,15.8-518.27,119.81-548.27,125.81s-130.7,41.4-172.6-102.31S84.25,146.51,77.15,116s-14.7-98.1,71.9-92.1S305.21,39.48,311.21,40s13.1.7,18.2,13.4S346,97.3,388.9,99.1s155.9,9.5,171.4,10.4c13.6.7,26-3.76,19.75-31.36-3.5-15.4,15.4-16.94,23.9-16.34,8.5.4,84.7,7.2,113,8.6C757,72.3,823.57,95.18,848.67,168s478.46,1422.29,489.86,1458.79S1354.33,1731.87,1288.93,1747.67Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
        <path d="M815.5,118.6c-30-32.4-70.6-43.2-99.1-44.6-28.3-1.4-104.5-8.2-113-8.6-8.5-.6-19.3-2.7-15.8,12.7,6.2,27.6-13.8,37.6-27.4,36.9-15.5-.9-128.5-8.6-171.4-10.4s-58.5-35.8-63.6-48.5S313,43.2,307,42.7s-62.3-5.6-148.8-11.6-79,61.6-71.9,92.1c5,21.3,220,746.3,363.2,1229.5Z" opacity="0.4" fill="url(#gradient_08)"/>
    </g>
</svg>
