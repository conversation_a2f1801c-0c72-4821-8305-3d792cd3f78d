<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1290 1364" data-forced-size="true" width="1290" height="1364" data-img-aspect-ratio="3:4" data-img-perspective="[[29.37, 3.63], [97.05, 2.27], [79.06, 96.29], [2.1, 88.26]]">
    <defs>
        <linearGradient id="gradient_01" x1="570.39" y1="-25.56" x2="744.28" y2="1500" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.2" stop-color="#8a8a8a"/>
            <stop offset="0.39" stop-color="#a1a1a1"/>
            <stop offset="0.48" stop-color="#8a8a8a"/>
            <stop offset="0.54" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="gradient_02" x1="2311.03" y1="1673.43" x2="2323.98" y2="1673.43" gradientTransform="translate(3724.56 1590.87) rotate(175.28)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#141414"/>
            <stop offset="0.35" stop-color="#343434"/>
            <stop offset="0.5" stop-color="#424242"/>
            <stop offset="0.65" stop-color="#343434"/>
            <stop offset="1" stop-color="#141414"/>
        </linearGradient>
        <linearGradient id="gradient_02-2" x1="2323.4" y1="1626.47" x2="2336.35" y2="1626.47" xlink:href="#gradient_02"/>
        <linearGradient id="gradient_03" x1="2514.84" y1="-334.76" x2="2517.27" y2="-341.04" gradientTransform="matrix(-1.46, 0.6, -0.79, -1.92, 3898.74, -847.84)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <radialGradient id="gradient_04" cx="-606.24" cy="-1439.78" r="3.91" gradientTransform="matrix(-0.93, 0.36, 0.36, 0.93, 748.01, 1585.22)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#6d7f86"/>
            <stop offset="0.05" stop-color="#5c6c76"/>
            <stop offset="0.15" stop-color="#414e5b"/>
            <stop offset="0.26" stop-color="#2b3445"/>
            <stop offset="0.37" stop-color="#1a2134"/>
            <stop offset="0.51" stop-color="#0e1328"/>
            <stop offset="0.68" stop-color="#070b21"/>
            <stop offset="1" stop-color="#05091f"/>
        </radialGradient>
        <linearGradient id="gradient_05" x1="94.63" y1="504.92" x2="1887.23" y2="-216.1" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="1249.32 19.87 415.66 34.97 370.98 59.49 27.16 1186.29 54.41 1228.24 995.95 1333.06 1055.34 1290.63 1270.57 27.61 1249.32 19.87"/>
        </clipPath>
        <path id="filterPath" d="M0.9849,0.0202l-0.1668,0.926L0.7721,0.9773,0.0422,0.9005l-0.0211-0.0308L0.2876,0.0436,0.3222,0.0257l0.6462-0.0111Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1274.28,17.08c-8.54-10.72-22-17.26-34.89-17.08S409.44,20.89,409.44,20.89c-32.7.54-57.59,28.33-62.68,43.41S8.46,1159.43,2.28,1180.13s-.73,49.58,28,65.75c0,0,9.45,6,25.43,8.89s917.52,106.87,934.43,108.79c38.33,4.36,77.3-24.38,83.21-57.21,7.71-42.88,212-1214.63,215.84-1235S1282.82,27.79,1274.28,17.08Zm-28.35,47.58c-2.84,15-210.39,1163.78-217.48,1202.83s-34.34,43.22-55.05,40.86-892.81-98.8-915.71-101.89-22.71-26.15-19.8-37.23S362.38,105,369.29,81.55,392.73,49.4,405.63,49c0,0,800.88-17.07,817.77-17.25S1249.84,44.05,1245.93,64.66Z" fill="url(#gradient_01)"/>
        <path d="M20,1238.79c6,4.52,12.09,6.81,15.87,7.27,7.45.9,892.42,104.17,942.6,109.33,43.24,4.45,78.85-22.7,85.66-60.75,3.39-18.94,215.3-1218,219.12-1242.51,1.52-9.78.4-17.33-1.5-22.88a68.07,68.07,0,0,0-7.49-12.17h0c-8.54-10.72-22-17.26-34.89-17.08S409.44,20.89,409.44,20.89c-32.7.54-57.59,28.33-62.68,43.41S8.46,1159.43,2.28,1180.13A55.71,55.71,0,0,0,20,1238.79ZM6.32,1180.9C11.63,1163.6,343.12,86.45,350.94,64s39-38.68,51.6-39c0,0,798.56-20.32,836.85-20.12,26.44.14,41.7,26.15,37.88,50.26-2.37,15-211.28,1196.22-219.57,1240.52-4.72,25.24-32,57.39-79.26,52.26-18-2-900.72-103.11-936.42-108S1,1198.2,6.32,1180.9Z" fill="#fff" opacity="0.5"/>
        <path d="M1239.39,4.86C1201.1,4.66,402.54,25,402.54,25c-12.63.36-43.79,16.61-51.6,39S11.63,1163.6,6.32,1180.9s0,54.08,35.7,59,918.43,106.06,936.42,108c47.29,5.13,74.54-27,79.26-52.26,8.29-44.3,217.2-1225.52,219.57-1240.52C1281.09,31,1265.83,5,1239.39,4.86Zm6.54,59.8c-2.84,15-210.39,1163.78-217.48,1202.83s-34.34,43.22-55.05,40.86-892.81-98.8-915.71-101.89-22.71-26.15-19.8-37.23S362.38,105,369.29,81.55,392.73,49.4,405.63,49c0,0,800.88-17.07,817.77-17.25S1249.84,44.05,1245.93,64.66Z"/>
        <g id="details">
            <g>
                <path d="M1276.41,132.8l6-33.35h0a5.54,5.54,0,0,0-.06-1.75,8.62,8.62,0,0,0-.54-1.88,5.75,5.75,0,0,0-.78-1.49c-.27-.37-.19-.39-.43-.39a7.25,7.25,0,0,0-1.14.08c-.15,0-.29.12-.4.16a3.66,3.66,0,0,0-.82.42,1.61,1.61,0,0,0-.5.59,5.55,5.55,0,0,0-.32,1l-5.34,29.93a8.87,8.87,0,0,0,1.34,6.78h0a1.73,1.73,0,0,0,.6.53,1,1,0,0,0,.66.13,2.8,2.8,0,0,0,1.72-.81" fill="url(#gradient_02)"/>
                <path d="M1282.51,99.28c.31-2,.12-3.84-.86-4.88a1.25,1.25,0,0,0-1.64-.2,2.63,2.63,0,0,0-1.22,1.88l-5.07,29.38a9.33,9.33,0,0,0,1,6.56,1.18,1.18,0,0,0,1.76.39,3.64,3.64,0,0,0,1.28-2.52Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.2"/>
            </g>
            <g>
                <path d="M1268,180.62l6-33.35h0a5.54,5.54,0,0,0-.06-1.75,8.62,8.62,0,0,0-.54-1.88,5.92,5.92,0,0,0-.78-1.49c-.27-.37-.19-.38-.43-.39a7.25,7.25,0,0,0-1.14.08c-.15,0-.29.12-.4.16a3.89,3.89,0,0,0-.82.42,1.61,1.61,0,0,0-.5.59,5.55,5.55,0,0,0-.32,1.05L1263.63,174a8.85,8.85,0,0,0,1.34,6.77h0a1.73,1.73,0,0,0,.6.53,1,1,0,0,0,.66.13,2.74,2.74,0,0,0,1.72-.81" fill="url(#gradient_02-2)"/>
                <path d="M1274.05,147.1c.31-2,.12-3.83-.86-4.88a1.25,1.25,0,0,0-1.64-.2,2.63,2.63,0,0,0-1.22,1.88l-5.07,29.38a9.33,9.33,0,0,0,1,6.56,1.18,1.18,0,0,0,1.76.39,3.64,3.64,0,0,0,1.28-2.52Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.2"/>
            </g>
            <g>
                <path d="M900.93,1348.72a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a12,12,0,0,1-5.19-1.71C898.16,1349,899,1348.55,900.93,1348.72Z" fill="#000102"/>
                <path d="M904.26,1351.4c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,904.26,1351.4Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M901,1350.67a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M902.05,1349.06a11,11,0,0,1,4.59,1.43c.75.64-.07,1-1.86.78a10.31,10.31,0,0,1-4.33-1.39C899.69,1349.27,900.41,1348.9,902.05,1349.06Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M889,1347.34a12.48,12.48,0,0,1,5.56,1.75c1,.77-.07,1.18-2.22,1a11.75,11.75,0,0,1-5.2-1.71C886.23,1347.62,887.06,1347.18,889,1347.34Z" fill="#000102"/>
                <path d="M892.34,1350c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,892.34,1350Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M889.07,1349.29a15.25,15.25,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M890.12,1347.69a10.86,10.86,0,0,1,4.59,1.42c.76.64-.07,1-1.86.79a10.26,10.26,0,0,1-4.33-1.4C887.76,1347.89,888.49,1347.52,890.12,1347.69Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M877.08,1346a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.89,11.89,0,0,1-5.19-1.7C874.31,1346.25,875.13,1345.8,877.08,1346Z" fill="#000102"/>
                <path d="M880.41,1348.65c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,880.41,1348.65Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M877.15,1347.92a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M878.2,1346.31a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C875.84,1346.52,876.56,1346.15,878.2,1346.31Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M865.15,1344.59a12.61,12.61,0,0,1,5.56,1.74c1,.78-.07,1.19-2.22,1a11.88,11.88,0,0,1-5.2-1.71C862.38,1344.87,863.21,1344.42,865.15,1344.59Z" fill="#000102"/>
                <path d="M868.49,1347.27c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,868.49,1347.27Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M865.22,1346.54a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M866.27,1344.93a11,11,0,0,1,4.59,1.43c.76.64-.07,1-1.86.78a10.27,10.27,0,0,1-4.33-1.39C863.91,1345.14,864.64,1344.77,866.27,1344.93Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M853.23,1343.22a12.36,12.36,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.19-1.71C850.46,1343.5,851.28,1343.05,853.23,1343.22Z" fill="#000102"/>
                <path d="M856.56,1345.89c2.15.26,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,856.56,1345.89Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M853.3,1345.16a14.57,14.57,0,0,0,3.21.73l1.35-1.08-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M854.35,1343.56a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C852,1343.76,852.71,1343.4,854.35,1343.56Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M841.3,1341.84a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a12,12,0,0,1-5.2-1.71C838.53,1342.12,839.36,1341.67,841.3,1341.84Z" fill="#000102"/>
                <path d="M844.64,1344.52c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,844.64,1344.52Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M841.37,1343.79a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M842.42,1342.18a11,11,0,0,1,4.59,1.43c.76.64-.07,1-1.86.78a10.41,10.41,0,0,1-4.33-1.39C840.06,1342.39,840.79,1342,842.42,1342.18Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M829.38,1340.46a12.61,12.61,0,0,1,5.55,1.74c1,.78-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.19-1.71C826.61,1340.74,827.43,1340.3,829.38,1340.46Z" fill="#000102"/>
                <path d="M832.71,1343.14c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,832.71,1343.14Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M829.45,1342.41a15.16,15.16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M830.5,1340.81a10.86,10.86,0,0,1,4.59,1.42c.75.64-.07,1-1.86.79a10.16,10.16,0,0,1-4.33-1.4C828.14,1341,828.86,1340.64,830.5,1340.81Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M817.45,1339.09a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.89,11.89,0,0,1-5.2-1.7C814.68,1339.37,815.51,1338.92,817.45,1339.09Z" fill="#000102"/>
                <path d="M820.79,1341.77c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,820.79,1341.77Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M817.52,1341a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M818.57,1339.43a10.71,10.71,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C816.21,1339.64,816.94,1339.27,818.57,1339.43Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M805.53,1337.71a12.61,12.61,0,0,1,5.55,1.74c1,.78-.07,1.19-2.22,1a11.83,11.83,0,0,1-5.19-1.71C802.76,1338,803.58,1337.54,805.53,1337.71Z" fill="#000102"/>
                <path d="M808.86,1340.39c2.15.25,3.18-.16,2.22-1l-3.65.72A8.05,8.05,0,0,0,808.86,1340.39Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M805.6,1339.66a15.8,15.8,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M806.65,1338.05a11,11,0,0,1,4.59,1.43c.75.64-.07,1-1.86.78a10.17,10.17,0,0,1-4.33-1.39C804.29,1338.26,805,1337.89,806.65,1338.05Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M793.6,1336.34a12.36,12.36,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.2-1.71C790.83,1336.61,791.66,1336.17,793.6,1336.34Z" fill="#000102"/>
                <path d="M796.94,1339c2.15.26,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,796.94,1339Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M793.67,1338.28a15.25,15.25,0,0,0,3.22.73l1.35-1.08-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M794.72,1336.68a10.71,10.71,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C792.36,1336.88,793.09,1336.52,794.72,1336.68Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M781.68,1335a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a12,12,0,0,1-5.19-1.71C778.91,1335.24,779.73,1334.79,781.68,1335Z" fill="#000102"/>
                <path d="M785,1337.64c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,785,1337.64Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M781.75,1336.91a15.8,15.8,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M782.8,1335.3a10.85,10.85,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C780.44,1335.51,781.16,1335.14,782.8,1335.3Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M769.75,1333.58a12.61,12.61,0,0,1,5.56,1.74c1,.78-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.2-1.71C767,1333.86,767.81,1333.42,769.75,1333.58Z" fill="#000102"/>
                <path d="M773.09,1336.26c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,773.09,1336.26Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M769.82,1335.53a15.25,15.25,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M770.87,1333.93a10.86,10.86,0,0,1,4.59,1.42c.76.64-.07,1-1.86.79a10.26,10.26,0,0,1-4.33-1.4C768.51,1334.13,769.24,1333.76,770.87,1333.93Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M757.83,1332.21a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.7,11.7,0,0,1-5.19-1.71C755.06,1332.49,755.88,1332,757.83,1332.21Z" fill="#000102"/>
                <path d="M761.16,1334.89c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,761.16,1334.89Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M757.9,1334.16a15.8,15.8,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M759,1332.55a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C756.59,1332.76,757.31,1332.39,759,1332.55Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M745.9,1330.83a12.61,12.61,0,0,1,5.56,1.74c1,.78-.07,1.19-2.22,1a11.88,11.88,0,0,1-5.2-1.71C743.13,1331.11,744,1330.66,745.9,1330.83Z" fill="#000102"/>
                <path d="M749.24,1333.51c2.15.25,3.18-.16,2.22-1l-3.65.72A8.05,8.05,0,0,0,749.24,1333.51Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M746,1332.78a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M747,1331.17a11,11,0,0,1,4.59,1.43c.76.64-.07,1-1.86.78a10.27,10.27,0,0,1-4.33-1.39C744.66,1331.38,745.39,1331,747,1331.17Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M734,1329.46a12.36,12.36,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.7,11.7,0,0,1-5.19-1.71C731.21,1329.73,732,1329.29,734,1329.46Z" fill="#000102"/>
                <path d="M737.31,1332.13c2.15.26,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,737.31,1332.13Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M734.05,1331.4a15.16,15.16,0,0,0,3.21.73l1.35-1.08-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M735.1,1329.8a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C732.74,1330,733.46,1329.64,735.1,1329.8Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M722.05,1328.08a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.89,11.89,0,0,1-5.2-1.7C719.28,1328.36,720.11,1327.91,722.05,1328.08Z" fill="#000102"/>
                <path d="M725.39,1330.76c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,725.39,1330.76Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M722.12,1330a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M723.17,1328.42a10.85,10.85,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C720.81,1328.63,721.54,1328.26,723.17,1328.42Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M710.13,1326.7a12.61,12.61,0,0,1,5.55,1.74c1,.78-.07,1.19-2.22,1a11.7,11.7,0,0,1-5.19-1.71C707.36,1327,708.18,1326.54,710.13,1326.7Z" fill="#000102"/>
                <path d="M713.46,1329.38c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,713.46,1329.38Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M710.2,1328.65a15.16,15.16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M711.25,1327.05a10.86,10.86,0,0,1,4.59,1.42c.75.64-.07,1-1.86.78a10,10,0,0,1-4.33-1.39C708.89,1327.25,709.61,1326.88,711.25,1327.05Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M517.32,1304.87a43,43,0,0,0-11.49-2.91l-30.57-3.45c-4.16-.47-6.93.26-5.29,1.49s5.82,2.42,10.29,3l34.06,3.9C519,1307.37,520.47,1306.31,517.32,1304.87Z" fill="#3d3d3d"/>
                <path d="M519,1305.05a41.22,41.22,0,0,0-11.25-2.82l-32.19-3.63c-4.05-.47-6.7.26-5.12,1.42s5.64,2.37,10,2.83l35.75,4.07C520.72,1307.48,522.1,1306.45,519,1305.05Z" fill="url(#gradient_03)"/>
                <path d="M517.8,1305a37.15,37.15,0,0,0-10-2.55L477.44,1299c-3.68-.41-6.09.22-4.51,1.29s5.09,2.11,8.89,2.55l33.34,3.87C519.27,1307.18,520.53,1306.27,517.8,1305Z" fill="#131313"/>
            </g>
            <g>
                <path d="M293.7,1278.42a12.48,12.48,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.19-1.71C290.93,1278.7,291.75,1278.25,293.7,1278.42Z" fill="#000102"/>
                <path d="M297,1281.09c2.15.26,3.18-.16,2.22-1l-3.65.73A8.06,8.06,0,0,0,297,1281.09Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M293.77,1280.37a15.29,15.29,0,0,0,3.21.72l1.35-1.08-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M294.82,1278.76a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C292.46,1279,293.18,1278.6,294.82,1278.76Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M281.77,1277a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.88,11.88,0,0,1-5.2-1.71C279,1277.32,279.83,1276.87,281.77,1277Z" fill="#000102"/>
                <path d="M285.11,1279.72c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,285.11,1279.72Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M281.84,1279a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M282.89,1277.38a11,11,0,0,1,4.59,1.43c.76.64-.07,1-1.86.78a10.27,10.27,0,0,1-4.33-1.39C280.53,1277.59,281.26,1277.22,282.89,1277.38Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M269.85,1275.66a12.48,12.48,0,0,1,5.55,1.75c1,.77-.07,1.18-2.22,1a11.75,11.75,0,0,1-5.19-1.71C267.08,1275.94,267.9,1275.5,269.85,1275.66Z" fill="#000102"/>
                <path d="M273.18,1278.34c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,273.18,1278.34Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M269.92,1277.61a15.3,15.3,0,0,0,3.21.73l1.35-1.08-1.22-.3Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M271,1276a10.86,10.86,0,0,1,4.59,1.42c.75.64-.07,1-1.86.79a10.16,10.16,0,0,1-4.33-1.4C268.61,1276.21,269.33,1275.85,271,1276Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M257.92,1274.29a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.89,11.89,0,0,1-5.2-1.7C255.15,1274.57,256,1274.12,257.92,1274.29Z" fill="#000102"/>
                <path d="M261.26,1277c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,261.26,1277Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M258,1276.24a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M259,1274.63a10.71,10.71,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C256.68,1274.84,257.41,1274.47,259,1274.63Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M246,1272.91a12.61,12.61,0,0,1,5.55,1.74c1,.78-.07,1.19-2.22,1a11.88,11.88,0,0,1-5.19-1.71C243.23,1273.19,244.05,1272.75,246,1272.91Z" fill="#000102"/>
                <path d="M249.33,1275.59c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,249.33,1275.59Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M246.07,1274.86a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M247.12,1273.26a10.86,10.86,0,0,1,4.59,1.42c.75.64-.07,1-1.86.78a10.17,10.17,0,0,1-4.33-1.39C244.76,1273.46,245.48,1273.09,247.12,1273.26Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M234.07,1271.54a12.36,12.36,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.2-1.71C231.3,1271.82,232.13,1271.37,234.07,1271.54Z" fill="#000102"/>
                <path d="M237.41,1274.21c2.15.26,3.18-.16,2.22-1L236,1274A7.24,7.24,0,0,0,237.41,1274.21Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M234.14,1273.48a14.66,14.66,0,0,0,3.22.73l1.35-1.08-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M235.19,1271.88a10.71,10.71,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C232.83,1272.08,233.56,1271.72,235.19,1271.88Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M222.15,1270.16a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a12,12,0,0,1-5.19-1.71C219.38,1270.44,220.2,1270,222.15,1270.16Z" fill="#000102"/>
                <path d="M225.48,1272.84c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,225.48,1272.84Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M222.22,1272.11a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M223.27,1270.5a11,11,0,0,1,4.59,1.43c.75.64-.07,1-1.86.78a10.31,10.31,0,0,1-4.33-1.39C220.91,1270.71,221.63,1270.34,223.27,1270.5Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M210.22,1268.78a12.48,12.48,0,0,1,5.56,1.75c1,.77-.07,1.18-2.22,1a11.75,11.75,0,0,1-5.2-1.71C207.45,1269.06,208.28,1268.62,210.22,1268.78Z" fill="#000102"/>
                <path d="M213.56,1271.46c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,213.56,1271.46Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M210.29,1270.73a15.25,15.25,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M211.34,1269.13a10.86,10.86,0,0,1,4.59,1.42c.76.64-.07,1-1.86.79a10.26,10.26,0,0,1-4.33-1.4C209,1269.33,209.71,1269,211.34,1269.13Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M198.3,1267.41a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.89,11.89,0,0,1-5.19-1.7C195.53,1267.69,196.35,1267.24,198.3,1267.41Z" fill="#000102"/>
                <path d="M201.63,1270.09c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,201.63,1270.09Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M198.37,1269.36a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M199.42,1267.75a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C197.06,1268,197.78,1267.59,199.42,1267.75Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M186.37,1266a12.61,12.61,0,0,1,5.56,1.74c1,.78-.07,1.19-2.22,1a11.88,11.88,0,0,1-5.2-1.71C183.6,1266.31,184.43,1265.86,186.37,1266Z" fill="#000102"/>
                <path d="M189.71,1268.71c2.15.25,3.18-.16,2.22-1l-3.65.72A8.05,8.05,0,0,0,189.71,1268.71Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M186.44,1268a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M187.49,1266.37a11,11,0,0,1,4.59,1.43c.76.64-.07,1-1.86.78a10.27,10.27,0,0,1-4.33-1.39C185.13,1266.58,185.86,1266.21,187.49,1266.37Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M174.45,1264.66a12.36,12.36,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.19-1.71C171.68,1264.94,172.5,1264.49,174.45,1264.66Z" fill="#000102"/>
                <path d="M177.78,1267.33c2.15.26,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,177.78,1267.33Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M174.52,1266.6a14.7,14.7,0,0,0,3.21.73l1.35-1.08-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M175.57,1265a10.71,10.71,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C173.21,1265.2,173.93,1264.84,175.57,1265Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M162.52,1263.28a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a12,12,0,0,1-5.2-1.71C159.75,1263.56,160.58,1263.11,162.52,1263.28Z" fill="#000102"/>
                <path d="M165.86,1266c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,165.86,1266Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M162.59,1265.23a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M163.64,1263.62a11,11,0,0,1,4.59,1.43c.76.64-.07,1-1.86.78a10.41,10.41,0,0,1-4.33-1.39C161.28,1263.83,162,1263.46,163.64,1263.62Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M150.6,1261.9a12.61,12.61,0,0,1,5.55,1.74c1,.78-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.19-1.71C147.83,1262.18,148.65,1261.74,150.6,1261.9Z" fill="#000102"/>
                <path d="M153.93,1264.58c2.15.25,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,153.93,1264.58Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M150.67,1263.85a15.3,15.3,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M151.72,1262.25a10.86,10.86,0,0,1,4.59,1.42c.75.64-.07,1-1.86.79a10.16,10.16,0,0,1-4.33-1.4C149.36,1262.45,150.08,1262.08,151.72,1262.25Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M138.67,1260.53a12.61,12.61,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.89,11.89,0,0,1-5.2-1.7C135.9,1260.81,136.73,1260.36,138.67,1260.53Z" fill="#000102"/>
                <path d="M142,1263.21c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,142,1263.21Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M138.74,1262.48a15.9,15.9,0,0,0,3.22.73l1.35-1.09-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M139.79,1260.87a10.71,10.71,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C137.43,1261.08,138.16,1260.71,139.79,1260.87Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M126.75,1259.15a12.61,12.61,0,0,1,5.55,1.74c1,.78-.07,1.19-2.22,1a11.88,11.88,0,0,1-5.19-1.71C124,1259.43,124.8,1259,126.75,1259.15Z" fill="#000102"/>
                <path d="M130.08,1261.83c2.15.25,3.18-.16,2.22-1l-3.65.72A8.05,8.05,0,0,0,130.08,1261.83Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M126.82,1261.1a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M127.87,1259.49a11,11,0,0,1,4.59,1.43c.75.64-.07,1-1.86.78a10.17,10.17,0,0,1-4.33-1.39C125.51,1259.7,126.23,1259.33,127.87,1259.49Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M114.82,1257.78a12.36,12.36,0,0,1,5.56,1.74c1,.77-.07,1.19-2.22,1a11.75,11.75,0,0,1-5.2-1.71C112.05,1258.05,112.88,1257.61,114.82,1257.78Z" fill="#000102"/>
                <path d="M118.16,1260.45c2.15.26,3.18-.16,2.22-1l-3.65.72A7.24,7.24,0,0,0,118.16,1260.45Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M114.89,1259.72a15.25,15.25,0,0,0,3.22.73l1.35-1.08-1.23-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M115.94,1258.12a10.71,10.71,0,0,1,4.59,1.43c.76.63-.07,1-1.86.78a10.26,10.26,0,0,1-4.33-1.4C113.58,1258.32,114.31,1258,115.94,1258.12Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M102.9,1256.4a12.61,12.61,0,0,1,5.55,1.74c1,.77-.07,1.19-2.22,1a12,12,0,0,1-5.19-1.71C100.13,1256.68,101,1256.23,102.9,1256.4Z" fill="#000102"/>
                <path d="M106.23,1259.08c2.15.25,3.18-.17,2.22-1l-3.65.73A9.08,9.08,0,0,0,106.23,1259.08Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M103,1258.35a16,16,0,0,0,3.21.73l1.35-1.09-1.22-.29Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M104,1256.74a10.85,10.85,0,0,1,4.59,1.43c.75.63-.07,1-1.86.78a10.16,10.16,0,0,1-4.33-1.4C101.66,1257,102.38,1256.58,104,1256.74Z" fill="#0a0e0e"/>
            </g>
            <g>
                <ellipse cx="799.61" cy="23.85" rx="6.85" ry="4.95" transform="translate(302.88 650.91) rotate(-53.32)" fill="#1a1c1c"/>
                <ellipse cx="799.6" cy="23.86" rx="4.47" ry="3.25" transform="translate(302.89 650.92) rotate(-53.33)" fill="url(#gradient_04)"/>
            </g>
        </g>
        <path d="M1169.05,32.88C986,36.67,405.63,49,405.63,49c-12.9.36-29.44,9.08-36.34,32.51C365,96.06,240.07,505.41,144.86,817.69Z" opacity="0.4" fill="url(#gradient_05)"/>
        <path d="M1274.28,17.08c-8.54-10.72-22-17.26-34.89-17.08S409.44,20.89,409.44,20.89c-32.7.54-57.59,28.33-62.68,43.4S8.46,1159.43,2.28,1180.13s-.73,49.58,28,65.75c0,0,9.45,6,25.43,8.89s917.52,106.87,934.43,108.79c38.33,4.36,77.3-24.38,83.21-57.21,7.71-42.88,212-1214.63,215.84-1235S1282.82,27.79,1274.28,17.08Zm-478.63,3.8c2.26-3,5.87-4.16,8.06-2.52s2.13,5.42-.13,8.45-5.87,4.16-8.06,2.53S793.39,23.92,795.65,20.88Zm456,38.52c-2.84,15-210,1175.48-217,1214.52s-34.34,43.23-55.05,40.87-905-98.44-927.91-101.53S29,1187.11,31.88,1176,358.26,99,365.17,75.56,388.6,43.41,401.5,43.05c0,0,810.75-16.35,827.65-16.53S1255.59,38.78,1251.68,59.4Z" fill="#383E45" style="mix-blend-mode: overlay" opacity="0.75"/>
    </g>
</svg>
