<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 1150 612" data-forced-size="true" width="1150" height="612" data-img-aspect-ratio="16:10" data-img-perspective="[[54.34, 15.04], [98.36, 4.46], [88.91, 88.4], [44.96, 89.01]]">
    <defs>
        <linearGradient id="gradient_01" x1="1025.31" y1="604.5" x2="1085.34" y2="604.5" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#575759"/>
            <stop offset="0.29" stop-color="#cbccd1"/>
            <stop offset="0.47" stop-color="#d0d1d6"/>
            <stop offset="0.71" stop-color="#cbccd1"/>
            <stop offset="1" stop-color="#575759"/>
        </linearGradient>
        <linearGradient id="gradient_01-2" x1="648.75" y1="609" x2="709.9" y2="609" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_01-3" x1="215.5" y1="606" x2="276.66" y2="606" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_02" x1="12.39" y1="0.67" x2="141.07" y2="598.12" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#8a8a8a"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.01" stop-color="#3a3330"/>
            <stop offset="0.01" stop-color="#575757"/>
            <stop offset="0.07" stop-color="#8a8a8a"/>
            <stop offset="0.48" stop-color="#a1a1a1"/>
            <stop offset="0.51" stop-color="#8a8a8a"/>
            <stop offset="0.56" stop-color="#787878"/>
            <stop offset="0.87" stop-color="#646464"/>
            <stop offset="0.93" stop-color="#414141"/>
            <stop offset="0.97" stop-color="#2c2c2c"/>
            <stop offset="0.98"/>
            <stop offset="1" stop-color="#646464"/>
        </linearGradient>
        <linearGradient id="gradient_03" x1="60.05" y1="304.92" x2="68.8" y2="303.1" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#121212"/>
            <stop offset="0" stop-color="#0d0d0d"/>
            <stop offset="0.06" stop-color="#0e0e0e" stop-opacity="0.83"/>
            <stop offset="0.41" stop-color="#111" stop-opacity="0.19"/>
            <stop offset="1" stop-color="#121212" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="gradient_04" x1="384.52" y1="554.79" x2="384.52" y2="575.15" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#191919"/>
            <stop offset="1" stop-color="#080808"/>
        </linearGradient>
        <linearGradient id="gradient_05" x1="140.65" y1="590.51" x2="1150" y2="590.51" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#a0a1a5"/>
            <stop offset="0.01" stop-color="#d3d4d9"/>
            <stop offset="0.02" stop-color="#6a6b70"/>
            <stop offset="0.03" stop-color="#797a7f"/>
            <stop offset="0.04" stop-color="#a8a9ae"/>
            <stop offset="0.5" stop-color="#a8a9ae"/>
            <stop offset="0.55" stop-color="#d3d4d9"/>
            <stop offset="0.57" stop-color="#797a7f"/>
            <stop offset="0.59" stop-color="#a8a9ae"/>
            <stop offset="0.98" stop-color="#a8a9ae"/>
            <stop offset="0.99" stop-color="#d3d4d9"/>
            <stop offset="1" stop-color="#a0a1a5"/>
        </linearGradient>
        <linearGradient id="gradient_06" x1="646.55" y1="571.15" x2="646.55" y2="609.7" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#858688" stop-opacity="0"/>
            <stop offset="0.6" stop-color="#737479"/>
            <stop offset="0.73" stop-color="#3d3d40"/>
            <stop offset="0.85" stop-color="#858688"/>
            <stop offset="0.88" stop-color="#7e7f81"/>
            <stop offset="0.92" stop-color="#6c6c6f"/>
            <stop offset="0.98" stop-color="#4d4d50"/>
            <stop offset="1" stop-color="#3d3d40"/>
        </linearGradient>
        <linearGradient id="gradient_07" x1="902.79" y1="576.9" x2="998.72" y2="576.9" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#575759"/>
            <stop offset="0.77" stop-color="#cbccd1"/>
            <stop offset="1" stop-color="#cbccd1"/>
        </linearGradient>
        <linearGradient id="gradient_08" x1="902.79" y1="576.9" x2="998.72" y2="576.9" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#575759"/>
            <stop offset="0.1" stop-color="#cbccd1" stop-opacity="0"/>
            <stop offset="0.47" stop-color="#d0d1d6" stop-opacity="0"/>
            <stop offset="0.9" stop-color="#cbccd1" stop-opacity="0"/>
            <stop offset="1" stop-color="#575759"/>
        </linearGradient>
        <linearGradient id="gradient_09" x1="501.36" y1="599.14" x2="501.36" y2="603.51" gradientTransform="matrix(-1, 0, 0, 1, 1150, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0.46" stop-color="#333"/>
            <stop offset="0.59" stop-color="#5f6061"/>
            <stop offset="0.78" stop-color="#999a9d"/>
            <stop offset="0.93" stop-color="#bdbec3"/>
            <stop offset="1" stop-color="#cbccd1"/>
        </linearGradient>
        <radialGradient id="gradient_10" cx="298.15" cy="53.91" r="2.86" gradientTransform="matrix(-0.16, -0.99, -0.99, 0.16, 952.27, 340.48)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#164467"/>
            <stop offset="0.16" stop-color="#164365"/>
            <stop offset="0.26" stop-color="#153e5c"/>
            <stop offset="0.34" stop-color="#15354e"/>
            <stop offset="0.41" stop-color="#142a3a"/>
            <stop offset="0.47" stop-color="#131a20"/>
            <stop offset="0.5" stop-color="#121212"/>
            <stop offset="1" stop-color="#0d0d0d"/>
        </radialGradient>
        <linearGradient id="gradient_11" x1="278.02" y1="583.03" x2="338.65" y2="583.03" gradientTransform="matrix(1, 0, 0, -1, 533.33, 1166.07)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#525252"/>
            <stop offset="0.04" stop-color="#4a4a4a"/>
            <stop offset="0.17" stop-color="#2f2f2f"/>
            <stop offset="0.33" stop-color="#1a1a1a"/>
            <stop offset="0.49" stop-color="#0b0b0b"/>
            <stop offset="0.69" stop-color="#030303"/>
            <stop offset="1"/>
        </linearGradient>
        <linearGradient id="gradient_11-2" x1="235.93" y1="583.03" x2="258.57" y2="583.03" gradientTransform="matrix(1, 0, 0, -1, 655.5, 1166.07)" xlink:href="#gradient_11"/>
        <linearGradient id="gradient_12" x1="933.54" y1="584.57" x2="967.18" y2="584.57" gradientUnits="userSpaceOnUse">
            <stop offset="0"/>
            <stop offset="0.31" stop-color="#030303"/>
            <stop offset="0.51" stop-color="#0b0b0b"/>
            <stop offset="0.67" stop-color="#1a1a1a"/>
            <stop offset="0.83" stop-color="#2f2f2f"/>
            <stop offset="0.96" stop-color="#4a4a4a"/>
            <stop offset="1" stop-color="#525252"/>
        </linearGradient>
        <linearGradient id="gradient_13" x1="614.08" y1="236.13" x2="830.03" y2="346.57" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <polygon points="1134.07 24 624.93 92.1 517.09 544.79 1022.55 541.06 1134.07 24"/>
        </clipPath>
        <path id="filterPath" d="M0.8892,0.8841l-0.4395,0.0061L0.5434,0.1505l0.4402-0.1059Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M497.85,610.6l-.25.3h0a3,3,0,0,1-2.33,1.1H445.7a3,3,0,0,1-2.32-1.1h0l-.26-.3Z"/>
        <path d="M64.66,601.37s4.64,6.27,7.09,6.27h49.93c2.32,0,2.86-4.91,2.86-4.91Z" fill="url(#gradient_01)"/>
        <path d="M445.7,612h49.57a3,3,0,0,0,2.33-1.1h0c1.59-2,5.15-4.9,2.62-4.9H441c-2.53,0,.76,2.94,2.36,4.9h0A3,3,0,0,0,445.7,612Z" fill="url(#gradient_01-2)"/>
        <path d="M879,609h49.57a3,3,0,0,0,2.32-1.1h0c1.6-2,5.16-4.9,2.63-4.9h-59.2c-2.53,0,.75,2.94,2.35,4.9h0A3,3,0,0,0,879,609Z" fill="url(#gradient_01-3)"/>
        <path d="M498.18,583.51l501.45,15c12.82,0,29.19-.81,33-21.26s114-539.59,116.5-552.13-.55-20.45-6.28-22.91S1129-.25,1123,.29,651,68.46,637.59,70.09s-17.46,12.55-19.1,18.54S498.18,583.51,498.18,583.51Zm18.91-38.72L624.93,92.1l506.25-64.79c-9.76,46.32-108.63,513.75-108.63,513.75Z"/>
        <path d="M989.68,598.21l6.31-3.52,8.82-.69c8.19-.68,17.05-8.45,19.92-21.4s115-542.18,116.77-550.9,3.68-19.36-12.14-21.41l-2-.26c5.22-.15,11.11.28,15.54,2.17,5.73,2.46,8.73,10.36,6.28,22.91s-112.68,531.68-116.5,552.13-20.19,21.26-33,21.26Z" fill="url(#gradient_02)"/>
        <path d="M989.68,598.21l6.31-3.52,8.82-.69c8.19-.68,17.05-8.45,19.92-21.4s115-542.18,116.77-550.9,3.68-19.36-12.14-21.41l-2-.26c5.22-.15,11.11.28,15.54,2.17,5.73,2.46,8.73,10.36,6.28,22.91s-112.68,531.68-116.5,552.13-20.19,21.26-33,21.26Z" opacity="0.5" fill="url(#gradient_03)"/>
        <path d="M1004.81,594c12-1.35,19-13.39,20.77-24.34l1-4.78,8.36-38.2c32.65-154.95,73-342.52,105.17-497.39,1.1-6.39,3.37-12.8,2.36-19.35-1.36-7.33-8.61-9.32-15.19-9.91,6.42.7,13.68,2.61,15,10,.91,6.49-1.44,12.89-2.62,19.21C1106.25,183.75,1067,371.65,1034,526.47c-.85,3.89-8.86,43.72-9.9,47.86-2.38,9.21-9.26,18.5-19.28,19.67Z" fill="#fff"/>
        <path d="M1027.46,552.7,508,555.61l-4.5,18.76,498,3.78h15.09C1021.55,578.15,1025.18,566.15,1027.46,552.7Z" fill="url(#gradient_04)"/>
        <path d="M990.35,606.38c8.55,0,16.55-4.18,16.55-12.91V571.78H0v18.54c0,5.73,2.32,12.27,16.37,12.27H120.59c2.91,0,3.27,3.91,3.27,3.91l347.4,2.73,459.25-2.91Z" fill="url(#gradient_05)"/>
        <path d="M990.35,606.38c8.55,0,16.55-4.18,16.55-12.91V571.78H0v18.54c0,5.73,2.32,12.27,16.37,12.27H120.59c2.91,0,3.27,3.91,3.27,3.91l347.4,2.73,459.25-2.91Z" fill-opacity="0.7" fill="url(#gradient_06)"/>
        <path d="M247,571.78l-.07.14S244.72,582,239.4,582H157.28c-2.18,0-6-3.41-6-10.09v-.14Z" opacity="0.63" fill="url(#gradient_07)"/>
        <path d="M247,571.78l-.07.14S244.72,582,239.4,582H157.28c-2.18,0-6-3.41-6-10.09v-.14Z" opacity="0.63" fill="url(#gradient_08)"/>
        <g opacity="0.63">
            <path d="M247,571.78c-1.3,3.67-3.15,9.61-7.71,9.72H158c-4.57.24-6.59-6-6.69-9.73-.27,4.17,1.63,10.65,6.69,10.73h81.29c5.13-.4,6.68-6.62,7.72-10.73Z" fill="#fff"/>
        </g>
        <path d="M515.09,599.05c-2.18,0-7.23,4.91-7.23,4.91H787.78a3.53,3.53,0,0,0,1.64-2.87,2,2,0,0,0-1.91-2Z" fill="url(#gradient_09)"/>
        <path d="M787.78,604c1.82-1.08,2.56-4.58-.27-4.89-23.43.12-46.88.11-70.32.1-58,0-135.62.09-193.37-.06l-8.79,0c-2.85.64-5,3-7.17,4.87,2.14-1.87,4.31-4.28,7.17-4.93l8.79,0c57.65-.15,135.45,0,193.37-.07,23.44,0,46.88,0,70.32.11,2.65.3,2.33,3.76.27,4.93Z" fill="#fff"/>
        <ellipse cx="851.85" cy="54.63" rx="3.41" ry="2.18" transform="translate(662.98 887.07) rotate(-80.89)" fill="url(#gradient_10)"/>
        <path d="M121.68,607.64H71.75c-1,0-2.38-1.07-3.66-2.32h55.84C123.5,606.5,122.79,607.64,121.68,607.64Z"/>
        <path d="M876.69,608l54.47-.44-.32.38a3,3,0,0,1-2.32,1.1H879A3,3,0,0,1,876.69,608Z"/>
        <path d="M0,571.78v18.54c0,5.73,2.32,12.27,16.37,12.27H65.62a25,25,0,0,0,4.2,4.22l.27.18.23.14.3.17.21.1a1.75,1.75,0,0,0,.32.12l.16.05a1.75,1.75,0,0,0,.44.07h49.93a1.4,1.4,0,0,0,.41-.06l.13-.05.23-.1a.91.91,0,0,0,.16-.11,1,1,0,0,0,.17-.12L123,607a1.59,1.59,0,0,0,.13-.13c.06-.07.11-.15.17-.23l.09-.12c.06-.1.12-.2.17-.3a.41.41,0,0,0,0-.09l.18-.36a7,7,0,0,1,.12.7L441.7,609c.48.54,1,1.09,1.42,1.6l.26.3a2.93,2.93,0,0,0,1.36.92,2.87,2.87,0,0,0,1,.18h49.57a3,3,0,0,0,1.29-.29,2.75,2.75,0,0,0,.56-.35,3.46,3.46,0,0,0,.48-.46l.25-.3c.42-.49.93-1,1.42-1.55l376.28-2.38c.38.43.76.84,1.07,1.23l.07.06h0a2.87,2.87,0,0,0,1,.76l.06,0a3.42,3.42,0,0,0,.54.18,4.22,4.22,0,0,0,.61.07h49.61a3.12,3.12,0,0,0,2.32-1.1l.09-.11c.39-.46.86-1,1.34-1.47l58.08.06c6.75,0,13.14-2.62,15.54-8,11.41-.61,23.53-3.91,26.75-21.13,3.82-20.45,114-539.59,116.5-552.13s-.55-20.45-6.28-22.91S1129-.25,1123,.29,651,68.46,637.59,70.09s-17.46,12.55-19.1,18.54C617,94.08,519.08,497.43,501,571.78H0Zm517.09-27L624.93,92.1l506.25-64.79c-9.76,46.32-108.63,513.75-108.63,513.75Zm-398.54,57.8h2a3.43,3.43,0,0,1,.57.06Z" opacity="0.3"/>
        <rect x="811.35" y="578.94" width="60.63" height="8.18" rx="4.09" transform="translate(1683.33 1166.07) rotate(180)" fill="url(#gradient_11)"/>
        <rect x="891.43" y="578.94" width="22.64" height="8.18" rx="4.09" transform="translate(1805.5 1166.07) rotate(180)" fill="url(#gradient_11-2)"/>
        <path d="M906,582.85v.27a.77.77,0,0,1-.76.77H891.52a4.11,4.11,0,0,1,0-1.71h13.83A.67.67,0,0,1,906,582.85Z" fill="#c1c1c1" fill-opacity="0.7"/>
        <polygon points="933.54 577.86 967.18 577.86 967.18 586.44 962.35 591.28 938.85 591.28 933.71 586.13 933.54 577.86" fill="url(#gradient_12)"/>
        <path d="M954,582.66H933.63l0,1.7h20.22a.76.76,0,0,0,.76-.76v-.27A.67.67,0,0,0,954,582.66Z" fill="#c1c1c1" fill-opacity="0.7"/>
        <g>
            <path d="M118.55,602.59l2.61.06a3.43,3.43,0,0,0-.57-.06Z" fill="none"/>
            <path d="M517.09,544.79l505.46-3.73s98.87-467.43,108.63-513.75L624.93,92.1Z" fill="none"/>
            <path d="M1142.86,2.2C1137.13-.25,1129-.25,1123,.29S651,68.46,637.59,70.09s-17.46,12.55-19.1,18.54C617,94.08,519.08,497.43,501,571.78H0v18.54c0,5.73,2.32,12.27,16.37,12.27H65.62a25,25,0,0,0,4.2,4.22l.27.18.23.14.3.17.21.1a1.75,1.75,0,0,0,.32.12l.16.05a1.75,1.75,0,0,0,.44.07h49.93a1.4,1.4,0,0,0,.41-.06l.13-.05.23-.1a.91.91,0,0,0,.16-.11,1,1,0,0,0,.17-.12L123,607a1.59,1.59,0,0,0,.13-.13c.06-.07.11-.15.17-.23l.09-.12c.06-.1.12-.2.17-.3a.41.41,0,0,0,0-.09l.18-.36a7,7,0,0,1,.12.7L441.7,609c.48.54,1,1.09,1.42,1.6l.26.3a2.93,2.93,0,0,0,1.36.92,2.87,2.87,0,0,0,1,.18h49.57a3,3,0,0,0,1.29-.29,2.5,2.5,0,0,0,.56-.35,3.46,3.46,0,0,0,.48-.46l.25-.3c.42-.49.93-1,1.43-1.55l376.27-2.38c.38.43.76.84,1.07,1.23l.07.06h0a2.87,2.87,0,0,0,1,.76l.06,0a3.42,3.42,0,0,0,.54.18,4.22,4.22,0,0,0,.61.07h49.61a3.12,3.12,0,0,0,2.32-1.1l.09-.11c.39-.46.86-1,1.34-1.47l58.08.06c6.75,0,13.14-2.62,15.54-8,11.41-.61,23.53-3.91,26.75-21.13,3.82-20.45,114-539.59,116.5-552.13S1148.59,4.66,1142.86,2.2ZM118.55,602.59h2a3.43,3.43,0,0,1,.57.06ZM906,583.12a.77.77,0,0,1-.76.77H891.52a4.11,4.11,0,0,1,0-1.71h13.83a.67.67,0,0,1,.67.67Zm48.63.48a.76.76,0,0,1-.76.76H933.67l0-1.7H954a.67.67,0,0,1,.67.67Zm67.9-42.54-505.46,3.73L624.93,92.1l506.25-64.79C1121.42,73.63,1022.55,541.06,1022.55,541.06Z" fill="#F6F6F6" style="mix-blend-mode: overlay" opacity="0.75"/>
        </g>
        <path d="M948.38,48.84,624.93,92.1,517.09,544.79l94-.69C711.87,395.54,855.48,184.42,948.38,48.84Z" opacity="0.2" fill="url(#gradient_13)"/>
    </g>
</svg>
