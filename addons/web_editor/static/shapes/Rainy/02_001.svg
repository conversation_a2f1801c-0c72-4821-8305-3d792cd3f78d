<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1400 1400">
    <defs>
        <linearGradient id="linear-gradient" x1="889.95" y1="-428.8" x2="34.92" y2="117.04"
        gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#3aadaa" stop-opacity="0" />
            <stop offset="1" stop-color="#3aadaa" stop-opacity="0.3" />
        </linearGradient>
        <linearGradient id="linear-gradient-2" x1="-1.38" y1="158.55" x2="290.16" y2="278.85"
        gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#3aadaa" stop-opacity="0" />
            <stop offset="1" stop-color="#3aadaa" />
        </linearGradient>
        <linearGradient id="linear-gradient-3" x1="3360.57" y1="-4639.02" x2="3128.42" y2="-4550.67"
        gradientTransform="translate(-3129.22 4963.3)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#383e45" />
            <stop offset="1" stop-color="#3aadaa" />
        </linearGradient>
        <linearGradient id="linear-gradient-4" x1="179.45" y1="273.24" x2="610.33" y2="150.45"
        gradientTransform="translate(186.2 -188.2) rotate(34.02)" xlink:href="#linear-gradient-2" />
        <linearGradient id="linear-gradient-5" x1="3685.8" y1="-4747.57" x2="3277.96" y2="-4592.34"
        gradientTransform="translate(-3213.84 4840.82)" xlink:href="#linear-gradient-2" />
        <linearGradient id="linear-gradient-6" x1="139.88" y1="366.5" x2="520.75" y2="366.5"
        gradientTransform="translate(261.6 -122.08) rotate(34.02)" xlink:href="#linear-gradient-2" />
        <linearGradient id="linear-gradient-7" x1="3614.91" y1="-4654.78" x2="3849.27" y2="-4554.34"
        gradientTransform="translate(-3216.09 4700.64)" xlink:href="#linear-gradient-3" />
        <linearGradient id="linear-gradient-8" x1="3864.59" y1="-4257.5" x2="3488.99" y2="-4532.16"
        gradientTransform="translate(-3083.61 4675.81)" xlink:href="#linear-gradient-3" />
        <linearGradient id="linear-gradient-9" x1="3707.04" y1="-4424.5" x2="4113.36" y2="-4587.33"
        gradientTransform="translate(-3177.31 4595.72)" gradientUnits="userSpaceOnUse">
            <stop offset="0.01" stop-color="#ffffff" />
            <stop offset="1" stop-color="#3aadaa" />
        </linearGradient>
        <linearGradient id="linear-gradient-10" x1="475.66" y1="94.68" x2="309.78" y2="271.21"
        gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#linear-gradient-3" />
        <linearGradient id="linear-gradient-11" x1="137.82" y1="496.61" x2="248.33" y2="420.7"
        gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" />
            <stop offset="0.44" stop-color="#3aadaa" stop-opacity="0" />
        </linearGradient>
        <g id="animations">
            <animateTransform xlink:href="#shape_1" attributeName="transform" attributeType="XML" type="translate" dur="10s" values="0 0;10 -10;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".445 .05 .55 .95;.445 .05 .55 .95"/>
            <animateTransform xlink:href="#shape_2" attributeName="transform" attributeType="XML" type="translate" dur="7s" values="0 0;10 -10;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".445 .05 .55 .95;.445 .05 .55 .95"/>
            <animateTransform xlink:href="#shape_3" attributeName="transform" attributeType="XML" type="translate" dur="5s" values="0 0;10 -10;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".445 .05 .55 .95;.445 .05 .55 .95"/>
          </g>
    </defs>
    <g>
        <polygon points="424.92 -3.37 -1.3 -3.37 -1.3 283.69 424.92 -3.37" style="fill: url(#linear-gradient)" />
        <path d="M283.08,91.79h0a55.76,55.76,0,0,0-77.19-15L-3,217.83V352l271.1-183A55.77,55.77,0,0,0,283.08,91.79Z"
        style="fill: url(#linear-gradient-2)" />
        <g id="shape_1" style="opacity: 0.73">
            <image width="230" height="170" transform="translate(7.11 283.13)"
            xlink:href="data:image/png;base64,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"
            style="opacity: 0.30000000000000004;mix-blend-mode: multiply" />
            <rect x="-5.29" y="348.93" width="254.6" height="33.94" rx="16.97"
            transform="matrix(0.83, -0.56, 0.56, 0.83, -183.84, 130.9)" style="fill: url(#linear-gradient-3)" />
        </g>
        <rect x="231.58" y="187.66" width="338.17" height="45.07" rx="22.54"
        transform="matrix(0.83, -0.56, 0.56, 0.83, -49.02, 260.16)" style="fill: url(#linear-gradient-4)" />
        <rect x="56.23" y="136.55" width="447.3" height="59.62" rx="29.81"
        transform="translate(-45.17 185.07) rotate(-34.02)" style="fill: url(#linear-gradient-5)" />
        <rect x="106.67" y="336.69" width="447.3" height="59.62" rx="29.81"
        transform="translate(-148.52 247.55) rotate(-34.02)" style="fill: url(#linear-gradient-6)" />
        <g id="shape_2" style="opacity: 0.73">
            <image width="126" height="102" transform="translate(444.7 45.73)"
            xlink:href="data:image/png;base64,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"
            style="opacity: 0.30000000000000004;mix-blend-mode: multiply" />
            <rect x="448.13" y="77.68" width="119.43" height="29.81" rx="14.9"
            transform="translate(35.13 299.99) rotate(-34.02)" style="fill: url(#linear-gradient-7)" />
        </g>
        <g id="shape_3" style="opacity: 0.73">
            <image width="216" height="172" transform="translate(507.15 216.18)"
            xlink:href="data:image/png;base64,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"
            style="opacity: 0.30000000000000004;mix-blend-mode: multiply" />
            <rect x="509.11" y="272.23" width="211.12" height="48.9" rx="24.45"
            transform="translate(-60.78 394.7) rotate(-34.02)" style="fill: url(#linear-gradient-8)" />
        </g>
        <rect x="569.76" y="87.66" width="257.87" height="31.7" rx="15.85"
        transform="translate(61.69 408.65) rotate(-34.02)" style="opacity: 0.73;fill: url(#linear-gradient-9)" />
        <circle cx="433.57" cy="139.47" r="13.7" style="opacity: 0.73;fill: url(#linear-gradient-10)" />
        <circle cx="170.22" cy="474.35" r="30.34" style="opacity: 0.39;fill: url(#linear-gradient-11)" />
    </g>
</svg>
