<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 570" preserveAspectRatio="none">
    <style type="text/css">
        @keyframes wave {
            0%, 100% {transform: scale(1, 1);}
            50%      {transform: scale(1, 1.2);}
        }
        .st0{fill:none;stroke:#3AADAA;stroke-miterlimit:10;transform-origin: right center;}
        .path_1 {animation: wave 25s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_2 {animation: wave 25s .5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_3 {animation: wave 25s 1s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_4 {animation: wave 25s 1.5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_5 {animation: wave 25s 2s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_6 {animation: wave 25s 2.5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_7 {animation: wave 25s 3s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_8 {animation: wave 25s 3.5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_9 {animation: wave 25s 4s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_10 {animation: wave 25s 4.5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_11 {animation: wave 25s 5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_12 {animation: wave 25s 5.5s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_13 {animation: wave 25s 6s cubic-bezier(.445, .05, .55, .95) infinite;}
        .path_14 {animation: wave 25s 6.5s cubic-bezier(.445, .05, .55, .95) infinite;}
        @media only screen and (max-width: 300px) {
            .st0{stroke-width:4}
        }
    </style>
    <g>
        <path class="path_1" fill="none" stroke="#3AADAA" d="M1260.5,0c-19.6,19.3-44.3,34.5-41.1,53.1c6.2,35.4,146.6,72.9,146.6,113.1c0,72.3-126.5,99.3-136.6,156.3 c-10.3,58,184.4,112.8,127.7,136.2c-27.1,11.2-188.7,10.7-99.3,112"/>
        <path class="path_2" fill="none" stroke="#3AADAA" d="M1273.5,0c-17.9,17.5-41.6,32.4-40,50.8c3,34.1,126.5,71.1,124.3,110.7c-3.9,69.5-120.8,98.2-128.4,152.4 c-6,42.6,89.7,79.2,124.2,109.4c14.5,12.7,18.8,22.2,1.3,29.6c-14.8,6.2-55.1,11.6-81.4,23.7c-32.3,14.9-48,43.4-2.8,94"/>
        <path class="path_3" fill="none" stroke="#3AADAA" d="M1286.7,0c-16.3,15.8-39,30.2-39.1,48.5c-0.2,32.8,106.5,69.3,102,108.3c-7.7,66.6-114.8,97.3-120.1,148.6 c-4.7,45,94.8,79.2,126.2,112c12.4,13,14.8,22.1-2.9,29.7c-17.2,7.4-57.2,15.2-78.6,28.5c-26.3,16.4-32.4,48.8,9.4,95"/>
        <path class="path_4" fill="none" stroke="#3AADAA" d="M1299.8,0c-14.6,14.1-36.2,28-38,46.2c-3.2,31.4,86.6,67.5,79.8,106c-11.3,63.6-108.5,96.4-111.9,144.8 c-3.3,47.5,99.8,78.8,128.3,114.7c10.4,13.1,10.8,22-7.1,29.9c-19.7,8.5-61.9,15.5-75.8,33.3c-22.9,13.6-16.7,54.3,21.6,95.9"/>
        <path class="path_5" fill="none" stroke="#3AADAA" d="M1313,0c-12.9,12.3-33.4,25.8-37.1,43.8c-6.2,30,66.7,65.7,57.5,103.6c-14.8,60.7-102,95.5-103.7,140.9 c-1.8,50,105,78.1,130.3,117.3c8.5,13.1,6.8,22-11.3,30c-22,9.8-64.9,18.2-72.9,38.2c-17.9,13.7-1,59.7,33.8,96.9"/>
        <path class="path_6" fill="none" stroke="#3AADAA" d="M1326,0c-11.3,10.6-30.4,23.6-36,41.5c-8.9,28.6,46.9,63.8,35.2,101.2c-18.1,57.8-95.3,94.7-95.4,137.1 c-0.1,52.5,110.4,77.1,132.3,119.9c6.7,13,2.8,21.9-15.5,30.2c-24.4,11-68,20.8-70,43c-12.9,13.9,14.6,65.1,46,97.8"/>
        <path class="path_7" fill="none" stroke="#3AADAA" d="M1339.1,0c-9.6,8.8-27.4,21.4-35,39.2c-11.6,27.2,27.2,62,12.9,98.8c-21.3,54.9-88.5,93.9-87.2,133.2 c1.8,55.1,116.2,75.8,134.3,122.6c5,12.9-1.1,21.9-19.6,30.4c-26.9,12.3-71.1,23.4-67.2,47.8c-7.9,14,30.3,70.6,58.3,98.8"/>
        <path class="path_8" fill="none" stroke="#3AADAA" d="M1352,0c-8,7.1-24.2,19.3-33.8,36.9c-14.2,25.9,7.6,60.2-9.4,96.4c-24.4,52.1-81.4,93-79,129.4 c3.9,57.6,122.6,74.5,136.3,125.2c3.4,12.6-5.1,21.9-23.8,30.5c-29.3,13.5-74.2,26-64.3,52.6c-2.9,14.1,46,76,70.5,99.7"/>
        <path class="path_9" fill="none" stroke="#3AADAA" d="M1365.1,0c-6.3,5.3-21.1,17.2-32.8,34.6c-16.7,24.7-12,58.5-31.7,94c-27.3,49.3-74.3,92.1-70.7,125.6 c6.4,60,129.6,73.1,138.3,127.8c2,12.5-9.1,21.8-28,30.7c-31.7,14.8-77.3,28.5-61.4,57.4c2.1,14.2,61.6,81.5,82.7,100.6"/>
        <path class="path_10" fill="none" stroke="#3AADAA" d="M1378.2,0c-4.7,3.5-17.9,15.2-31.7,32.3c-19.1,23.6-31.4,56.9-53.9,91.6c-30.2,46.7-67,91.2-62.5,121.7 c9.2,62.4,137.2,71.9,140.3,130.5c0.7,12.4-13,21.8-32.2,30.8c-34,16.1-77.9,29.5-58.6,62.2c8.2,13.8,77.3,86.9,94.9,101.6"/>
        <path class="path_11" fill="none" stroke="#3AADAA" d="M1391.3,0c-12.3,7.4-173.8,172.3-161.2,236.8s145.5,70.9,142.3,133c-1.8,35.5-134.5,47-92.1,97.8 c12.1,14.5,92.9,92.3,107.2,102.4"/>
    </g>
</svg>
