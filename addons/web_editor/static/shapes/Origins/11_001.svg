<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1400 2800">
 	<style>
		@media only screen and (max-width: 300px) {
			svg {transform: scaleY(5); transform-origin: center top;}
		}
    </style>
	<linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="0" y1="1400" x2="1400" y2="1400">
		<stop offset="0" stop-color="#FFFFFF" stop-opacity=".25"/>
		<stop offset="1" stop-color="#FFFFFF" stop-opacity="0"/>
	</linearGradient>
	<path fill="url(#gradient)" d="M1400,0C913.6,36.9,445.1,40.3,0,0v2800h1400V0z"/>
	<path fill="#F6F6F6" d="M1400,0H0C445.1,40.3,913.6,36.9,1400,0z"/>
</svg>
