# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks_page_ir_ui_view
#: model_terms:website.page,arch_db:website_crm.contactus_thanks_page
msgid "&amp;times;"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid ""
"<span class=\"btn btn-primary btn-lg o_website_form_send\">Send</span>\n"
"                                <span id=\"o_website_form_result\"/>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Add a contact form in the"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Communication"
msgstr "Komunikacija"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Contact Form"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Contact Us"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_res_config_settings_crm_default_team_id
msgid "Default Sales Channel"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_website_crm_default_team_id
msgid "Default Sales Channels"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_res_config_settings_crm_default_user_id
#: model:ir.model.fields,field_description:website_crm.field_website_crm_default_user_id
msgid "Default Salesperson"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,help:website_crm.field_res_config_settings_crm_default_team_id
#: model:ir.model.fields,help:website_crm.field_website_crm_default_team_id
msgid ""
"Default sales channel for new leads created through the Contact Us form."
msgstr ""

#. module: website_crm
#: model:ir.model.fields,help:website_crm.field_res_config_settings_crm_default_user_id
#: model:ir.model.fields,help:website_crm.field_website_crm_default_user_id
msgid "Default salesperson for new leads created through the Contact Us form."
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "Email"
msgstr "E-mail"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks_page_ir_ui_view
#: model_terms:website.page,arch_db:website_crm.contactus_thanks_page
msgid "If you have an emergency, do not hesitate to contact us by phone:"
msgstr ""

#. module: website_crm
#: model:ir.model,name:website_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "New messages are managed as leads or opportunities in your CRM app."
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "Phone Number"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Sales Channel"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Salesperson"
msgstr "Prodavač"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "Subject"
msgstr "Subjekat"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks_page_ir_ui_view
#: model_terms:website.page,arch_db:website_crm.contactus_thanks_page
msgid "Thanks!"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks_page_ir_ui_view
#: model_terms:website.page,arch_db:website_crm.contactus_thanks_page
msgid "We will get back to you shortly."
msgstr ""

#. module: website_crm
#: model:ir.model,name:website_crm.model_website
msgid "Website"
msgstr "Internet stranica"

#. module: website_crm
#: model:ir.actions.act_url,name:website_crm.action_open_website
msgid "Website Contact Form"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "Your Company"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "Your Name"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "Your Question"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_thanks_page_ir_ui_view
#: model_terms:website.page,arch_db:website_crm.contactus_thanks_page
msgid "Your message has been sent successfully."
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "page"
msgstr ""

#. module: website_crm
#: model:ir.model,name:website_crm.model_res_config_settings
msgid "res.config.settings"
msgstr ""
