# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# <PERSON><PERSON><PERSON> <piotr.w.c<PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <judyta.ka<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"<span attrs=\"{'invisible': [('show_availability', '=', "
"False)]}\">Units</span>"
msgstr ""
"<span attrs=\"{'invisible': [('show_availability', '=', "
"False)]}\">Jednostki</span>"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__stock_notification_partner_ids
msgid "Back in stock Notifications"
msgstr "Powiadomienia z powrotem w magazynie"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr "Kontynuuj sprzedaż"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr "Kontynuuj sprzedaż gdy magazyn jest pusty"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Dear Customer,"
msgstr "Drogi kliencie,"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""
"Domyślny tryb dostępności na nowo utworzonych produktach, które mogą być "
"składowane. Może zostać zmieniony na poziomie produktu."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr "Domyślna widoczność dla własnych wiadomości"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Get notified when back in stock"
msgstr "Powiadom gdy magazyn zapełni się z powrotem"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "How to display products having low quantities (on hand - reserved)"
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/controllers/main.py:0
#, python-format
msgid "Invalid Email"
msgstr "Błędny email"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Invalid email"
msgstr "Błędny email"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory Defaults"
msgstr "Domyślne ustawienia magazynowania"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Only"
msgstr "Jedynie"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Order Now"
msgstr "Zamów teraz"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Out of Stock"
msgstr "Brak w magazynie"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr "Brak w magazynie"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr "Wiadomość o braku w magazynie"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#: model:ir.model,name:website_sale_stock.model_product_template
#, python-format
msgid "Product"
msgstr "Produkt"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product Variant"
msgstr "Wariant produktu"

#. module: website_sale_stock
#: model:ir.actions.server,name:website_sale_stock.ir_cron_send_availability_email_ir_actions_server
#: model:ir.cron,cron_name:website_sale_stock.ir_cron_send_availability_email
msgid "Product: send email regarding products availability"
msgstr "Produkt: wyślij email o dostępności produktów"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Regards,"
msgstr "Pozdrawiam,"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Zamówienie sprzedaży"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pozycja zamówienia sprzedaży"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr "Pokaż dostępną ilość"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr "Pokaż próg"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr "Pokaż dostępną ilość"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""
"Niektóre produkty stały się niedostępne, a Twój koszyk został "
"zaktualizowany. Przepraszamy za niedogodności."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "The following product is now available."
msgstr "Następujący produkt jest teraz dostępny"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#, python-format
msgid "The product '%(product_name)s' is now available"
msgstr "Produkt '%(product_name)s' jest teraz dostępny"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "This product is out of stock."
msgstr "Ten produkt został wyprzedany"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Przekaz"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Units"
msgstr "Sztuki"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "Magazyn"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "We'll notify you once the product is back in stock."
msgstr "Powiadomimy Cię gdy produkt ponownie będzie dostępny"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "Strona internetowa"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website where this order has been placed, for eCommerce orders."
msgstr ""
"Witryna na której to zamówienie zostało złożone, dla zamówień eCommerce"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added"
msgstr "już dodałeś"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added all the available product in your cart."
msgstr "Już dodałeś wszystkie dostępne produkty do koszyka"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "You already have %s Units in your cart."
msgstr "Masz już %s sztuk w koszyku"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order_line.py:0
#, python-format
msgid ""
"You ask for %(desired_qty)s %(product_name)s but only %(new_qty)s is "
"available"
msgstr ""

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid "You ask for %(desired_qty)s products but only %(new_qty)s is available"
msgstr ""
"Prosisz o %(desired_qty)s produktów lecz tylko %(new_qty)s jest dostępne."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "You ask for %s Units but only %s are available."
msgstr "Prosisz o %s sztuk lecz jedynie %s jest dostępne."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "in your cart."
msgstr "w Twoim koszyku."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "left in stock."
msgstr "pozostało w magazynie."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr "tylko jeżeli poniżej"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"
