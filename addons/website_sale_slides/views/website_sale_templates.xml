<?xml version="1.0" ?>
<odoo><data>

<template id="website_sale_confirmation_slide" inherit_id="website_sale.confirmation">
    <xpath expr="//div[@id='oe_structure_website_sale_confirmation_2']" position="after">
        <t t-if="any(product.detailed_type == 'course' for product in order.order_line.product_id)" t-call="website_sale_slides.course_purchased_confirmation_message"/>
    </xpath>
</template>

<template id="course_purchased_confirmation_message">
    <div>
        <h4>
            <t t-if="order.state == 'sale'">You have gained access to the following course(s):</t>
            <t t-else="">Once your order is paid &amp; confirmed, you will gain access to:</t>
        </h4>
    </div>
    <div class="mt-2">
        <t t-foreach="order.order_line" t-as="line">
            <t t-set="signup_token" t-value="order.partner_id.signup_prepare() and order.partner_id.signup_token"/>
            <div t-foreach="line.product_id.channel_ids" t-as="course" class="row mx-0 my-2 border">
                <t t-set="slide_url" t-valuef="/web/login?token=#{signup_token}&amp;redirect=/slides/#{slug(course)}"/>
                <div class="col-5 d-flex justify-content-center my-auto">
                    <span t-if="course.image_1920" t-field="course.image_1920" t-options="{'widget': 'image', 'class': 'my-2'}"/>
                    <img t-else="" class="img img-fluid my-2" src="/website_slides/static/src/img/channel-training-default.jpg"/>
                </div>
                <div class="col-7">
                    <a t-att-href="slide_url"><h3 t-out="course.name" class="m-2"/></a>
                    <div t-out="course.description_short" class="fw-light o_wslides_desc_truncate_2 ms-2"/>
                    <div class="fw-light ms-2 mt-2">
                        <t t-out="course.total_time" t-options="{'widget': 'duration', 'unit': 'hour', 'round': 'minute'}"/>
                        <t t-if="course.total_slides">
                           <t t-if="course.total_time"> - </t><t t-out="course.total_slides"/> step(s)
                        </t>
                    </div>

                    <a role="button" class="btn btn-primary ms-2 my-2" t-att-href="slide_url">
                        Start Learning
                    </a>
                </div>
            </div>
        </t>
    </div>
</template>

<template id="cart_summary_inherit_website_sale_slides"
          inherit_id="website_sale.cart_summary"
          name="Course Cart right column">
    <xpath expr="//td[hasclass('td-product_name')]/div/strong" position="after">
        <div t-if="line.product_id.channel_ids"
             t-foreach="line.product_id.channel_ids.filtered(lambda course: course.enroll == 'payment')"
             t-as="course"
             t-esc="course.name"/>
    </xpath>
</template>

</data></odoo>
