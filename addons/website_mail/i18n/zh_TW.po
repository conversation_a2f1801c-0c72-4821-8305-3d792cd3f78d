# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mail
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# Wil <PERSON>doo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Follow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr "<small>關注</small><i class=\"fa fa-fw ms-1\"/>"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Unfollow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr "<small>取消關注</small><i class=\"fa fa-fw ms-1\"/>"

#. module: website_mail
#. odoo-javascript
#: code:addons/website_mail/static/src/js/follow.js:0
#, python-format
msgid "Error"
msgstr "錯誤"

#. module: website_mail
#: model:ir.model,name:website_mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "隱私權聲明與會員使用條款"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Subscribe"
msgstr "訂閱"

#. module: website_mail
#. odoo-python
#: code:addons/website_mail/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha 檢測到的可疑活動."

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Unsubscribe"
msgstr "取消訂閱"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "your email..."
msgstr "你的電郵地址⋯"
