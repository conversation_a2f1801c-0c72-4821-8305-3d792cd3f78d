# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mail
# 
# Translators:
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Follow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr "<small>Urmărește</small><i class=\"fa fa-fw ms-1\"/>"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "<small>Unfollow</small><i class=\"fa fa-fw ms-1\"/>"
msgstr "<small>Dezabonare</small><i class=\"fa fa-fw ms-1\"/>"

#. module: website_mail
#. odoo-javascript
#: code:addons/website_mail/static/src/js/follow.js:0
#, python-format
msgid "Error"
msgstr "Eroare"

#. module: website_mail
#: model:ir.model,name:website_mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Contract de garanție pentru editor"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Subscribe"
msgstr "Abonare"

#. module: website_mail
#. odoo-python
#: code:addons/website_mail/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Activitate suspectă detectată de Google reCaptcha."

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Unsubscribe"
msgstr "Dezabonare"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "your email..."
msgstr "emailul dvs...."
