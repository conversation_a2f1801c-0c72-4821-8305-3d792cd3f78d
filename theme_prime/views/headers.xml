<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="template_header_style_1" inherit_id="website.layout" name="Header Style - 1" active="False">
        <xpath expr="//header/*" position="before">
            <div class="tp-preheader o_colored_level o_cc" data-name="Preheader">
                <div class="container">
                    <div class="row align-items-center justify-content-lg-between justify-content-center">
                        <div class="col-auto py-3">
                            <div class="s_social_media no_icon_color" data-name="Social Media">
                                <a href="/website/social/facebook" class="s_social_media_facebook" target="_blank">
                                    <i class="fa fa-facebook m-1"/>
                                </a>
                                <a href="/website/social/twitter" class="s_social_media_twitter" target="_blank">
                                    <i class="fa fa-twitter m-1"/>
                                </a>
                                <a href="/website/social/linkedin" class="s_social_media_linkedin" target="_blank">
                                    <i class="fa fa-linkedin m-1"/>
                                </a>
                                <a href="/website/social/github" class="s_social_media_github" target="_blank">
                                    <i class="fa fa-github m-1"/>
                                </a>
                                <a href="/website/social/youtube" class="s_social_media_youtube" target="_blank">
                                    <i class="fa fa-youtube m-1"/>
                                </a>
                                <a href="/website/social/instagram" class="s_social_media_instagram" target="_blank">
                                    <i class="fa fa-instagram m-1"/>
                                </a>
                            </div>
                        </div>
                        <div class="col-auto py-3 d-none d-lg-block">
                            <ul class="list-inline mb-0">
                                <li class="list-inline-item"><a href="tel:****************"><i class="fa fa-1x fa-fw fa-phone"/> ****************</a></li>
                                <li class="list-inline-item"><a href="mailto:<EMAIL>"><i class="fa fa-1x fa-fw fa-envelope"/> <EMAIL></a></li>
                            </ul>
                        </div>
                        <div class="col-auto py-3 d-none d-lg-block">
                            <t t-call="theme_prime.component_language_pricelist_selector"/>
                        </div>
                        <div class="col-auto py-3 d-none d-lg-block">
                            <ul class="list-inline mb-0 d-flex align-items-center tp-account-info">
                                <t t-call="theme_prime.component_account_info"/>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </xpath>
        <xpath expr="//header//nav" position="replace">
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container justify-content-between">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <t t-call="website.placeholder_header_brand"/>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse order-last order-lg-0">
                        <t t-call="website.navbar_nav">
                            <t t-set="_nav_class" t-value="'flex-grow-1 justify-content-end'"/>
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_2" inherit_id="website.layout" name="Header Style - 2" active="False">
        <xpath expr="//header//nav" position="replace">
            <div class="tp-header-box o_colored_level o_cc">
                <div class="container">
                    <div class="row justify-content-center justify-content-lg-between align-items-center">
                        <div class="col-4 py-3">
                            <t t-call="website.website_search_box_input" t-nocache="">
                                <t t-set="_form_classes" t-valuef="w-100 o_wsale_products_searchbar_form o_not_editable"/>
                                <t t-set="_classes" t-valuef=" "/>
                                <t t-set="search_type" t-valuef="products"/>
                                <t t-set="action" t-value="'/shop'"/>
                            </t>
                        </div>
                        <div class="col-4 text-center py-3">
                            <!-- Brand -->
                            <t t-call="website.placeholder_header_brand">
                                <t t-set="_link_class" t-value="'d-none d-lg-inline-block mx-auto'"/>
                            </t>
                        </div>
                        <div class="col-4 py-3 text-end">
                            <ul class="list-inline m-0 tp-account-info">
                                <t t-call="theme_prime.component_account_info">
                                    <t t-set="_hide_search" t-value="True"/>
                                </t>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container justify-content-between">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <a href="/" t-attf-class="navbar-brand logo d-lg-none o_offcanvas_logo_container">
                        <span t-field="website.logo" t-options="{'widget': 'image', 'width': 95, 'height': 40}" role="img" t-att-aria-label="'Logo of %s' % website.name" t-att-title="website.name"/>
                    </a>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse order-last order-lg-0">
                        <t t-call="website.navbar_nav">
                            <t t-set="_nav_class" t-value="'flex-grow-1'"/>
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_3" inherit_id="website.layout" name="Header Style - 3" active="False">
        <xpath expr="//header//nav" position="replace">
            <div class="tp-header-box o_colored_level o_cc">
                <div class="container">
                    <div class="row justify-content-center justify-content-lg-between align-items-center">
                        <div class="col-3 py-3">
                            <!-- Brand -->
                            <t t-call="website.placeholder_header_brand">
                                <t t-set="_link_class" t-value="'d-none d-lg-inline-block'"/>
                            </t>
                        </div>
                        <div class="col-6 py-3">
                            <t t-call="website.website_search_box_input" t-nocache="">
                                <t t-set="_form_classes" t-valuef="mx-auto w-100 o_wsale_products_searchbar_form o_not_editable"/>
                                <t t-set="_classes" t-valuef=" "/>
                                <t t-set="search_type" t-valuef="products"/>
                                <t t-set="action" t-value="'/shop'"/>
                            </t>
                        </div>
                        <div class="col-3 py-3 text-end">
                            <ul class="list-inline m-0 tp-account-info">
                                <t t-call="theme_prime.component_account_info">
                                    <t t-set="_hide_search" t-value="True"/>
                                </t>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container justify-content-between">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <a href="/" t-attf-class="navbar-brand logo d-lg-none o_offcanvas_logo_container">
                        <span t-field="website.logo" t-options="{'widget': 'image', 'width': 95, 'height': 40}" role="img" t-att-aria-label="'Logo of %s' % website.name" t-att-title="website.name"/>
                    </a>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse order-last order-lg-0">
                        <t t-call="website.navbar_nav">
                            <t t-set="_nav_class" t-value="'flex-grow-1'"/>
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_4" inherit_id="website.layout" name="Header Style - 4" active="False">
        <xpath expr="//header//nav" position="replace">
            <div class="tp-header-box o_colored_level o_cc">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-auto py-3">
                            <!-- Brand -->
                            <t t-call="website.placeholder_header_brand">
                                <t t-set="_link_class" t-value="'d-none d-lg-block'"/>
                            </t>
                        </div>
                        <div class="col-auto py-3 flex-grow-1">
                            <t t-call="website.website_search_box_input" t-nocache="">
                                <t t-set="_form_classes" t-valuef="mx-auto w-75 o_wsale_products_searchbar_form o_not_editable"/>
                                <t t-set="_classes" t-valuef=" "/>
                                <t t-set="search_type" t-valuef="products"/>
                                <t t-set="action" t-value="'/shop'"/>
                            </t>
                        </div>
                        <div class="col-auto py-3 o_not_editable">
                            <div class="row align-items-center justify-content-end tp-account-info">
                                <div class="col-auto my-1">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <t t-nocache="The number of products is dynamic, this rendering cannot be cached.">
                                                <t t-set="website_sale_cart_quantity" t-value="request.session['website_sale_cart_quantity'] if 'website_sale_cart_quantity' in request.session else website.sale_get_order().cart_quantity or 0"/>
                                                <t t-set="show_cart" t-value="true"/>
                                                <div t-attf-class="o_not_editable position-relative o_wsale_my_cart tp-cart-sidebar-action #{not show_cart and 'd-none'}">
                                                    <a href="/shop/cart">
                                                        <i class="dri dri-cart tp-light-bg rounded-circle"/>
                                                        <sup class="my_cart_quantity badge text-bg-primary" t-out="website_sale_cart_quantity" t-att-data-order-id="request.session.get('sale_order_id', '')"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                        <div class="flex-grow-1 ms-3 d-none d-xl-block">
                                            <div class="mb-1">My Cart</div>
                                            <a class="tp-cart-sidebar-action" href="#"><h6 class="mb-0">View Cart</h6></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto my-1">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <t t-nocache="The wishlist may vary and depends on the user.">
                                                <t t-set="wishcount" t-value="len(request.env['product.wishlist'].current())"/>
                                                <t t-set="show_wishes" t-value="True"/>
                                                <div t-attf-class="o_not_editable position-relative o_wsale_my_wish #{not show_wishes and 'd-none'}">
                                                    <a href="/shop/wishlist">
                                                        <i class="dri dri-wishlist tp-light-bg rounded-circle"/>
                                                        <sup t-out="wishcount" t-attf-class="my_wish_quantity o_animate_blink badge text-bg-primary"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                        <div class="flex-grow-1 ms-3 d-none d-xl-block">
                                            <div class="mb-1">My Wishlist</div>
                                            <a href="/shop/wishlist"><h6 class="mb-0">View Wishlist</h6></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto my-1" t-if="request.website.viewref('portal.user_sign_in').active">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <a href="#" class="tp-account-info-sidebar-action">
                                                <i class="dri dri-user tp-light-bg rounded-circle"/>
                                            </a>
                                        </div>
                                        <div class="flex-grow-1 ms-3 d-none d-xl-block">
                                            <div t-nocache="User may vary." style="max-width: 150px;" class="text-truncate mb-1"><t t-out="not user_id._is_public() and user_id.name or 'Guest'"/></div>
                                            <a class="tp-account-info-sidebar-action" href="#"><h6 class="mb-0">My Account</h6></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container justify-content-between">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <a href="/" t-attf-class="navbar-brand logo d-lg-none o_offcanvas_logo_container">
                        <span t-field="website.logo" t-options="{'widget': 'image', 'width': 95, 'height': 40}" role="img" t-att-aria-label="'Logo of %s' % website.name" t-att-title="website.name"/>
                    </a>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse order-last order-lg-0">
                        <t t-call="website.navbar_nav">
                            <t t-set="_nav_class" t-value="'flex-grow-1'"/>
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_5" inherit_id="website.layout" name="Header Style - 5" active="False">
        <xpath expr="//header//nav" position="replace">
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container justify-content-between">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <t t-call="website.placeholder_header_brand"/>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse">
                        <t t-call="website.navbar_nav">
                            <t t-set="_nav_class" t-valuef="flex-grow-1 justify-content-center"/>
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                    </div>
                    <!-- Account Info -->
                    <ul class="list-inline my-3 d-none d-lg-block tp-account-info">
                        <t t-call="theme_prime.component_account_info"/>
                    </ul>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_6" inherit_id="website.layout" name="Header Style - 6" active="False">
        <xpath expr="//header//nav" position="replace">
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm o_top_menu_collapse_shown"/>
                <div id="top_menu_container" class="container">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <t t-call="website.placeholder_header_brand">
                        <t t-set="_link_class" t-value="'mx-2'"/>
                    </t>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse">
                        <t t-call="website.navbar_nav">
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                        <!-- Account Info -->
                        <ul class="list-inline mb-0 ms-auto d-none d-lg-block tp-account-info">
                            <t t-call="theme_prime.component_account_info">
                                <t t-set="_item_class" t-valuef="tp-light-bg"/>
                            </t>
                        </ul>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_7" inherit_id="website.layout" name="Header Style - 7" active="False">
        <xpath expr="//header//nav" position="replace">
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container-fluid px-0">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <t t-call="website.placeholder_header_brand"/>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse">
                        <t t-call="website.navbar_nav">
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                        <!-- Account Info -->
                        <ul class="list-inline mb-0 ms-auto d-none d-lg-block tp-account-info">
                            <t t-call="theme_prime.component_account_info">
                                <t t-set="_show_title" t-value="True"/>
                            </t>
                        </ul>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="template_header_style_8" inherit_id="website.layout" name="Header Style - 8" active="False">
        <xpath expr="//header//nav" position="replace">
            <div class="tp-header-box o_colored_level o_cc">
                <div class="container">
                    <div class="row justify-content-center justify-content-lg-between align-items-center">
                        <div class="col-4 py-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <span class="fa fa-phone text-primary fa-2x"/>
                                </div>
                                <div class="flex-grow-1 ps-3">
                                    <h6 class="mb-1">Call us now</h6>
                                    <h5 class="mb-0"><a href="tel:+****************">+****************</a></h5>
                                    <h5 class="mb-0"><a href="tel:+****************">+****************</a></h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-4 text-center py-3">
                            <!-- Brand -->
                            <t t-call="website.placeholder_header_brand">
                                <t t-set="_link_class" t-value="'d-none d-lg-inline-block mx-auto'"/>
                            </t>
                        </div>
                        <div class="col-4 py-3 text-end">
                            <ul class="list-inline m-0 tp-account-info">
                                <t t-call="theme_prime.component_account_info"/>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <t t-call="website.navbar">
                <t t-set="_navbar_classes" t-valuef="shadow-sm"/>
                <div id="top_menu_container" class="container justify-content-between">
                    <!-- Navbar Toggler -->
                    <t t-call="theme_prime.component_navbar_toggler"/>
                    <!-- Brand -->
                    <a href="/" t-attf-class="navbar-brand logo d-lg-none o_offcanvas_logo_container">
                        <span t-field="website.logo" t-options="{'widget': 'image', 'width': 95, 'height': 40}" role="img" t-att-aria-label="'Logo of %s' % website.name" t-att-title="website.name"/>
                    </a>
                    <!-- Account Info -->
                    <t t-call="theme_prime.component_navbar_mobile_account_info"/>
                    <!-- Navbar Collapse -->
                    <div id="top_menu_collapse" class="collapse navbar-collapse order-last order-lg-0">
                        <t t-call="website.navbar_nav">
                            <t t-set="_nav_class" t-value="'flex-grow-1'"/>
                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-value="'nav-item'"/>
                                    <t t-set="link_class" t-value="'nav-link'"/>
                                </t>
                            </t>
                        </t>
                    </div>
                    <div class="oe_structure oe_structure_solo d-none d-lg-block">
                        <section class="s_text_block" data-snippet="s_text_block" data-name="Text">
                            <div class="container">
                                <a href="/contactus" class="btn btn-primary btn_cta">Contact Us</a>
                            </div>
                        </section>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

</odoo>
