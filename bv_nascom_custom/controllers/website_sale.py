# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale


class WebsiteSaleInherit(WebsiteSale):
    
    def _checkout_form_save(self, mode, checkout, all_values):
        """
        Override to implement custom billing address logic for portal users.
        
        When a portal user has a parent_id (child partner), use parent's billing address.
        When a portal user doesn't have parent_id, use their own billing address.
        """
        # Get the current user and order
        current_user = request.env.user
        order = request.website.sale_get_order()
        
        # Check if this is billing address mode and user is logged in (not public user)
        if mode[1] == 'billing' and not current_user._is_public():
            # Get the current partner (logged in user)
            current_partner = current_user.partner_id
            
            # If the current partner has a parent_id, use parent's billing address
            if current_partner.parent_id:
                # Use parent's billing address
                parent_partner = current_partner.parent_id
                
                # Update the order with parent as billing partner
                if order:
                    order.partner_id = parent_partner
                    order.partner_invoice_id = parent_partner
                
                return parent_partner.id
            else:
                # Use current user's billing address (default behavior)
                if order:
                    order.partner_id = current_partner
                    order.partner_invoice_id = current_partner
                
                return current_partner.id
        
        # For all other cases (shipping, public users, etc.), use default behavior
        return super()._checkout_form_save(mode, checkout, all_values)

    @http.route(['/shop/address'], type='http', auth="public", website=True, sitemap=False)
    def address(self, **kw):
        """
        Override address route to automatically set billing address for portal users
        based on parent_id relationship.
        """
        # Get the current user and order
        current_user = request.env.user
        order = request.website.sale_get_order()

        # Check if user is logged in (not public user) and we have an order
        if not current_user._is_public() and order:
            current_partner = current_user.partner_id

            # If this is a billing address request and partner has parent_id
            if kw.get('partner_id') and current_partner.parent_id:
                # Automatically set parent as billing address
                parent_partner = current_partner.parent_id
                order.partner_id = parent_partner
                order.partner_invoice_id = parent_partner

                # Redirect to checkout if billing address is set
                if not kw.get('callback'):
                    return request.redirect('/shop/checkout')
            elif not current_partner.parent_id:
                # Use current user as billing address
                order.partner_id = current_partner
                order.partner_invoice_id = current_partner

                # Redirect to checkout if billing address is set
                if not kw.get('callback'):
                    return request.redirect('/shop/checkout')

        # For all other cases, use default behavior
        return super().address(**kw)

    @http.route(['/shop/checkout'], type='http', auth="public", website=True, sitemap=False)
    def checkout(self, **post):
        """
        Override checkout to ensure billing address is properly set for portal users
        based on parent_id relationship.
        """
        # Get the current user and order
        current_user = request.env.user
        order = request.website.sale_get_order()

        # Check if user is logged in (not public user) and we have an order
        if not current_user._is_public() and order:
            current_partner = current_user.partner_id

            # Apply billing address logic based on parent_id
            if current_partner.parent_id:
                # Use parent's billing address
                parent_partner = current_partner.parent_id
                if order.partner_id != parent_partner or order.partner_invoice_id != parent_partner:
                    order.partner_id = parent_partner
                    order.partner_invoice_id = parent_partner
            else:
                # Use current user's billing address
                if order.partner_id != current_partner or order.partner_invoice_id != current_partner:
                    order.partner_id = current_partner
                    order.partner_invoice_id = current_partner

        # Call parent method
        result = super().checkout(**post)

        # Add additional context for template rendering
        if hasattr(result, 'qcontext') and result.qcontext:
            current_user = request.env.user
            if not current_user._is_public() and order:
                current_partner = current_user.partner_id
                # Add billing email information to context
                if current_partner.parent_id:
                    # User has parent_id, use parent's inv_email_confirmation_partner_id
                    result.qcontext['billing_email_partner'] = current_partner.parent_id.inv_email_confirmation_partner_id
                    result.qcontext['has_parent_id'] = True
                else:
                    # User has no parent_id, use own inv_email_confirmation_partner_id
                    result.qcontext['billing_email_partner'] = current_partner.inv_email_confirmation_partner_id
                    result.qcontext['has_parent_id'] = False

        return result
