<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit the address_kanban template to show inv_email_confirmation_partner_id -->
    <template id="address_kanban_inherit_billing_email" inherit_id="website_sale.address_kanban">
        <xpath expr="//t[@t-esc='contact']" position="after">
            <!-- Show inv_email_confirmation_partner_id for billing address -->
            <t t-if="contact == order.partner_invoice_id">
                <div class="mt-2">
                    <t t-set="email_partner" t-value="contact.parent_id.inv_email_confirmation_partner_id if contact.parent_id else contact.inv_email_confirmation_partner_id"/>
                    <t t-if="email_partner">
                        <small class="text-muted">
                            <strong>Invoice Email:</strong> <span t-field="email_partner.email"/>
                        </small>
                    </t>
                </div>
            </t>
        </xpath>
    </template>

    <!-- Inherit the checkout template directly around line 1685 to show billing email -->
    <template id="checkout_billing_address_inherit" inherit_id="website_sale.checkout">
        <xpath expr="//h3[contains(@class, 'o_page_header') and contains(text(), 'Billing Address')]" position="after">
            <!-- Add invoice email information after billing address header -->
            <t t-if="order.partner_invoice_id and not request.env.user._is_public()">
                <div class="alert alert-info mt-2 mb-3">
                    <t t-set="current_user_partner" t-value="request.env.user.partner_id"/>
                    <t t-if="current_user_partner.parent_id">
                        <!-- User has parent_id, show parent's inv_email_confirmation_partner_id -->
                        <t t-set="email_partner" t-value="current_user_partner.parent_id.inv_email_confirmation_partner_id"/>
                        <t t-if="email_partner">
                            <small>
                                <i class="fa fa-envelope me-1"></i>
                                <strong>Invoice will be sent to:</strong>
                                <span t-field="email_partner.name"/>
                                (<span t-field="email_partner.email"/>)
                            </small>
                        </t>
                        <t t-else="">
                            <small>
                                <i class="fa fa-envelope me-1"></i>
                                <strong>Invoice will be sent to:</strong>
                                <span t-field="current_user_partner.parent_id.name"/>
                                (<span t-field="current_user_partner.parent_id.email"/>)
                            </small>
                        </t>
                    </t>
                    <t t-else="">
                        <!-- User has no parent_id, show own inv_email_confirmation_partner_id -->
                        <t t-set="email_partner" t-value="current_user_partner.inv_email_confirmation_partner_id"/>
                        <t t-if="email_partner">
                            <small>
                                <i class="fa fa-envelope me-1"></i>
                                <strong>Invoice will be sent to:</strong>
                                <span t-field="email_partner.name"/>
                                (<span t-field="email_partner.email"/>)
                            </small>
                        </t>
                        <t t-else="">
                            <small>
                                <i class="fa fa-envelope me-1"></i>
                                <strong>Invoice will be sent to:</strong>
                                <span t-field="current_user_partner.name"/>
                                (<span t-field="current_user_partner.email"/>)
                            </small>
                        </t>
                    </t>
                </div>
            </t>
        </xpath>
    </template>
</odoo>
