# -*- coding: utf-8 -*-
import base64
from odoo import api, fields, models, _, Command    
from odoo.addons.account_avatax_sale.models.sale_order import SaleOrder as SO
from odoo.exceptions import ValidationError
from odoo.tools.misc import formatLang

LOCKED_FIELD_STATES = {
    state: [('readonly', True)]
    for state in {'done', 'cancel'}
}

def button_update_avatax(self):
    if self.fiscal_position_id and self.fiscal_position_id.state_ids and (self.partner_shipping_id.state_id.id not in self.fiscal_position_id.state_ids.ids):
        return
    mapped_taxes, _ = self.filtered(lambda m: m.fiscal_position_id.is_avatax)._map_avatax(False)
    to_flush = self.env['sale.order.line']
    for line, detail in mapped_taxes.items():
        line.tax_id = detail['tax_ids']
        to_flush += line


    # Trigger field computation due to changing the tax id. Do
    # this here because we will manually change the taxes.
    to_flush.flush_recordset(['price_tax', 'price_subtotal', 'price_total'])

    for line, detail in mapped_taxes.items():
        line.price_tax = detail['tax_amount']
        line.price_subtotal = detail['total']
        line.price_total = detail['tax_amount'] + detail['total']
SO._patch_method('button_update_avatax', button_update_avatax)

class SalesOrder(models.Model):
    _inherit = 'sale.order'

    promise_date = fields.Date(string='Promised Ship Date')
    client_order_ref = fields.Char(string="Customer Reference", copy=False)
    _sql_constraints = [
        ('client_order_ref', 'unique (client_order_ref)', "Customer Reference is already exists."),
    ]

    @api.depends('delivery_status')
    def _depends_delivery_status(self):
        """
        Set is closed order
        """
        for order in self:
            is_closed = False
            if order.delivery_status == 'full':
                is_closed = True
            order.write({'is_closed': is_closed})

    is_closed = fields.Boolean(string="Is Closed?", compute='_depends_delivery_status', copy=False, store=True)
    state = fields.Selection(
        selection_add=[
            ('draft', "Quotation"),
            ('sent', "Confirmation Email Sent"),
            ('ready_for_review', 'Ready To Review'),
            ('ready_for_confirm', 'Ready To Confirm'),
            ('sale', "Sales Order"),
            ('done', "Locked"),
            ('cancel', "Cancelled"),
        ],
        string="Status",
        readonly=True, copy=False, index=True,
        tracking=3,
        default='draft')
    shipping_pay_type = fields.Selection(
        selection=[
            ('SENDER', 'SENDER'),
            ('RECEIVER', 'RECEIVER'),
            ('THIRD PARTY', 'THIRD PARTY')],
            string='Pay Type',
            default='SENDER',
            required=True
            )
    ship_charge = fields.Selection(
        selection=[
            ('PPDNOADD', 'PPDNOADD'),
            ('PPD+ADD', 'PPD+ADD'),
            ('COLLECT', 'COLLECT')],
            string='Ship Charge',
            default='PPDNOADD',
            required=True
            )

    is_customer_part_no = fields.Boolean('Is customer PN?', compute='_compute_customer_pn')
    is_use_third_party_account = fields.Boolean(string="Use Third Party Account?")
    account = fields.Char(string='Account #')
    shipping_country_id = fields.Many2one('res.country', string='Country')
    shipping_zip = fields.Char(string='Zip')
    shipping_carrier_id = fields.Many2one('shipstation.delivery.carrier', string='Carrier')
    domestic_service_type_id = fields.Many2one('shipstation.delivery.carrier.service', string='Service Type')
    attention = fields.Text(string="Attention")
    so_email_to_partner_id = fields.Many2one('res.partner', string="Email To")
    invoice_email_to_partner_id = fields.Many2one('res.partner', string="Email To")
    delivery_email_to_partner_id = fields.Many2one('res.partner', string="Email To")
    carrier_tracking_ref = fields.Char(string="Tracking Reference", readonly=True)

    commitment_date = fields.Datetime(
        string="Delivery Date", copy=False,
        states=LOCKED_FIELD_STATES,
        default=lambda self: fields.Datetime.now(),
        help="This is the delivery date promised to the customer. "
             "If set, the delivery order will be scheduled based on "
             "this date rather than product lead times.")

    is_assign_promise_date = fields.Boolean(string='Assign Promised Date', copy=False, compute='_compute_is_assign_promise_date')
    sfdc_so_ref = fields.Char(string='SFDC SO REF')
    is_manual_order = fields.Boolean('Is Manual Order?', tracking=True)

    @api.onchange('shipping_carrier_id')
    def _onchange_shipping_carrier_id(self):
        if self._context.get('is_domestic_service_type_id'):
            self.domestic_service_type_id = False

    @api.depends('order_line')
    def _compute_is_assign_promise_date(self):
        for order in self:
            is_assign_promise_date = True
            if order.order_line.filtered(lambda ln: not ln.is_delivery and not ln.promise_date):
                is_assign_promise_date = False
            order.is_assign_promise_date = is_assign_promise_date

    @api.model
    def default_get(self, fields):
        res = super().default_get(fields)
        res["incoterm"] = self.env.ref('account.incoterm_EXW').id
        res["incoterm_location"] = 'San Diego'
        return res

    def action_ready_for_review(self):
        self.update({'state': 'ready_for_review'})

    def action_ready_for_confirm(self):
        self.update({'state': 'ready_for_confirm'})
        return

    def action_open_delivery_wizard(self):
        action = super().action_open_delivery_wizard()
        if self.shipping_pay_type in ['RECEIVER', 'THIRD PARTY']:
            action.get('context').update({'default_shipping_pay_type': self.shipping_pay_type,
                                          'default_shipping_carrier_id': self.shipping_carrier_id.id,
                                          'default_domestic_service_type_id': self.domestic_service_type_id.id})
        return action

    @api.onchange('shipping_pay_type')
    def _onchange_nascom_shipping_pay_type(self):
        ship_charge = 'PPD+ADD'
        if self.shipping_pay_type == 'SENDER':
            ship_charge = 'PPDNOADD'
        elif self.shipping_pay_type in ['THIRD PARTY', 'RECEIVER']:
            ship_charge = 'COLLECT'
        self.ship_charge = ship_charge

    @api.onchange('partner_id')
    def _onchange_partner_id_warning(self):
        shipping_pay_type = 'SENDER'
        if self.partner_id.is_company:
            self.shipping_carrier_id = self.partner_id.shipping_carrier_id and self.partner_id.shipping_carrier_id.id or False
            self.shipping_pay_type = self.partner_id.other_shipping_type or shipping_pay_type
            self.account = self.partner_id.account
            self.shipping_country_id = self.partner_id.country_id and self.partner_id.country_id.id or False
            self.shipping_zip = self.partner_id.zip
            self.invoice_email_to_partner_id = self.partner_id.inv_email_confirmation_partner_id and self.partner_id.inv_email_confirmation_partner_id.id or False
            self.domestic_service_type_id = self.partner_id.domestic_service_type_id and self.partner_id.domestic_service_type_id.id or False
        else:
            if self.partner_id.parent_id:
                self.shipping_carrier_id = self.partner_id.parent_id.shipping_carrier_id and self.partner_id.parent_id.shipping_carrier_id.id or False
                self.shipping_pay_type = self.partner_id.parent_id.other_shipping_type   or shipping_pay_type
                self.account = self.partner_id.parent_id.account
                self.shipping_country_id = self.partner_id.parent_id.country_id and self.partner_id.parent_id.country_id.id or False
                self.shipping_zip = self.partner_id.parent_id.zip
                self.invoice_email_to_partner_id = self.partner_id.parent_id.inv_email_confirmation_partner_id and self.partner_id.parent_id.inv_email_confirmation_partner_id.id or False
                self.domestic_service_type_id = self.partner_id.parent_id.domestic_service_type_id and self.partner_id.parent_id.domestic_service_type_id.id or False
            else:
                self.shipping_carrier_id = False
                self.shipping_pay_type = False
                self.account = False
                self.shipping_country_id = False
                self.shipping_zip = False
                self.invoice_email_to_partner_id = False
                self.domestic_service_type_id = False
        return super(SalesOrder, self.with_context(is_domestic_service_type_id=True))._onchange_partner_id_warning()

    def _compute_customer_pn(self):
        a = []
        for l in self:
            for rec in l.order_line:
                a.append(rec.part_no)
            l.is_customer_part_no = any(a)

    @api.model
    def create(self, vals):
        """
        - Add attachment to sales order based on the CRM data
        - Update Banner Message
        """
        res = super().create(vals)
        if res.opportunity_id:
            attachment_ids = self.env['ir.attachment'].search([
                ('res_model', '=', 'crm.lead'),
                ('res_id', '=', res.opportunity_id.id)
            ])
            for attachment in attachment_ids:
                attachment.copy({'res_model': 'sale.order', 'res_id': res.id})

        if res:
            if res.website_id:
                payment_term_id = self.env['account.payment.term'].sudo().search([('name', '=', "Net 30")], limit=1)
                if payment_term_id:
                    res.payment_term_id = payment_term_id
                res.shipping_pay_type == "SENDER"
                res.ship_charge = "PPD+ADD"
        return res

    def action_nascom_send_confirm_mail(self):
        template = False

        if not self.so_email_to_partner_id or not self.so_email_to_partner_id.email:
            raise ValidationError(_("Partner '%s' does not have email configured" % self.so_email_to_partner_id.name))

        if not self.so_email_to_partner_id:
            return self.action_quotation_send()

        if self.state in ['sent', 'sale']:
            template = self.env.ref('sale.mail_template_sale_confirmation')
        elif self.state == 'draft':
            template = self.env.ref('sale.email_template_edi_sale')

        if template:
            report = self.env['ir.actions.report']._render_qweb_pdf("bv_nascom_custom.report_nascom_sale_report", self.id)

            values = template.generate_email(
                self.id,
                ['subject', 'body_html',
                 'email_from',
                 'email_cc', 'email_to', 'reply_to',
                 'auto_delete', 'scheduled_date']
            )

            if values.get('attachments'):
                values.pop('attachments')

            if template.report_template:
                attachment_ids = []
                Attachment = self.env['ir.attachment']
                data_attach = {
                    'name': "Order Confirmation.pdf",
                    'datas': base64.b64encode(report[0]),
                    'res_model': 'sale.order',
                    'res_id': self.id,
                    'type': 'binary',
                }
                attachment_ids.append(Attachment.create(data_attach).id)
                if values.get('attachment_ids', []) or attachment_ids:
                    values['attachment_ids'] = [Command.set(attachment_ids)]

            values.update({'partner_ids': [], 'email_to': self.so_email_to_partner_id.email,
                           'message_type': 'comment', 'subtype_id': self.env.ref('mail.mt_comment').id})

            if self.company_id.display_cc:
                values.update({'email_cc': self.company_id.default_cc})
            if template.email_bcc:
                values.update({'email_bcc': template.email_bcc})

            mail_id = self.env['mail.mail'].sudo().create(values)
            mail_id.write({'mail_server_id' : self.company_id.mail_server_id.id})
            mail_id.sudo().send()

    def magento_sale_order_url(self):
        base_url = self.env["connector.instance"].search([], limit=1).name
        order_mapping = self.env["connector.order.mapping"].search([("name", "=", self.magento_order_number)], limit=1)

        if base_url and self.magento_order_number and order_mapping.ecommerce_order_id:
            order_url = f"{base_url}/sales/order/view/order_id/{order_mapping.ecommerce_order_id}/"
            return order_url

        return self.get_portal_url()

    def action_confirm(self):
        for order in self:
            if order.order_line.filtered(lambda ln: not ln.is_delivery and not ln.promise_date):
                raise ValidationError(_('Promise Date missing on order line.'))
            elif order.shipping_pay_type in ['THIRD PARTY', 'RECEIVER'] and order.order_line.filtered(lambda ln: ln.is_delivery and ln.price_unit > 0):
                raise ValidationError(_('Shipping amount must be 0.'))
        res = super().action_confirm()
        self.picking_ids.write({'delivery_email_to_partner_id': self.delivery_email_to_partner_id and self.delivery_email_to_partner_id.id or False})
        return res

    def delivery_carrier_service_type(self):
        service_type_ids = self.env['shipstation.delivery.carrier.service'].search([])
        return service_type_ids

    def assign_promise_date(self):
        """
        Assign Promise date to all the lines of sale order
        """
        self.ensure_one()
        wizard = self.env.ref("bv_nascom_custom.nascom_assign_delivery_date_form_view")
        return {
            "name": _("Assign Promise Date"),
            "type": "ir.actions.act_window",
            "view_mode": "form",
            "res_model": "nascom.assign.delivery.date",
            "views": [(wizard.id, "form")],
            "view_id": wizard.id,
            "target": "new",
            "context": {
                "default_order_id": self.id
            },
        }

    @api.onchange('partner_id')
    def onchange_nascom_partner_id(self):
        """ Change customer PN based on partner selection"""
        for order in self:
            order.order_line._nascom_customer_pn()

    def _get_invoiceable_lines(self, final=False):
        res = super()._get_invoiceable_lines(final=final)
        if self.ship_charge and (self.ship_charge != 'PPD+ADD'):
            return res.filtered(lambda ln: not ln.is_delivery)

        return res

    def _create_invoices(self, grouped=False, final=False, date=None):
        res = super()._create_invoices(grouped=grouped, final=final, date=date)
        picking_id = self.picking_ids.filtered(lambda x: x.state == 'done' and not x.invoice_id and x.carrier_tracking_ref)
        picking_id.write({
            'invoice_id' : res.id
        })

        res.write({
            'invoice_email_to_partner_id' : self.invoice_email_to_partner_id.id,
            'sale_id' : self.id,
            'carrier_tracking_ref' : ", ".join(picking_id.mapped('carrier_tracking_ref'))
        })
        # for inv in res:
        #     orders = inv.invoice_line_ids and inv.invoice_line_ids.mapped('sale_line_ids.order_id')
        #     if orders:
        #         inv.write({'invoice_email_to_partner_id': orders[0].invoice_email_to_partner_id and orders[0].invoice_email_to_partner_id.id or False,
        #                     'sale_id':orders[0].id
        #                     })
        return res

    def set_so_to_rtc(self):
        """
            This function is only for admin to set is closed false and ready for confrim state in SO.
        """
        query ='''UPDATE sale_order set is_closed=False, state='ready_for_confirm' WHERE id = %s''' % (self.id)
        self._cr.execute(query)
        self._cr.commit()

    def open_tracking_website_url(self):
        transfer = self.picking_ids.filtered(lambda x: x.picking_type_code == 'outgoing' and x.state == 'done')
        if transfer:
            return transfer[0].open_website_url()

    def _mass_invoice_create(self):
        "TO CREATE MASS INVOICE"
        sale_ids = self.browse(self.env.context.get('active_ids'))
        # to_close = []
        # closed = sale_ids.filtered(lambda x: not x.is_closed)
        # if closed:
        #     for close in closed:
        #         to_close.append(close.name)
        #     raise ValidationError(_('Sale order %s not closed yet!!') % (str(to_close)[1:-1]))
        for so in sale_ids:
            so.with_context(raise_if_nothing_to_invoice=False)._create_invoices()