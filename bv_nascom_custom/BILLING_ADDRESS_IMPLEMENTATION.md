# Portal User Billing Address Implementation

## Overview

This implementation adds custom billing address logic for portal users during website checkout and displays the `inv_email_confirmation_partner_id` information in the billing address section.

## Business Requirements

1. **Billing Address Logic**: When a portal user places an order:
   - If user has `parent_id`: Use parent company's billing address
   - If user has no `parent_id`: Use user's own billing address

2. **Email Display**: Show the `inv_email_confirmation_partner_id` in the billing address section:
   - If user has `parent_id`: Show parent's `inv_email_confirmation_partner_id`
   - If user has no `parent_id`: Show user's own `inv_email_confirmation_partner_id`

## Implementation Details

### Files Modified/Added

1. **`controllers/website_sale.py`** - New file
   - Overrides `WebsiteSale` controller methods
   - Implements billing address logic in checkout flow
   - Methods: `_checkout_form_save()`, `address()`, `checkout()`

2. **`models/sale_order.py`** - Modified
   - Added `_set_billing_address_for_portal_user()` method
   - Modified `create()` method to apply billing logic for website orders
   - Added `write()` method override to handle partner updates

3. **`views/website_billing_address_template.xml`** - New file
   - Inherits from website_sale templates (around line 1685)
   - Shows `inv_email_confirmation_partner_id` in billing address section
   - Displays email information based on parent_id relationship

4. **`controllers/__init__.py`** - Modified
   - Added import for new website_sale controller

5. **`__manifest__.py`** - Modified
   - Added `website_sale` dependency
   - Added new template file to data list

6. **`tests/test_billing_address_logic.py`** - New file
   - Unit tests to verify billing address logic
   - Tests for both parent_id and independent scenarios

### Key Features

#### Template Enhancement (Line 1685 Reference)
The template inherits from `website_sale.checkout` and adds an information box after the "Billing Address" header that shows:

```xml
<div class="alert alert-info mt-2 mb-3">
    <i class="fa fa-envelope me-1"></i>
    <strong>Invoice will be sent to:</strong> 
    [Partner Name] ([Email Address])
</div>
```

#### Automatic Billing Address Selection
- Portal users automatically get the correct billing address based on their parent_id relationship
- No manual selection required during checkout
- Seamless integration with existing Odoo checkout flow

#### Email Information Display
- Shows who will receive the invoice email
- Dynamically updates based on user's parent_id relationship
- Fallback to user's own email if no `inv_email_confirmation_partner_id` is set

## Usage Examples

### Scenario 1: Child Portal User
```
Parent Company: ABC Corp (inv_email_confirmation_partner_id: <EMAIL>)
Child Contact: John Doe (portal user, parent_id = ABC Corp)

Checkout Display:
- Billing Address: ABC Corp's address
- Email Info: "Invoice will be sent to: Accounts Department (<EMAIL>)"
```

### Scenario 2: Independent Portal User
```
Independent Contact: Jane Smith (portal user, inv_email_confirmation_partner_id: <EMAIL>)

Checkout Display:
- Billing Address: Jane Smith's address  
- Email Info: "Invoice will be sent to: Jane Smith (<EMAIL>)"
```

## Testing

The implementation includes unit tests that verify:
- Correct billing address selection for users with parent_id
- Correct billing address selection for independent users
- No impact on public user checkout flow

## Installation

1. Update the module with the new files
2. Restart Odoo server
3. Update the `bv_nascom_custom` module
4. The functionality will be automatically available for portal users

## Notes

- Only applies to logged-in portal users (not public users)
- Preserves existing Odoo security and access controls
- Compatible with existing checkout customizations
- Template inheritance ensures compatibility with future Odoo updates
