# -*- coding: utf-8 -*-
{
    'name': 'BV Nascom Custom',
    'version': '********.0',
    'summary': 'Nascom-Customise functionality',
    'sequence': '10',
    'license': 'AGPL-3',
    'author': 'Brainvire Infotech Inc',
    'category': 'General',
    'website': 'https://brainvire.com',
    'depends': [
        'base', 'mail','product', 'sale_management', 'mrp', 'purchase_stock', 'sale_product_configurator', 'sale_stock', 'stock', 'account',
        'shipstation_shipping_odoo_integration', 'crm', 'delivery', 'website', 'website_sale', 'account'
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/security.xml',
        'data/ir_action_server.xml',
        'data/ir_sequence.xml',
        'data/mail_templates_email_layouts.xml',
        'report/invoice_report.xml',
        'data/ir_attachment.xml',
        'data/sale_order_mail_template_inherit.xml',
        'data/sale_order_quotation_mail_template_ineheirt.xml',
        'data/invoice_mail_template_inherit.xml',
        'data/delivery_mail_template_inherit.xml',
        'data/nascom_payment_reminder.xml',
        'data/website_menu_inherited.xml',
        'report/nascom_shipping_label.xml',
        'views/sale_order.xml',
        'views/product_template.xml',
        'wizard/nascom_import_details.xml',
        'views/product_attibute.xml',
        'views/nascom_attribute_validation.xml',
        'views/nascom_base_pn_data.xml',
        'views/product_product.xml',
        'wizard/nascom_assign_delivery_date.xml',
        'views/sale_order_line.xml',
        'views/account_move.xml',
        'views/sale_portal_template.xml',
        'views/nascom_customer_ref_details.xml',
        'views/stock_picking.xml',
        'views/nascom_customer_pn_info.xml',
        'views/crm_lead.xml',
        'views/res_partner.xml',
        'wizard/choose_delivery_carrier.xml',
        'views/delivery_carrier.xml',
        'views/res_company.xml',
        'views/report_invoice_inherit.xml',
        'views/email_template_view.xml',
        'views/mail_mail_view.xml',
        'views/payment_token_views.xml',
        'views/product_public_category.xml',
        'views/account_journal.xml',
        'views/res_config_settings.xml',
        'views/shipstation_delivery_carrier.xml',
        'views/website_billing_address_template.xml'
    ],
    'assets': {
        'web.assets_backend': [
            'bv_nascom_custom/static/src/js/product_configurator_modal.js',
        ],

        'web.assets_frontend': [
            'bv_nascom_custom/static/src/js/product_configurator_modal.js',
            'bv_nascom_custom/static/src/js/website_product_cn_control.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
