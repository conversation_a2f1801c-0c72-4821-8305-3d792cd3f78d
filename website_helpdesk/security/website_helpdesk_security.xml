<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="helpdesk_team_public_user" model="ir.rule">
            <field name="name">Public User: Published Helpdesk Team</field>
            <field name="model_id" ref="helpdesk.model_helpdesk_team"/>
            <field name="domain_force">[('website_published', '=', True)]</field>
            <field name="groups" eval="[(4, ref('base.group_public'))]"/>
        </record>
    </data>
</odoo>
