 
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
.Python
env/
bin/
build/
develop-eggs/
dist/
eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.eggs
AUTHORS
ChangeLog

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.cache
nosetests.xml
coverage.xml

# Translations
*.mo

.vscode

# Pycharm
.idea

# Django stuff:
*.log

.mr.developer.cfg
.project
.pydevproject

# Rope
.ropeproject

# Sphinx documentation
docs/_build/

# Backup files
*~
*.swp



# file generated when running tests
.cprofile_*
node_modules
package-lock.json