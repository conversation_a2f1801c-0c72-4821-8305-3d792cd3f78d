# -*- coding: utf-8 -*-
##############################################################################
# Copyright (c) 2015-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>)
# See LICENSE file for full copyright and licensing details.
# License URL : <https://store.webkul.com/license.html/>
##############################################################################


from odoo import http
from odoo.http import request, route
from odoo.addons.sale.controllers.variant import VariantController



class WkAmazonS3Customization(VariantController):

    def get_ptav_images_carousel(self, product_template_id, product_id, attribute_value_id=False, **kw):
        if attribute_value_id:
            attribute_value = request.env['product.template.attribute.value']
            ptav = attribute_value.browse(attribute_value_id)
            data = attribute_value._get_ptav_images(ptav)
            if data:
                image = []
                for i in data:
                    image = image + list(list(i.values())[0])
                html = request.env['ir.ui.view']._render_template('wk_ptav_images.attribute_media_page',
                values={
                    'media': image,
                    'product': request.env['product.template'].browse(int(product_template_id)),
                    'product_variant': request.env['product.product'].browse(int(product_id)),
                })
                return html
        return False

    @http.route(['/sale/get_combination_info_website'], type='json', auth="public", methods=['POST'], website=True)
    def get_combination_info_website(self, product_template_id, product_id, combination, add_qty, **kw):
        """Special route to use website logic in get_combination_info override.
        This route is called in JS by appending _website to the base route.
        """
        res = super().get_combination_info_website(product_template_id=product_template_id,
                                                    product_id=product_id,
                                                    combination=combination,
                                                    add_qty=add_qty, **kw)
        website = request.env['website'].get_current_website()
        ptav_extra_images = website.ptav_extra_images
        wk_is_merge = website.wk_is_merge
        if ptav_extra_images and kw.get('changed_attrib') and not wk_is_merge:
            data = self.get_ptav_images_carousel(product_template_id,product_id,kw.get('changed_attrib'))
            if data:
                res['carousel'] = data
                return res
        return res