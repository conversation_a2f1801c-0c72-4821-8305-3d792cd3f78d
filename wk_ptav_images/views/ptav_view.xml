<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<odoo>
    <record model="ir.ui.view" id="s3_image_view">
        <field name="name">s3.image.button</field>
        <field name="model">product.template.attribute.value</field>
        <field name="inherit_id" ref="product.product_template_attribute_value_view_form"/>
        <field name="arch" type="xml">
        <xpath expr="//field[@name='exclude_for']" position="after">
        <field name="ptav_extra_image_ids" attrs="{'invisible': [('ptav_config', '=', False)]}" 
            class="o_website_sale_image_list"  context="{'default_name': name}" mode="kanban" options="{'create_text':'Add a Media'}" />
        <field name="ptav_config" invisible='1' />
        </xpath>
        </field>
    </record>
</odoo>
