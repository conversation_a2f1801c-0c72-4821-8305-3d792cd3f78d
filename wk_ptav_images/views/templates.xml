<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<odoo>

    <template id="attribute_media_page" name="attribute_media_page">
        <div id="o-carousel-product" class="carousel slide position-sticky mb-3 overflow-hidden" data-ride="carousel" data-interval="0">
            <div class="o_carousel_product_outer carousel-outer position-relative flex-grow-1">
                <div class="carousel-inner h-100">
                    <t t-foreach="media" t-as="extra_media">
                        <div t-attf-class="carousel-item h-100#{' active' if media.index(extra_media) == 0 else ''}">
                            <div class="d-flex align-items-center justify-content-center h-100 oe_unmovable">
                                <img t-att-src="'/web/image/product.image/%s/image_1024/' % str(extra_media.id)"
                                    class='img img-fluid oe_unmovable product_detail_img mh-100'
                                    style='max-width:630px; max-height:400px;' />
                            </div>
                        </div>
                    </t>
                </div>
                <t t-if="len(media) > 1">
                    <a class="carousel-control-prev" href="#o-carousel-product" role="button"
                        data-bs-slide="prev">
                        <span class="fa fa-chevron-left fa-2x oe_unmovable" role="img" aria-label="Previous"
                            title="Previous" />
                    </a>
                    <a class="carousel-control-next" href="#o-carousel-product" role="button"
                        data-bs-slide="next">
                        <span class="fa fa-chevron-right fa-2x oe_unmovable" role="img" aria-label="Next"
                            title="Next" />
                    </a>
                </t>
                <t t-else="">
                </t>
            </div>
            <div t-ignore="True" class="o_carousel_product_indicators pt-2 overflow-hidden">
                <ol t-if="len(media) > 1"
                    class="carousel-indicators position-static pt-2 pt-lg-0 mx-auto my-0 text-start" style="justify-content: start;">
                    <t t-foreach="media" t-as="extra_media">
                        <li t-attf-class="position-relative align-top {{'active' if media.index(extra_media) == 0 else ''}}"
                            data-bs-target="#o-carousel-product"
                            t-att-data-bs-slide-to="str(media.index(extra_media))">
                            <img t-att-src="'/web/image/product.image/%s/image_1024/' % str(extra_media.id)" class='o_image_64_contain' />
                        </li>
                    </t>
                </ol>
            </div>
        </div>
    </template>

    <template id="s3_customization_products_attributes" name="s3_customization_products_attributes"
        inherit_id="sale.variants">
        <xpath expr='//li[@t-foreach="ptal.product_template_value_ids._only_active()"]' position="replace">
            <li t-foreach="ptal.product_template_value_ids._only_active()" t-as="ptav" class="list-inline-item">
                <t t-if="ptav.ptav_extra_image_ids and ptav.env['website'].get_current_website().ptav_extra_images">
                    <label
                        t-attf-style="background:url(#{'/web/image/product.image/%s/image_1024' % ptav.ptav_extra_image_ids[0].id if ptav.ptav_extra_image_ids else '' });
                        background-color:#{ptav.html_color or ptav.product_attribute_value_id.name if ( not ptav.is_custom and not ptav.ptav_extra_image_ids) else ''};
                        width: 38px; height: 50px; background-size: contain;background-repeat: no-repeat; background-position: center;"
                        t-attf-class="css_attribute_color wk_custom_color">
                        <input type="radio"
                            t-attf-class="js_variant_change  #{ptal.attribute_id.create_variant}"
                            t-att-checked="ptav in combination"
                            t-att-name="'ptal-%s' % ptal.id"
                            t-att-value="ptav.id"
                            t-att-title="ptav.name"
                            t-att-data-value_id="ptav.id"
                            t-att-data-value_name="ptav.name"
                            t-att-data-attribute_name="ptav.attribute_id.name"
                            t-att-data-is_custom="ptav.is_custom"
                            t-att-data-is_single="single"
                            t-att-data-is_single_and_custom="single_and_custom" />
                    </label>
                </t>
                <t t-else="">
                    <label
                        t-attf-style="background-color:#{ptav.html_color or ptav.product_attribute_value_id.name if not ptav.is_custom else ''};
                        width: 38px; height: 50px;"
                        t-attf-class="css_attribute_color wk_custom_color #{'active' if ptav in combination else ''} #{'custom_value' if ptav.is_custom else ''}">
                        <input type="radio"
                            t-attf-class="js_variant_change  #{ptal.attribute_id.create_variant}"
                            t-att-checked="ptav in combination"
                            t-att-name="'ptal-%s' % ptal.id"
                            t-att-value="ptav.id"
                            t-att-title="ptav.name"
                            t-att-data-value_id="ptav.id"
                            t-att-data-value_name="ptav.name"
                            t-att-data-attribute_name="ptav.attribute_id.name"
                            t-att-data-is_custom="ptav.is_custom"
                            t-att-data-is_single="single"
                            t-att-data-is_single_and_custom="single_and_custom" />
                    </label>
                </t>
            </li>
        </xpath>

        <xpath expr="//strong[hasclass('attribute_name')]" position="replace">
            <t t-if="ptal.attribute_id.display_type == 'color'">
                <strong class="attribute_name">
                    <t t-esc="ptal.attribute_id.name" />: <t t-esc="ptal.value_ids[0].name"/>
                </strong>
            </t>
            <t t-else="">
                <strong t-field="ptal.attribute_id.name" class="attribute_name"/>
            </t>
        </xpath>
    </template>
</odoo>