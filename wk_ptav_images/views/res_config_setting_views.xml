<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.ptav.images</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="website_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div id='hide_add_to_cart_setting' position="after">
                <div class="col-12 col-lg-6 o_setting_box" id="ptav_extra_images">
                    <div class="o_setting_left_pane">
                        <field name="ptav_extra_images_config" />
                    </div>
                    <div class="o_setting_right_pane" name="ptav_extra_images   ">
                        <label for="ptav_extra_images_config"/>
                        <div class="text-muted">
                            Extra Images for Attributes
                        </div>
                        <label for="wk_is_merge_config"/>
                        <field name="wk_is_merge_config" />
                    </div>
                </div>
            </div>
        </field>
    </record>

</odoo>
