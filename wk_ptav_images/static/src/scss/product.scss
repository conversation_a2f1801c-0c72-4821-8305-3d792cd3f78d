$base-th-color: map-get($o-color-palette, o-color-1);
$btn-color: map-get($o-user-color-palette, o-cc2-btn-primary-border);

label.css_attribute_color.css_not_available {
    display: none;
}

.css_attribute_color:before {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0;
    border: 0;
    box-shadow:none;
}

.css_attribute_color:hover{
    border: 2px solid $base-th-color;
}

.css_attribute_color.active {
    border: 2px solid $base-th-color;
}

.css_attribute_color {
    border: 2px solid #CED4DA;
    border-radius: 0;
}