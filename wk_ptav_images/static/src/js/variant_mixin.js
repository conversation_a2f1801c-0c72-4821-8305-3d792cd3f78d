/* Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) */
/* See LICENSE file for full copyright and licensing details. */
/* License URL : <https://store.webkul.com/license.html/> */

odoo.define('wk_ptav_images.VariantMixin', function (require) {
    'use strict';
    
    var publicWidget = require('web.public.widget');
    require('website_sale.website_sale');

    publicWidget.registry.WebsiteSale.include({

        onChangeVariant: function (ev) {
            if( $(ev.target)[0] != $('.js_add_cart_variants')[0] ){
                this.$ev = $(ev.target);
            }
            return this._super.apply(this, arguments)
        },

        _getOptionalCombinationInfoParam: function ($product) {
            var rec = this._super.apply(this, arguments)
            if( this.$ev ) {
                var changed_attrib = this.$ev.data('value_id');
                if(changed_attrib){
                    rec['changed_attrib'] = changed_attrib
                    return rec;
                }
            }
            return rec;
        },
    });
});