/* Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) */
/* See LICENSE file for full copyright and licensing details. */
/* License URL : <https://store.webkul.com/license.html/> */

odoo.define('wk_ptav_images.wk_custom_color', function (require) {
    'use strict';
    
    var publicWidget = require('web.public.widget');
    var VariantMixin = require('sale.VariantMixin');
    var core = require('web.core');
    var _t = core._t;
    
    
    publicWidget.registry.cssAttributeColor = publicWidget.Widget.extend({
        selector: '.wk_custom_color',
        events: {
            'mouseenter': '_onMouseEnter',
            'mouseleave': '_onMouseLeave',
            'click': '_onclick'
        },

        init: function() {
            this._super.apply(this, arguments);
            if ($(document).find('.wk_custom_color').length > 1){
                var $input = $(document).find('.wk_custom_color > input:checked')
                this.attribute_name = $input.data('attribute_name');
                var name = $input.data('value_name');
                $(document).find('.wk_custom_color').parents('.variant_attribute').find('strong')
                    .text(this.attribute_name+': '+name);
            }
        },

        _onMouseEnter: function (ev) {
            if ($(document).find('.wk_custom_color').length > 1){
                this.prev_massege = $(ev.target).parents('.variant_attribute ').find('strong').text();
                this.attribute_name = $(ev.target).find('input').data('attribute_name');
                var name = $(ev.target).find('input').data('value_name');
                $(ev.target).parents('.variant_attribute ').find('strong').text(this.attribute_name+': '+name);
            }
        },
        _onMouseLeave: function (ev) {
            if ($(document).find('.wk_custom_color').length > 1){
                $(ev.target).parents('.variant_attribute ').find('strong').text(this.prev_massege);
            }
        },
        _onclick: function (ev) {
            if ($(document).find('.wk_custom_color').length > 1){
                this.prev_massege = $(ev.target).parents('.variant_attribute ').find('strong').text();
            }
        },
    })
});
