<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
  integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous" />
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"
  integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
  integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
  integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"
  integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
<link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700&display=swap" rel="stylesheet">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<div class="container">
  <section class="oe_container" id="wk_header">
    <div class="mt32 mb32">
      <h2 class="text-center"
        style="font-family:'Open Sans', sans-serif; line-height: 41px; font-size:30px; color:#000000">
        <strong> Odoo Attribute Extra Images</strong>
      </h2>
      <h5 class="mt8 mb8 text-center"
        style="font-weight:500;font-family:'Open Sans', sans-serif; font-size:18px;color:#555555">
        <i>
          Set Attribute images in Odoo!
        </i>
      </h5>
    </div>
    <div class="row mt16 mb16 py-2 m-1"
      style="font-weight:500;font-family:'Open Sans', sans-serif; font-size:18px; color:#555555">
      <div class="container col-md-6  border-right"
        style="font-family:'Open Sans', sans-serif;font-weight:500; line-height:24px; font-size:16px; color:#555555; vertical-align:middle; display:grid">
        <p style="height:fit-content" class="mb4 mt4 mr8">
          Attribute Extra Images enhances the product attributes by adding images for every attribute. Moreover, it
          allows merging one attribute image with another, which helps create visually appealing and informative product
          listings. The Odoo app makes products attractive and enriches the customer experience in Odoo.
        </p>
      </div>
      <div class="container col-md-6 border-left">
        <h4 class="ml-md-3 mt8"><strong>Information</strong></h4>
        <div class="mt-2 ml-md-3" style="display:flex">
          <span class="ml-1 mr-2 my-auto"><img src="icon-user-guide.png" alt="user-guide"></span>
          <div style="font-weight:600; font-size:14px; line-height:10px" class="mt-2 ml-2">User Guide
          </div>
        </div>
        <div class="ml-md-3" style="display:flex">
          <span style="font-size:16px; margin-top:0.2rem">https://webkul.com/blog/</span>
        </div>
      </div>
    </div>
  </section>

  <section class="container mb-4">
    <div class="row m-1 p-3 align-items-center border">
      <span style="padding: 10px 15px 5px;"><img style="width: 72px; height: 72px;" src="icon-1.png"
          alt="image" /></span>
      <div style="font-weight: 600; font-size: 18px; line-height: 30px">
        Extra Image for Product Attribute Value!</div>
      <div class="col-md-12" style="padding: 15px">
        <p style="
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 27px;
            color: #646464;
            margin-bottom: 0;
          ">
          Multiple product attribute images offer significant benefits. Most importantly, they enhance the overall
          product presentation and user experience. It provides clarity and accuracy regarding the various attributes
          available for a product. The attribute images help assess the difference between attribute options which aids
          customers make decisions readily after evaluating the differences between attribute options.
        </p><br>
        <p style="
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 27px;
            color: #646464;
            margin-bottom: 0;
          ">
          In addition, multi-attribute images make the browsing and filtering procedure more efficient and enjoyable for
          customers. It allows them to compare and identify different attribute options. Attribute images enable you to
          add various attribute combinations such as color, size, style, etc.
        </p><br>
        <p style="
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 27px;
            color: #646464;
            margin-bottom: 0;
          ">
          Further, multiple attribute images help provide a richer and more engaging shopping experience.
        </p>
      </div>
    </div>
  </section>

  <section class="container mb-4">
    <div class="row p-3 m-1 align-items-center border">
      <span style="padding: 10px 15px 5px;"><img style="width: 72px; height: 72px;" src="icon-2.png"
          alt="image" /></span>
      <div style="font-weight: 600; font-size: 18px; line-height: 30px">
        Benefits of Adding Attribute Extra Images</div>
      <div class="col-md-12 ml-1" style="padding: 15px">
        <p style="
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 27px;
            color: #646464;
            margin-bottom: 0;
          ">
          Multiple attribute images enhance the customers' shopping experience. It incorporates visual cues about the
          various attributes available. Also, attribute images eliminate ambiguity and help customers select the right
          products variant.
        </p><br>
        <p style="
            font-family: Open Sans;
            font-style: normal;
            font-weight: normal;
            font-size: 16px;
            line-height: 27px;
            color: #646464;
            margin-bottom: 0;
          ">
          Attribute images also improve the overall product presentations. By incorporating multiple images, business
          owners can create more visually appealing and engaging listings that capture the customers' attention. And it
          creates a more captivating shopping experience. It inspires the customers to engage with listings that provide
          wide attribute images which can influence product purchases.
        </p>
      </div>
    </div>
  </section>

  <section class="oe_container lead mb-4">
    <div class="container">
      <div class="row p-3 justify-content-center">
        <div class="sm p-3 ">
          <h3 class="d-flex align-items-start justify-content-center">
            <span
              style="font-family:'Open Sans',sans-serif; font-weight:700; font-size:36px; line-height:49px; color:#000000">
              Highlighted Features
            </span>
          </h3>
          <p class="d-flex align-items-start justify-content-center"
            style="font-family:'Open Sans',sans-serif; font-weight:normal; font-size:16px; line-height:23px; color:#646464">
            Below is the detailed list of Feature for Odoo Attribute Extra Images</p>
        </div>
      </div>
      <div class="d-flex row mb-4 justify-content-center">
        <div class="col-lg-5 col-sm-12">
          <div class="card-body px-0 justify-content-start">
            <div class="d-flex align-items-center ">
              <img class="mr-2" src="feature-icon-1.png" alt="Icon">
              <span class="pl-3"
                style="font-family:'Open Sans',sans-serif; font-weight:700; font-size:18px; line-height:22px; color:bold; color: #333333;">
                Add multiple attribute images for products
              </span>
            </div>
          </div>
        </div>
        <div class="col-lg-5 col-sm-12">
          <div class="card-body px-0 justify-content-start w-40">
            <div class="d-flex align-items-center">
              <img class="mr-2" src="feature-icon-2.png" alt="Icon">
              <span class="pl-3"
                style="font-family:'Open Sans',sans-serif; font-weight:700; font-size:18px; line-height:22px; color:bold; color: #333333;">
                Merge multiple attributes' images
              </span>
            </div>
          </div>
        </div>
        <div class="col-lg-5 col-sm-12">
          <div class="card-body px-0 justify-content-start">
            <div class="d-flex align-items-center">
              <img class="mr-2" src="feature-icon-3.png" alt="Icon">
              <span class="pl-3"
                style="font-family:'Open Sans',sans-serif; font-weight:700; font-size:18px; line-height:22px; color:bold; color: #333333;">
                Enhances the shopping experience
              </span>
            </div>
          </div>
        </div>
        <div class="col-lg-5 col-sm-12">
          <div class="card-body px-0 justify-content-start">
            <div class="d-flex align-items-center">
              <img class="mr-2" src="feature-icon-4.png" alt="Icon">
              <span class="pl-3"
                style="font-family:'Open Sans',sans-serif; font-weight:700; font-size:18px; line-height:22px; color:bold; color: #333333;">
                Eliminates attribute ambiguity
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="oe_spaced mw-100 card" style="margin-top:20px; margin-bottom:20px; border: 0px;">
    <div class="container p-0"
      style="font-family:'Open Sans', sans-serif; font-weight:600; font-size:18px; line-height:28px; color:#333333">
      <ul class="mp-nav nav nav-tabs justify-content-center pt-2" id="myTab" role="tablist"
        style="background-color:#EBF3F5">
        <li class="nav-item mr-sm-1" style="color:white; background-color:#EBF3F5;cursor: pointer;">
          <span class="nav-link active" id="screenshots-tab" data-toggle="tab" style="color:#2335D7; padding:7px 20px; border-radius:10px 10px 0px 0px; border:1px solid #2335D7; 
                  border-bottom:0px; font-weight: 600; line-height: 26px;" href="#ss" role="tab">
            Screenshots
          </span>
        </li>
        <li class="nav-item border-left ml-sm-1" style="color:white; background-color:#EBF3F5;cursor: pointer;">
          <span class="nav-link" id="configuration-tab" data-toggle="tab" style="color:#2335D7; padding:7px 20px; border-radius:10px 10px 0px 0px; border:1px solid #2335D7; 
                  border-bottom:0px; font-weight: 600; line-height: 26px;" href="#features" role="tab">
            Detailed features
          </span>
        </li>
      </ul>
    </div>
    <div class="card-body tab-content p-0" style="font-weight:400">
      <div class="tab-pane fade slow" id="features" role="tabpanel">
        <section class="mt32 mb32">
          <div class="p-4 mb-2 ml32 mr32" style="color:#64687A; font-family:'Open Sans', sans-serif">
            <h3 style="
                            margin: 0;
                            font-family: Open Sans,sans-serif;
                            font-style: normal;
                            font-weight: 600;
                            font-size: 26px;
                            line-height: 38px;
                            color: #333333;
                            ">
              Detailed Features List
            </h3>
            <p class="mb-0" style="
                            font-family: Open Sans,sans-serif;	
                            font-style: normal;
                            font-weight: normal;
                            font-size: 16px;
                            line-height: 37px;
                            color: #555555;
                              ">
              Below is the list of features for Odoo Attribute Extra Images
            </p>
          </div>
          <div class="col-md-12">
            <div class="row pl-4" style="max-width: 100%; border-radius: 2px">
              <div style="
                                font-size: 18px;
                                color: #333333;
                                line-height: 25px;
                                padding: 1rem;
                                " class="col-md-6">
                <div style="padding: 4% 4% 1% 4%; height: 100%; box-shadow: 2px 3px 8px rgba(0, 0, 0, 0.12);">
                  <h4 style="font-family: Open Sans,sans-serif;
                                        font-style: normal;
                                        font-weight: 600;
                                        font-size: 18px;
                                        line-height: 22px;
                                        color: #333333;">
                    Configure Attribute Images Settings
                  </h4>
                  <div class="container mt16">
                    <ul style="font-family: Open Sans,sans-serif;
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 16px;
                                            line-height: 22px;
                                            color: #555555;" class="fa-ul mt-2 p-2 ml-3">
                      <li class="mb-2"><i class="fa-li fa fa-check" style="color:#500FDA"></i>
                        Enable/disable attribute extra images settings from the backend.
                      </li>
                      <li class="mt-2"><i class="fa-li fa fa-check" style="color:#500FDA"></i>
                        You can also enable the merge images settings for different attributes of the same product.
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div style="
                          font-size: 18px;
                          color: #333333;
                          line-height: 25px;
                          padding: 1rem;
                        " class="col-md-6">
                <div style="padding: 4% 4% 1% 4%; height: 100%; box-shadow: 2px 3px 8px rgba(0, 0, 0, 0.12);">
                  <h4 style="font-family: Open Sans,sans-serif;
                            font-style: normal;
                            font-weight: 600;
                            font-size: 18px;
                            line-height: 30px;
                            color: #333333;">
                    Add Different Images for Product Attributes
                  </h4>
                  <div class="container mt16">
                    <ul style="font-family: Open Sans,sans-serif;
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 16px;
                                            line-height: 22px;
                                            color: #555555;" class="fa-ul mt-2 p-2 ml-3">
                      <li class="mb-2"><i class="fa-li fa fa-check" style="color:#500FDA"></i>
                        The Odoo app permits adding images for every product attribute.
                      </li>
                      <li class="mt-2"><i class="fa-li fa fa-check" style="color:#500FDA"></i>
                        You can add multiple images for a product attribute.
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <div class="tab-pane fade slow show active" id="ss" role="tabpanel">
        <div class="container shadow-sm px-4">
          <section class="oe_container my-4">
            <div class="row text-center" style="
                      border-radius: 2px;
                      padding: 2rem;
                      justify-content: center;">
              <div class="container text-center">
                <div class="card mb-3">
                  <div class="card-title text-light py-3"
                    style="background-color: #714B67;font-family:Montserrat; font-style:normal; font-weight:600; font-size:18px; line-height:22px; text-align:center; color:#333333">
                    Enabling attribute extra images feature with merge image setting from the backend
                  </div>
                  <div class="row card-body px-md-5">
                    <div class="col-md-12" style="border: 1px solid rgba(0, 0, 0, 0.06); margin-top: 1rem">
                      <img class="image image-responsive" src="1.png" style="width: 100%">
                    </div>
                  </div>
                </div>
                <div class="card mb-3">
                  <div class="card-title text-light py-3"
                    style="background-color: #714B67; font-family:Montserrat; font-style:normal; font-weight:600; font-size:18px; line-height:22px; text-align:center; color:#333333">
                    Enabling the attribute extra images feature without enabling merge image settings from the backend
                  </div>
                  <div class="row card-body px-md-5">
                    <div class="col-md-12" style="border: 1px solid rgba(0, 0, 0, 0.06); margin-top: 1rem">
                      <img class="image image-responsive" src="2.png" style="width: 100%">
                    </div>
                  </div>
                </div>
                <div class="card mb-3">
                  <div class="card-title text-light py-3"
                    style="background-color: #714B67; font-family:Montserrat; font-style:normal; font-weight:600; font-size:18px; line-height:22px; text-align:center; color:#333333">
                    Configuring extra images in product attribute
                  </div>
                  <div class="row card-body px-md-5">
                    <div class="col-md-12" style="border: 1px solid rgba(0, 0, 0, 0.06); margin-top: 1rem">
                      <img class="image image-responsive" src="3.png" style="width: 100%">
                    </div>
                  </div>
                </div>
                <div class="card mb-3">
                  <div class="card-title text-light py-3"
                    style="background-color: #714B67; font-family:Montserrat; font-style:normal; font-weight:600; font-size:18px; line-height:22px; text-align:center; color:#333333">
                    Odoo website with attribute images when only the attribute extra images feature is enabled
                  </div>
                  <div class="row card-body px-md-5">
                    <div class="col-md-12" style="border: 1px solid rgba(0, 0, 0, 0.06); margin-top: 1rem">
                      <img class="image image-responsive" src="4.png" style="width: 100%">
                    </div>
                  </div>
                </div>
                <div class="card mb-3">
                  <div class="card-title text-light py-3"
                    style="background-color: #714B67; font-family:Montserrat; font-style:normal; font-weight:600; font-size:18px; line-height:22px; text-align:center; color:#333333">
                    Odoo website with attribute images when the attribute extra images and merge image setting is enabled
                  </div>
                  <div class="row card-body px-md-5">
                    <div class="col-md-12" style="border: 1px solid rgba(0, 0, 0, 0.06); margin-top: 1rem">
                      <img class="image image-responsive" src="5.png" style="width: 100%">
                    </div>
                  </div>
                </div>
                <div class="card mb-3">
                  <div class="card-title text-light py-3"
                    style="background-color: #714B67; font-family:Montserrat; font-style:normal; font-weight:600; font-size:18px; line-height:22px; text-align:center; color:#333333">
                    Odoo website when the attribute extra images feature is disabled
                  </div>
                  <div class="row card-body px-md-5">
                    <div class="col-md-12" style="border: 1px solid rgba(0, 0, 0, 0.06); margin-top: 1rem">
                      <img class="image image-responsive" src="6.png" style="width: 100%">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </section>

  <section class="container mb-5 mt-5" id="webkul_support">
    <div class="row">
      <div class="col-12">
        <h3 class="text-center" style="font-weight: 700; font-size: 24px; line-height: 29px; color: #333333;">
          Support</h3>
        <p class="mb-2 text-center" style="font-weight: 400; font-size: 13px; line-height: 22px; color: #333333;">
          Get Immediate support for any of your query
        </p>
      </div>
      <div class="col-12">
        <p class="text-center px-5" style="font-weight: 400; font-size: 14px; line-height: 20px; color: #555555;">
          You will get 90 days free support for any doubt, queries, and bug fixing
          (excluding data recovery) or any type of issue related to this module.
        </p>
      </div>
      <div class="mx-auto col-lg-9 mb-4 mt-2 p-3" style="border: 1px solid #D9D9D9; border-radius: 4px;">
        <div class="row align-items-center mx-0">
          <div class="col-sm-2 col-lg-1 text-center p-0">
            <img src="mail.png" alt="mail" class="img" />
          </div>
          <div class="col-lg-auto col-sm-10" style="padding-right: 0;">
            <p class="my-2" style="font-weight: 600; font-size: 16px; line-height: 22px; color: #555555;">
              Write a mail to us:
            </p>
            <a style="font-size: 18px; font-weight: 600; line-height: 16px;"
              href="mailto:<EMAIL>"><EMAIL></a>
            <p class="my-2" style="font-weight: normal; font-size: 14px; color: #777777">
              Any queries or want any extra features? Just drop a mail to our
              support.
            </p>
          </div>
          <div class="col-lg-3 col-sm-4 mt-3 mt-lg-0 offset-sm-2 offset-lg-1">
            <a href="mailto:<EMAIL>" style="
                        padding: 10px 22px;
                        background-color: #2335d7;
                        font-size: 14px;
                        color: #ffffff;
                        white-space: nowrap;
                        "><i class="fa fa-pencil-square-o" style="color: white; margin-right: 4px"></i>Write To US</a>
          </div>
        </div>
      </div>
      <div class="mx-auto col-lg-9 p-3" style="border: 1px solid #D9D9D9; border-radius: 4px;">
        <div class="row align-items-center mx-0">
          <div class="col-sm-2 col-lg-1 text-center p-0">
            <img src="support-icon.png" alt="support-icon" class="img" />
          </div>
          <div class="col-lg-auto col-sm-10" style="padding-right: 0;">
            <p class="my-2" style="font-weight: 600; font-size: 16px; line-height: 22px; color: #555555;">
              Raise A Ticket At
            </p>
            <b class='text-break' style="font-size: 18px; font-weight: 600; line-height: 16px;color: #007bff; word-wrap: break-word;"
              href="">https://webkul.uvdesk.com/en/customer/create-ticket/</b>
            <p class="my-2" style="font-weight: normal; font-size: 14px; color: #777777">
              For support or to request customization, Create A Support Ticket by copying the link and
              opening it in a new tab
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>