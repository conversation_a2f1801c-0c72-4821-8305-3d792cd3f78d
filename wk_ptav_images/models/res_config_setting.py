# -*- coding: utf-8 -*-
##############################################################################
# Copyright (c) 2015-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>)
# See LICENSE file for full copyright and licensing details.
# License URL : <https://store.webkul.com/license.html/>
##############################################################################

from odoo import api, models, fields


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    ptav_extra_images_config = fields.<PERSON><PERSON>an(related='website_id.ptav_extra_images', string="Attribute Extra Images", readonly=False)
    wk_is_merge_config = fields.Boolean(related='website_id.wk_is_merge', string="Merge Images", readonly=False)
