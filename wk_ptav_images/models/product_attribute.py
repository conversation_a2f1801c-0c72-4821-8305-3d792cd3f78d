# -*- coding: utf-8 -*-
##############################################################################
# Copyright (c) 2015-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>)
# See LICENSE file for full copyright and licensing details.
# License URL : <https://store.webkul.com/license.html/>
##############################################################################

from odoo import models, fields, api


class ProductImage(models.Model):
    _inherit = 'product.image'

    product_variant_attribute_id = fields.Many2one('product.template.attribute.value', "Product Attribute", index=True, ondelete='cascade')

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        if res.product_variant_attribute_id:
            res.product_tmpl_id = False
        return res


class productTemplateAttrValue(models.Model):
    _inherit = "product.template.attribute.value"

    ptav_extra_image_ids = fields.One2many('product.image', 'product_variant_attribute_id', string="Attribute Extra Images")
    ptav_config = fields.Boolean(compute='_get_config_value')

    def _get_ptav_images(self, product_attr_value=False):
        is_merge = self.env['website'].get_current_website().wk_is_merge
        data = []
        if self.env['website'].get_current_website().ptav_extra_images:
            for rec in product_attr_value:
                if rec.ptav_extra_image_ids:
                    ptav_images = rec.ptav_extra_image_ids
                    data.append({rec.id: ptav_images})
                    if not is_merge:
                        return data
            return data
        return False
    
    @api.depends('ptav_extra_image_ids')
    def _get_config_value(self):
        self.ptav_config = self.env['website'].get_current_website().ptav_extra_images


class Product(models.Model):
    _inherit = "product.product"

    def _get_images(self):
        rec = super()._get_images()
        attributes = self.product_template_attribute_value_ids
        data = self.env['product.template.attribute.value']._get_ptav_images(attributes)
        if data:
            image = []
            for i in data:
                image = image + list(list(i.values())[0])
            return image
        return rec