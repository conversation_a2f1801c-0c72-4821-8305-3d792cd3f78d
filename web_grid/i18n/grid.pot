# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* grid
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~15+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-04-18 16:06+0000\n"
"PO-Revision-Date: 2017-04-18 16:06+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: grid
#. openerp-web
#: code:addons/grid/static/src/js/grid_view.js:71
#, python-format
msgid "Add a Line"
msgstr ""

#. module: grid
#: code:addons/grid/models.py:214
#: code:addons/grid/models.py:246
#, python-format
msgid "Can not use fields of type %s as grid columns"
msgstr ""

#. module: grid
#. openerp-web
#: code:addons/grid/static/src/js/grid_view.js:69
#, python-format
msgid "Grid"
msgstr ""

#. module: grid
#. openerp-web
#: code:addons/grid/static/src/js/grid_view.js:402
#, python-format
msgid "See all the records aggregated in this cell"
msgstr ""

#. module: grid
#. openerp-web
#: code:addons/grid/static/src/js/grid_view.js:206
#, python-format
msgid "The sectioned grid view can't handle groups with different columns sets"
msgstr ""

#. module: grid
#. openerp-web
#: code:addons/grid/static/src/js/grid_view.js:308
#: code:addons/grid/static/src/js/grid_view.js:318
#, python-format
msgid "Total"
msgstr ""

#. module: grid
#: model:ir.model,name:grid.model_base
msgid "base"
msgstr ""

#. module: grid
#: model:ir.model,name:grid.model_ir_actions_act_window_view
msgid "ir.actions.act_window.view"
msgstr ""

#. module: grid
#: model:ir.model,name:grid.model_ir_ui_view
msgid "ir.ui.view"
msgstr ""

